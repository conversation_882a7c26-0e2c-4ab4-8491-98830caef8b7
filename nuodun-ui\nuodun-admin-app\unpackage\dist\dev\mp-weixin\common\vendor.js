"use strict";
/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/function e(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const t=Object.freeze({}),n=Object.freeze([]),o=()=>{},r=()=>!1,i=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),s=e=>e.startsWith("onUpdate:"),a=Object.assign,c=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},l=Object.prototype.hasOwnProperty,u=(e,t)=>l.call(e,t),f=Array.isArray,p=e=>"[object Map]"===w(e),d=e=>"[object Set]"===w(e),h=e=>"function"==typeof e,g=e=>"string"==typeof e,m=e=>"symbol"==typeof e,y=e=>null!==e&&"object"==typeof e,v=e=>(y(e)||h(e))&&h(e.then)&&h(e.catch),b=Object.prototype.toString,w=e=>b.call(e),x=e=>w(e).slice(8,-1),_=e=>"[object Object]"===w(e),A=e=>g(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,S=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),k=e("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),E=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},P=/-(\w)/g,I=E((e=>e.replace(P,((e,t)=>t?t.toUpperCase():"")))),C=/\B([A-Z])/g,T=E((e=>e.replace(C,"-$1").toLowerCase())),B=E((e=>e.charAt(0).toUpperCase()+e.slice(1))),O=E((e=>e?`on${B(e)}`:"")),j=(e,t)=>!Object.is(e,t),L=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},N=e=>{const t=parseFloat(e);return isNaN(t)?e:t};function R(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=g(o)?D(o):R(o);if(r)for(const e in r)t[e]=r[e]}return t}if(g(e)||y(e))return e}const M=/;(?![^(]*\))/g,F=/:([^]+)/,U=/\/\*[^]*?\*\//g;function D(e){const t={};return e.replace(U,"").split(M).forEach((e=>{if(e){const n=e.split(F);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function q(e){let t="";if(g(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const o=q(e[n]);o&&(t+=o+" ")}else if(y(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const H=(e,t)=>t&&t.__v_isRef?H(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[z(t,o)+" =>"]=n,e)),{})}:d(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>z(e)))}:m(t)?z(t):!y(t)||f(t)||_(t)?t:String(t),z=(e,t="")=>{var n;return m(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},Q=["{","}"];const $=/^(?:\d)+/,V=/^(?:\w)+/;const W="zh-Hans",K="zh-Hant",J="en",X=Object.prototype.hasOwnProperty,G=(e,t)=>X.call(e,t),Y=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Q){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,c=$.test(t)?"list":a&&V.test(t)?"named":"unknown";o.push({value:t,type:c})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r?n.push(t[i.value]):console.warn(`Type of token '${i.type}' and format of value '${r}' don't match!`);break;case"unknown":console.warn("Detect 'unknown' type of token!")}o++}return n}(o,t)}};function Z(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return W;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?W:e.indexOf("-hant")>-1?K:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?K:W);var n;let o=[J,"fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class ee{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale=J,this.fallbackLocale=J,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||Y,this.messages=n||{},this.setLocale(e||J),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Z(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{G(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Z(t,this.messages))&&(o=this.messages[t]):n=t,G(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}const te="onShow",ne="onHide",oe="onLaunch",re="onError",ie="onThemeChange",se="onPageNotFound",ae="onUnhandledRejection",ce="onLoad",le="onReady",ue="onUnload",fe="onInit",pe="onSaveExitState",de="onResize",he="onBackPress",ge="onPageScroll",me="onTabItemTap",ye="onReachBottom",ve="onPullDownRefresh",be="onShareTimeline",we="onShareChat",xe="onAddToFavorites",_e="onShareAppMessage",Ae="onNavigationBarButtonTap",Se="onNavigationBarSearchInputClicked",ke="onNavigationBarSearchInputChanged",Ee="onNavigationBarSearchInputConfirmed",Pe="onNavigationBarSearchInputFocusChanged",Ie="virtualHostId";function Ce(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function Te(e,t){if(!g(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let o=n[0];return e||(e={}),1===n.length?e[o]:Te(e[o],n.slice(1).join("."))}function Be(e){let t={};return _(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}const Oe=/:/g;const je=encodeURIComponent;function Le(e,t=je){const n=e?Object.keys(e).map((n=>{let o=e[n];return void 0===typeof o||null===o?o="":_(o)&&(o=JSON.stringify(o)),t(n)+"="+t(o)})).filter((e=>e.length>0)).join("&"):null;return n?`?${n}`:""}const Ne=[fe,ce,te,ne,ue,he,ge,me,ye,ve,be,_e,we,xe,pe,Ae,Se,ke,Ee,Pe];const Re=[te,ne,oe,re,ie,se,ae,"onExit",fe,ce,le,ue,de,he,ge,me,ye,ve,be,xe,_e,we,pe,Ae,Se,ke,Ee,Pe],Me=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function Fe(e,t,n=!0){return!(n&&!h(t))&&(Re.indexOf(e)>-1||0===e.indexOf("on"))}let Ue;const De=[];const qe=Ce(((e,t)=>t(e))),He=function(){};He.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var ze=He;
/**
* @dcloudio/uni-mp-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Qe(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let $e,Ve;class We{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=$e,!e&&$e&&(this.index=($e.scopes||($e.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=$e;try{return $e=this,e()}finally{$e=t}}else Qe("cannot run an inactive effect scope.")}on(){$e=this}off(){$e=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}class Ke{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=$e){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,nt();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),ot()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=Ye,t=Ve;try{return Ye=!0,Ve=this,this._runnings++,Je(this),this.fn()}finally{Xe(this),this._runnings--,Ve=t,Ye=e}}stop(){var e;this.active&&(Je(this),Xe(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Je(e){e._trackId++,e._depsLength=0}function Xe(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Ge(e.deps[t],e);e.deps.length=e._depsLength}}function Ge(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let Ye=!0,Ze=0;const et=[];function nt(){et.push(Ye),Ye=!1}function ot(){const e=et.pop();Ye=void 0===e||e}function rt(){Ze++}function it(){for(Ze--;!Ze&&at.length;)at.shift()()}function st(e,t,n){var o;if(t.get(e)!==e._trackId){t.set(e,e._trackId);const r=e.deps[e._depsLength];r!==t?(r&&Ge(r,e),e.deps[e._depsLength++]=t):e._depsLength++,null==(o=e.onTrack)||o.call(e,a({effect:e},n))}}const at=[];function ct(e,t,n){var o;rt();for(const r of e.keys()){let i;r._dirtyLevel<t&&(null!=i?i:i=e.get(r)===r._trackId)&&(r._shouldSchedule||(r._shouldSchedule=0===r._dirtyLevel),r._dirtyLevel=t),r._shouldSchedule&&(null!=i?i:i=e.get(r)===r._trackId)&&(null==(o=r.onTrigger)||o.call(r,a({effect:r},n)),r.trigger(),r._runnings&&!r.allowRecurse||2===r._dirtyLevel||(r._shouldSchedule=!1,r.scheduler&&at.push(r.scheduler)))}it()}const lt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},ut=new WeakMap,ft=Symbol("iterate"),pt=Symbol("Map key iterate");function dt(e,t,n){if(Ye&&Ve){let o=ut.get(e);o||ut.set(e,o=new Map);let r=o.get(n);r||o.set(n,r=lt((()=>o.delete(n)))),st(Ve,r,{target:e,type:t,key:n})}}function ht(e,t,n,o,r,i){const s=ut.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&f(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!m(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":f(e)?A(n)&&a.push(s.get("length")):(a.push(s.get(ft)),p(e)&&a.push(s.get(pt)));break;case"delete":f(e)||(a.push(s.get(ft)),p(e)&&a.push(s.get(pt)));break;case"set":p(e)&&a.push(s.get(ft))}rt();for(const c of a)c&&ct(c,4,{target:e,type:t,key:n,newValue:o,oldValue:r,oldTarget:i});it()}const gt=e("__proto__,__v_isRef,__isVue"),mt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(m)),yt=vt();function vt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=ln(this);for(let t=0,r=this.length;t<r;t++)dt(n,"get",t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(ln)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){nt(),rt();const n=ln(this)[t].apply(this,e);return it(),ot(),n}})),e}function bt(e){const t=ln(this);return dt(t,"has",e),t.hasOwnProperty(e)}class wt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Zt:Yt:r?Gt:Xt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=f(e);if(!o){if(i&&u(yt,t))return Reflect.get(yt,t,n);if("hasOwnProperty"===t)return bt}const s=Reflect.get(e,t,n);return(m(t)?mt.has(t):gt(t))?s:(o||dt(e,"get",t),r?s:mn(s)?i&&A(t)?s:s.value:y(s)?o?tn(s):en(s):s)}}class xt extends wt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=sn(r);if(an(n)||sn(n)||(r=ln(r),n=ln(n)),!f(e)&&mn(r)&&!mn(n))return!t&&(r.value=n,!0)}const i=f(e)&&A(t)?Number(t)<e.length:u(e,t),s=Reflect.set(e,t,n,o);return e===ln(o)&&(i?j(n,r)&&ht(e,"set",t,n,r):ht(e,"add",t,n)),s}deleteProperty(e,t){const n=u(e,t),o=e[t],r=Reflect.deleteProperty(e,t);return r&&n&&ht(e,"delete",t,void 0,o),r}has(e,t){const n=Reflect.has(e,t);return m(t)&&mt.has(t)||dt(e,"has",t),n}ownKeys(e){return dt(e,"iterate",f(e)?"length":ft),Reflect.ownKeys(e)}}class _t extends wt{constructor(e=!1){super(!0,e)}set(e,t){return Qe(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0}deleteProperty(e,t){return Qe(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}}const At=new xt,St=new _t,kt=new xt(!0),Et=new _t(!0),Pt=e=>e,It=e=>Reflect.getPrototypeOf(e);function Ct(e,t,n=!1,o=!1){const r=ln(e=e.__v_raw),i=ln(t);n||(j(t,i)&&dt(r,"get",t),dt(r,"get",i));const{has:s}=It(r),a=o?Pt:n?pn:fn;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function Tt(e,t=!1){const n=this.__v_raw,o=ln(n),r=ln(e);return t||(j(e,r)&&dt(o,"has",e),dt(o,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function Bt(e,t=!1){return e=e.__v_raw,!t&&dt(ln(e),"iterate",ft),Reflect.get(e,"size",e)}function Ot(e){e=ln(e);const t=ln(this);return It(t).has.call(t,e)||(t.add(e),ht(t,"add",e,e)),this}function jt(e,t){t=ln(t);const n=ln(this),{has:o,get:r}=It(n);let i=o.call(n,e);i?Jt(n,o,e):(e=ln(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?j(t,s)&&ht(n,"set",e,t,s):ht(n,"add",e,t),this}function Lt(e){const t=ln(this),{has:n,get:o}=It(t);let r=n.call(t,e);r?Jt(t,n,e):(e=ln(e),r=n.call(t,e));const i=o?o.call(t,e):void 0,s=t.delete(e);return r&&ht(t,"delete",e,void 0,i),s}function Nt(){const e=ln(this),t=0!==e.size,n=p(e)?new Map(e):new Set(e),o=e.clear();return t&&ht(e,"clear",void 0,void 0,n),o}function Rt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=ln(i),a=t?Pt:e?pn:fn;return!e&&dt(s,"iterate",ft),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function Mt(e,t,n){return function(...o){const r=this.__v_raw,i=ln(r),s=p(i),a="entries"===e||e===Symbol.iterator&&s,c="keys"===e&&s,l=r[e](...o),u=n?Pt:t?pn:fn;return!t&&dt(i,"iterate",c?pt:ft),{next(){const{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ft(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";Qe(`${B(e)} operation ${n}failed: target is readonly.`,ln(this))}return"delete"!==e&&("clear"===e?void 0:this)}}function Ut(){const e={get(e){return Ct(this,e)},get size(){return Bt(this)},has:Tt,add:Ot,set:jt,delete:Lt,clear:Nt,forEach:Rt(!1,!1)},t={get(e){return Ct(this,e,!1,!0)},get size(){return Bt(this)},has:Tt,add:Ot,set:jt,delete:Lt,clear:Nt,forEach:Rt(!1,!0)},n={get(e){return Ct(this,e,!0)},get size(){return Bt(this,!0)},has(e){return Tt.call(this,e,!0)},add:Ft("add"),set:Ft("set"),delete:Ft("delete"),clear:Ft("clear"),forEach:Rt(!0,!1)},o={get(e){return Ct(this,e,!0,!0)},get size(){return Bt(this,!0)},has(e){return Tt.call(this,e,!0)},add:Ft("add"),set:Ft("set"),delete:Ft("delete"),clear:Ft("clear"),forEach:Rt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Mt(r,!1,!1),n[r]=Mt(r,!0,!1),t[r]=Mt(r,!1,!0),o[r]=Mt(r,!0,!0)})),[e,n,t,o]}const[Dt,qt,Ht,zt]=Ut();function Qt(e,t){const n=t?e?zt:Ht:e?qt:Dt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(u(n,o)&&o in t?n:t,o,r)}const $t={get:Qt(!1,!1)},Vt={get:Qt(!1,!0)},Wt={get:Qt(!0,!1)},Kt={get:Qt(!0,!0)};function Jt(e,t,n){const o=ln(n);if(o!==n&&t.call(e,o)){const t=x(e);Qe(`Reactive ${t} contains both the raw and reactive versions of the same object${"Map"===t?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const Xt=new WeakMap,Gt=new WeakMap,Yt=new WeakMap,Zt=new WeakMap;function en(e){return sn(e)?e:on(e,!1,At,$t,Xt)}function tn(e){return on(e,!0,St,Wt,Yt)}function nn(e){return on(e,!0,Et,Kt,Zt)}function on(e,t,n,o,r){if(!y(e))return Qe(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=(a=e).__v_skip||!Object.isExtensible(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(x(a));var a;if(0===s)return e;const c=new Proxy(e,2===s?o:n);return r.set(e,c),c}function rn(e){return sn(e)?rn(e.__v_raw):!(!e||!e.__v_isReactive)}function sn(e){return!(!e||!e.__v_isReadonly)}function an(e){return!(!e||!e.__v_isShallow)}function cn(e){return rn(e)||sn(e)}function ln(e){const t=e&&e.__v_raw;return t?ln(t):e}function un(e){return Object.isExtensible(e)&&((e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})})(e,"__v_skip",!0),e}const fn=e=>y(e)?en(e):e,pn=e=>y(e)?tn(e):e;class dn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ke((()=>e(this._value)),(()=>gn(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=ln(this);return e._cacheable&&!e.effect.dirty||!j(e._value,e._value=e.effect.run())||gn(e,4),hn(e),e.effect._dirtyLevel>=2&&(this._warnRecursive&&Qe("Computed is still dirty after getter evaluation, likely because a computed is mutating its own dependency in its getter. State mutations in computed getters should be avoided.  Check the docs for more details: https://vuejs.org/guide/essentials/computed.html#getters-should-be-side-effect-free","\n\ngetter: ",this.getter),gn(e,2)),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function hn(e){var t;Ye&&Ve&&(e=ln(e),st(Ve,null!=(t=e.dep)?t:e.dep=lt((()=>e.dep=void 0),e instanceof dn?e:void 0),{target:e,type:"get",key:"value"}))}function gn(e,t=4,n){const o=(e=ln(e)).dep;o&&ct(o,t,{target:e,type:"set",key:"value",newValue:n})}function mn(e){return!(!e||!0!==e.__v_isRef)}function yn(e){return function(e,t){if(mn(e))return e;return new vn(e,t)}(e,!1)}class vn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:ln(e),this._value=t?e:fn(e)}get value(){return hn(this),this._value}set value(e){const t=this.__v_isShallow||an(e)||sn(e);e=t?e:ln(e),j(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:fn(e),gn(this,4,e))}}function bn(e){return mn(e)?e.value:e}const wn={get:(e,t,n)=>bn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return mn(r)&&!mn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function xn(e){return rn(e)?e:new Proxy(e,wn)}class _n{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=ln(this._object),t=this._key,null==(n=ut.get(e))?void 0:n.get(t);var e,t,n}}function An(e,t,n){const o=e[t];return mn(o)?o:new _n(e,t,n)}const Sn=[];function kn(e){Sn.push(e)}function En(){Sn.pop()}function Pn(e,...t){nt();const n=Sn.length?Sn[Sn.length-1].component:null,o=n&&n.appContext.config.warnHandler,r=function(){let e=Sn[Sn.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}();if(o)Bn(o,n,11,[e+t.map((e=>{var t,n;return null!=(n=null==(t=e.toString)?void 0:t.call(e))?n:JSON.stringify(e)})).join(""),n&&n.proxy,r.map((({vnode:e})=>`at <${ei(n,e.type)}>`)).join("\n"),r]);else{const n=[`[Vue warn]: ${e}`,...t];r.length&&n.push("\n",...function(e){const t=[];return e.forEach(((e,n)=>{t.push(...0===n?[]:["\n"],...function({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=!!e.component&&null==e.component.parent,r=` at <${ei(e.component,e.type,o)}`,i=">"+n;return e.props?[r,...In(e.props),i]:[r+i]}(e))})),t}(r)),console.warn(...n)}ot()}function In(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach((n=>{t.push(...Cn(n,e[n]))})),n.length>3&&t.push(" ..."),t}function Cn(e,t,n){return g(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):"number"==typeof t||"boolean"==typeof t||null==t?n?t:[`${e}=${t}`]:mn(t)?(t=Cn(e,ln(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):h(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=ln(t),n?t:[`${e}=`,t])}const Tn={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://github.com/vuejs/core ."};function Bn(e,t,n,o){try{return o?e(...o):e()}catch(r){jn(r,t,n)}}function On(e,t,n,o){if(h(e)){const r=Bn(e,t,n,o);return r&&v(r)&&r.catch((e=>{jn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(On(e[i],t,n,o));return r}function jn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=Tn[n]||n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void Bn(s,null,10,[e,r,i])}Ln(e,n,r,o)}function Ln(e,t,n,o=!0){{const o=Tn[t]||t;n&&kn(n),Pn("Unhandled error"+(o?` during execution of ${o}`:"")),n&&En(),console.error(e)}}let Nn=!1,Rn=!1;const Mn=[];let Fn=0;const Un=[];let Dn=null,qn=0;const Hn=Promise.resolve();let zn=null;function Qn(e){const t=zn||Hn;return e?t.then(this?e.bind(this):e):t}function $n(e){Mn.length&&Mn.includes(e,Nn&&e.allowRecurse?Fn+1:Fn)||(null==e.id?Mn.push(e):Mn.splice(function(e){let t=Fn+1,n=Mn.length;for(;t<n;){const o=t+n>>>1,r=Mn[o],i=Jn(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),Vn())}function Vn(){Nn||Rn||(Rn=!0,zn=Hn.then(Gn))}function Wn(e){f(e)?Un.push(...e):Dn&&Dn.includes(e,e.allowRecurse?qn+1:qn)||Un.push(e),Vn()}function Kn(e,t,n=(Nn?Fn+1:0)){for(t=t||new Map;n<Mn.length;n++){const e=Mn[n];if(e&&e.pre){if(Yn(t,e))continue;Mn.splice(n,1),n--,e()}}}const Jn=e=>null==e.id?1/0:e.id,Xn=(e,t)=>{const n=Jn(e)-Jn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Gn(e){Rn=!1,Nn=!0,e=e||new Map,Mn.sort(Xn);const t=t=>Yn(e,t);try{for(Fn=0;Fn<Mn.length;Fn++){const e=Mn[Fn];if(e&&!1!==e.active){if(t(e))continue;Bn(e,null,14)}}}finally{Fn=0,Mn.length=0,function(e){if(Un.length){const t=[...new Set(Un)].sort(((e,t)=>Jn(e)-Jn(t)));if(Un.length=0,Dn)return void Dn.push(...t);for(Dn=t,e=e||new Map,qn=0;qn<Dn.length;qn++)Yn(e,Dn[qn])||Dn[qn]();Dn=null,qn=0}}(e),Nn=!1,zn=null,(Mn.length||Un.length)&&Gn(e)}}function Yn(e,t){if(e.has(t)){const n=e.get(t);if(n>100){const e=t.ownerInstance,n=e&&Zr(e.type);return jn(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}e.set(t,n+1)}else e.set(t,1)}let Zn,eo=[],to=!1;function no(e,...t){Zn?Zn.emit(e,...t):to||eo.push({event:e,args:t})}function oo(e,t){var n,o;if(Zn=e,Zn)Zn.enabled=!0,eo.forEach((({event:e,args:t})=>Zn.emit(e,...t))),eo=[];else if("undefined"!=typeof window&&window.HTMLElement&&!(null==(o=null==(n=window.navigator)?void 0:n.userAgent)?void 0:o.includes("jsdom"))){(t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push((e=>{oo(e,t)})),setTimeout((()=>{Zn||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,to=!0,eo=[])}),3e3)}else to=!0,eo=[]}const ro=ao("component:added"),io=ao("component:updated"),so=ao("component:removed");
/*! #__NO_SIDE_EFFECTS__ */
function ao(e){return t=>{no(e,t.appContext.app,t.uid,0===t.uid?void 0:t.parent?t.parent.uid:0,t)}}const co=uo("perf:start"),lo=uo("perf:end");function uo(e){return(t,n,o)=>{no(e,t.appContext.app,t.uid,t,n,o)}}function fo(e,n,...o){if(e.isUnmounted)return;const r=e.vnode.props||t;{const{emitsOptions:t,propsOptions:[r]}=e;if(t)if(n in t){const e=t[n];if(h(e)){e(...o)||Pn(`Invalid event arguments: event validation failed for event "${n}".`)}}else r&&O(n)in r||Pn(`Component emitted event "${n}" but it is neither declared in the emits option nor as an "${O(n)}" prop.`)}let i=o;const s=n.startsWith("update:"),a=s&&n.slice(7);if(a&&a in r){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:n,trim:s}=r[e]||t;s&&(i=o.map((e=>g(e)?e.trim():e))),n&&(i=o.map(N))}!function(e,t,n){no("component:emit",e.appContext.app,e,t,n)}(e,n,i);{const t=n.toLowerCase();t!==n&&r[O(t)]&&Pn(`Event "${t}" is emitted in component ${ei(e,e.type)} but the handler is registered for "${n}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${T(n)}" instead of "${n}".`)}let c,l=r[c=O(n)]||r[c=O(I(n))];!l&&s&&(l=r[c=O(T(n))]),l&&On(l,e,6,i);const u=r[c+"Once"];if(u){if(e.emitted){if(e.emitted[c])return}else e.emitted={};e.emitted[c]=!0,On(u,e,6,i)}}function po(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},c=!1;if(!h(e)){const o=e=>{const n=po(e,t,!0);n&&(c=!0,a(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||c?(f(i)?i.forEach((e=>s[e]=null)):a(s,i),y(e)&&o.set(e,s),s):(y(e)&&o.set(e,null),null)}function ho(e,t){return!(!e||!i(t))&&(t=t.slice(2).replace(/Once$/,""),u(e,t[0].toLowerCase()+t.slice(1))||u(e,T(t))||u(e,t))}let go=null;function mo(e){const t=go;return go=e,e&&e.type.__scopeId,t}const yo="components";function vo(e,t){return e&&(e[t]||e[I(t)]||e[B(I(t))])}const bo={};function wo(e,t,n){return h(t)||Pn("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),xo(e,t,n)}function xo(e,n,{immediate:r,deep:i,flush:s,once:a,onTrack:l,onTrigger:u}=t){if(n&&a){const e=n;n=(...t)=>{e(...t),E()}}void 0!==i&&"number"==typeof i&&Pn('watch() "deep" option with number value will be used as watch depth in future versions. Please use a boolean instead to avoid potential breakage.'),n||(void 0!==r&&Pn('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),void 0!==i&&Pn('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),void 0!==a&&Pn('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const p=e=>{Pn("Invalid watch source: ",e,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},d=Mr,g=e=>!0===i?e:So(e,!1===i?1:void 0);let m,y,v=!1,b=!1;if(mn(e)?(m=()=>e.value,v=an(e)):rn(e)?(m=()=>g(e),v=!0):f(e)?(b=!0,v=e.some((e=>rn(e)||an(e))),m=()=>e.map((e=>mn(e)?e.value:rn(e)?g(e):h(e)?Bn(e,d,2):void p(e)))):h(e)?m=n?()=>Bn(e,d,2):()=>(y&&y(),On(e,d,3,[w])):(m=o,p(e)),n&&i){const e=m;m=()=>So(e())}let w=e=>{y=S.onStop=()=>{Bn(e,d,4),y=S.onStop=void 0}},x=b?new Array(e.length).fill(bo):bo;const _=()=>{if(S.active&&S.dirty)if(n){const e=S.run();(i||v||(b?e.some(((e,t)=>j(e,x[t]))):j(e,x)))&&(y&&y(),On(n,d,3,[e,x===bo?void 0:b&&x[0]===bo?[]:x,w]),x=e)}else S.run()};let A;_.allowRecurse=!!n,"sync"===s?A=_:"post"===s?A=()=>Cr(_,d&&d.suspense):(_.pre=!0,d&&(_.id=d.uid),A=()=>$n(_));const S=new Ke(m,o,A),k=$e,E=()=>{S.stop(),k&&c(k.effects,S)};return S.onTrack=l,S.onTrigger=u,n?r?_():x=S.run():"post"===s?Cr(S.run.bind(S),d&&d.suspense):S.run(),E}function _o(e,t,n){const o=this.proxy,r=g(e)?e.includes(".")?Ao(o,e):()=>o[e]:e.bind(o,o);let i;h(t)?i=t:(i=t.handler,n=t);const s=qr(this),a=xo(r,i.bind(o),n);return s(),a}function Ao(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function So(e,t,n=0,o){if(!y(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),mn(e))So(e.value,t,n,o);else if(f(e))for(let r=0;r<e.length;r++)So(e[r],t,n,o);else if(d(e)||p(e))e.forEach((e=>{So(e,t,n,o)}));else if(_(e))for(const r in e)So(e[r],t,n,o);return e}function ko(e){k(e)&&Pn("Do not use built-in directive ids as custom directive id: "+e)}function Eo(){return{app:null,config:{isNativeTag:r,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Po=0;let Io=null;function Co(e,t,n=!1){const o=Mr||go;if(o||Io){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:Io._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&h(t)?t.call(o&&o.proxy):t;Pn(`injection "${String(e)}" not found.`)}else Pn("inject() can only be used inside setup() or functional components.")}function To(e,t){Oo(e,"a",t)}function Bo(e,t){Oo(e,"da",t)}function Oo(e,t,n=Mr){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(Lo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)e.parent.vnode.type.__isKeepAlive&&jo(o,t,n,e),e=e.parent}}function jo(e,t,n,o){const r=Lo(t,e,o,!0);qo((()=>{c(o[t],r)}),n)}function Lo(e,t,n=Mr,o=!1){if(n){r=e,Ne.indexOf(r)>-1&&(n=n.root);const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;nt();const r=qr(n),i=On(t,n,e,o);return r(),ot(),i});return o?i.unshift(s):i.push(s),s}Pn(`${O((Tn[e]||e.replace(/^on/,"")).replace(/ hook$/,""))} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup().`);var r}const No=e=>(t,n=Mr)=>(!Vr||"sp"===e)&&Lo(e,((...e)=>t(...e)),n),Ro=No("bm"),Mo=No("m"),Fo=No("bu"),Uo=No("u"),Do=No("bum"),qo=No("um"),Ho=No("sp"),zo=No("rtg"),Qo=No("rtc");function $o(e,t=Mr){Lo("ec",e,t)}const Vo=e=>e?$r(e)?Xr(e)||e.proxy:Vo(e.parent):null,Wo=a(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>nn(e.props),$attrs:e=>nn(e.attrs),$slots:e=>nn(e.slots),$refs:e=>nn(e.refs),$parent:e=>Vo(e.parent),$root:e=>Vo(e.root),$emit:e=>e.emit,$options:e=>nr(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,$n(e.update)}),$watch:e=>_o.bind(e)}),Ko=e=>"_"===e||"$"===e,Jo=(e,n)=>e!==t&&!e.__isScriptSetup&&u(e,n),Xo={get({_:e},n){const{ctx:o,setupState:r,data:i,props:s,accessCache:a,type:c,appContext:l}=e;if("__isVue"===n)return!0;let f;if("$"!==n[0]){const c=a[n];if(void 0!==c)switch(c){case 1:return r[n];case 2:return i[n];case 4:return o[n];case 3:return s[n]}else{if(Jo(r,n))return a[n]=1,r[n];if(i!==t&&u(i,n))return a[n]=2,i[n];if((f=e.propsOptions[0])&&u(f,n))return a[n]=3,s[n];if(o!==t&&u(o,n))return a[n]=4,o[n];Yo&&(a[n]=0)}}const p=Wo[n];let d,h;return p?(("$attrs"===n||"$slots"===n)&&dt(e,"get",n),p(e)):(d=c.__cssModules)&&(d=d[n])?d:o!==t&&u(o,n)?(a[n]=4,o[n]):(h=l.config.globalProperties,u(h,n)?h[n]:void(!go||g(n)&&0===n.indexOf("__v")||(i!==t&&Ko(n[0])&&u(i,n)?Pn(`Property ${JSON.stringify(n)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===go&&Pn(`Property ${JSON.stringify(n)} was accessed during render but is not defined on instance.`))))},set({_:e},n,o){const{data:r,setupState:i,ctx:s}=e;return Jo(i,n)?(i[n]=o,!0):i.__isScriptSetup&&u(i,n)?(Pn(`Cannot mutate <script setup> binding "${n}" from Options API.`),!1):r!==t&&u(r,n)?(r[n]=o,!0):u(e.props,n)?(Pn(`Attempting to mutate prop "${n}". Props are readonly.`),!1):"$"===n[0]&&n.slice(1)in e?(Pn(`Attempting to mutate public property "${n}". Properties starting with $ are reserved and readonly.`),!1):(n in e.appContext.config.globalProperties?Object.defineProperty(s,n,{enumerable:!0,configurable:!0,value:o}):s[n]=o,!0)},has({_:{data:e,setupState:n,accessCache:o,ctx:r,appContext:i,propsOptions:s}},a){let c;return!!o[a]||e!==t&&u(e,a)||Jo(n,a)||(c=s[0])&&u(c,a)||u(r,a)||u(Wo,a)||u(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:u(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Go(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}Xo.ownKeys=e=>(Pn("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e));let Yo=!0;function Zo(e){const t=nr(e),n=e.proxy,r=e.ctx;Yo=!1,t.beforeCreate&&er(t.beforeCreate,e,"bc");const{data:i,computed:s,methods:a,watch:c,provide:l,inject:u,created:p,beforeMount:d,mounted:g,beforeUpdate:m,updated:b,activated:w,deactivated:x,beforeDestroy:_,beforeUnmount:A,destroyed:S,unmounted:k,render:E,renderTracked:P,renderTriggered:I,errorCaptured:C,serverPrefetch:T,expose:B,inheritAttrs:O,components:j,directives:L,filters:N}=t,R=function(){const e=Object.create(null);return(t,n)=>{e[n]?Pn(`${t} property "${n}" is already defined in ${e[n]}.`):e[n]=t}}();{const[t]=e.propsOptions;if(t)for(const e in t)R("Props",e)}if(u&&function(e,t,n=o){f(e)&&(e=sr(e));for(const o in e){const r=e[o];let i;i=y(r)?"default"in r?Co(r.from||o,r.default,!0):Co(r.from||o):Co(r),mn(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[o]=i,n("Inject",o)}}(u,r,R),a)for(const o in a){const e=a[o];h(e)?(Object.defineProperty(r,o,{value:e.bind(n),configurable:!0,enumerable:!0,writable:!0}),R("Methods",o)):Pn(`Method "${o}" has type "${typeof e}" in the component definition. Did you reference the function correctly?`)}if(i){h(i)||Pn("The data option must be a function. Plain object usage is no longer supported.");const t=i.call(n,n);if(v(t)&&Pn("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),y(t)){e.data=en(t);for(const e in t)R("Data",e),Ko(e[0])||Object.defineProperty(r,e,{configurable:!0,enumerable:!0,get:()=>t[e],set:o})}else Pn("data() should return an object.")}if(Yo=!0,s)for(const f in s){const e=s[f],t=h(e)?e.bind(n,n):h(e.get)?e.get.bind(n,n):o;t===o&&Pn(`Computed property "${f}" has no getter.`);const i=!h(e)&&h(e.set)?e.set.bind(n):()=>{Pn(`Write operation failed: computed property "${f}" is readonly.`)},a=ti({get:t,set:i});Object.defineProperty(r,f,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e}),R("Computed",f)}if(c)for(const o in c)tr(c[o],r,n,o);function M(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(function(){if(l){const e=h(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(Mr){let n=Mr.provides;const o=Mr.parent&&Mr.parent.provides;o===n&&(n=Mr.provides=Object.create(o)),n[e]=t,"app"===Mr.type.mpType&&Mr.appContext.app.provide(e,t)}else Pn("provide() can only be used inside setup().")}(t,e[t])}))}}(),p&&er(p,e,"c"),M(Ro,d),M(Mo,g),M(Fo,m),M(Uo,b),M(To,w),M(Bo,x),M($o,C),M(Qo,P),M(zo,I),M(Do,A),M(qo,k),M(Ho,T),f(B))if(B.length){const t=e.exposed||(e.exposed={});B.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});E&&e.render===o&&(e.render=E),null!=O&&(e.inheritAttrs=O),j&&(e.components=j),L&&(e.directives=L),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function er(e,t,n){On(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function tr(e,t,n,o){const r=o.includes(".")?Ao(n,o):()=>n[o];if(g(e)){const n=t[e];h(n)?wo(r,n):Pn(`Invalid watch handler specified by key "${e}"`,n)}else if(h(e))wo(r,e.bind(n));else if(y(e))if(f(e))e.forEach((e=>tr(e,t,n,o)));else{const o=h(e.handler)?e.handler.bind(n):t[e.handler];h(o)?wo(r,o,e):Pn(`Invalid watch handler specified by key "${e.handler}"`,o)}else Pn(`Invalid watch option: "${o}"`,e)}function nr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let c;return a?c=a:r.length||n||o?(c={},r.length&&r.forEach((e=>or(c,e,s,!0))),or(c,t,s)):c=t,y(t)&&i.set(t,c),c}function or(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&or(e,i,n,!0),r&&r.forEach((t=>or(e,t,n,!0)));for(const s in t)if(o&&"expose"===s)Pn('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const o=rr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const rr={data:ir,props:lr,emits:lr,methods:cr,computed:cr,beforeCreate:ar,created:ar,beforeMount:ar,mounted:ar,beforeUpdate:ar,updated:ar,beforeDestroy:ar,beforeUnmount:ar,destroyed:ar,unmounted:ar,activated:ar,deactivated:ar,errorCaptured:ar,serverPrefetch:ar,components:cr,directives:cr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=a(Object.create(null),e);for(const o in t)n[o]=ar(e[o],t[o]);return n},provide:ir,inject:function(e,t){return cr(sr(e),sr(t))}};function ir(e,t){return t?e?function(){return a(h(e)?e.call(this,this):e,h(t)?t.call(this,this):t)}:t:e}function sr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ar(e,t){return e?[...new Set([].concat(e,t))]:t}function cr(e,t){return e?a(Object.create(null),e,t):t}function lr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:a(Object.create(null),Go(e),Go(null!=t?t:{})):t}function ur(e,t,n,o=!1){const r={},i={};e.propsDefaults=Object.create(null),fr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);vr(t||{},r,e),n?e.props=o?r:on(r,!1,kt,Vt,Gt):e.type.props?e.props=r:e.props=i,e.attrs=i}function fr(e,n,o,r){const[i,s]=e.propsOptions;let a,c=!1;if(n)for(let t in n){if(S(t))continue;const l=n[t];let f;i&&u(i,f=I(t))?s&&s.includes(f)?(a||(a={}))[f]=l:o[f]=l:ho(e.emitsOptions,t)||t in r&&l===r[t]||(r[t]=l,c=!0)}if(s){const n=ln(o),r=a||t;for(let t=0;t<s.length;t++){const a=s[t];o[a]=pr(i,n,a,r[a],e,!u(r,a))}}return c}function pr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=u(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&h(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=qr(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==T(n)||(o=!0))}return o}function dr(e,o,r=!1){const i=o.propsCache,s=i.get(e);if(s)return s;const c=e.props,l={},p=[];let d=!1;if(!h(e)){const t=e=>{d=!0;const[t,n]=dr(e,o,!0);a(l,t),n&&p.push(...n)};!r&&o.mixins.length&&o.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!c&&!d)return y(e)&&i.set(e,n),n;if(f(c))for(let n=0;n<c.length;n++){g(c[n])||Pn("props must be strings when using array syntax.",c[n]);const e=I(c[n]);hr(e)&&(l[e]=t)}else if(c){y(c)||Pn("invalid props options",c);for(const e in c){const t=I(e);if(hr(t)){const n=c[e],o=l[t]=f(n)||h(n)?{type:n}:a({},n);if(o){const e=yr(Boolean,o.type),n=yr(String,o.type);o[0]=e>-1,o[1]=n<0||e<n,(e>-1||u(o,"default"))&&p.push(t)}}}}const m=[l,p];return y(e)&&i.set(e,m),m}function hr(e){return"$"!==e[0]&&!S(e)||(Pn(`Invalid prop name: "${e}" is a reserved property.`),!1)}function gr(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function mr(e,t){return gr(e)===gr(t)}function yr(e,t){return f(t)?t.findIndex((t=>mr(t,e))):h(t)&&mr(t,e)?0:-1}function vr(e,t,n){const o=ln(t),r=n.propsOptions[0];for(const i in r){let t=r[i];null!=t&&br(i,o[i],t,nn(o),!u(e,i)&&!u(e,T(i)))}}function br(e,t,n,o,r){const{type:i,required:s,validator:a,skipCheck:c}=n;if(s&&r)Pn('Missing required prop: "'+e+'"');else if(null!=t||s){if(null!=i&&!0!==i&&!c){let n=!1;const o=f(i)?i:[i],r=[];for(let e=0;e<o.length&&!n;e++){const{valid:i,expectedType:s}=xr(t,o[e]);r.push(s||""),n=i}if(!n)return void Pn(function(e,t,n){if(0===n.length)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let o=`Invalid prop: type check failed for prop "${e}". Expected ${n.map(B).join(" | ")}`;const r=n[0],i=x(t),s=_r(t,r),a=_r(t,i);1===n.length&&Ar(r)&&!function(...e){return e.some((e=>"boolean"===e.toLowerCase()))}(r,i)&&(o+=` with value ${s}`);o+=`, got ${i} `,Ar(i)&&(o+=`with value ${a}.`);return o}(e,t,r))}a&&!a(t,o)&&Pn('Invalid prop: custom validator check failed for prop "'+e+'".')}}const wr=e("String,Number,Boolean,Function,Symbol,BigInt");function xr(e,t){let n;const o=gr(t);if(wr(o)){const r=typeof e;n=r===o.toLowerCase(),n||"object"!==r||(n=e instanceof t)}else n="Object"===o?y(e):"Array"===o?f(e):"null"===o?null===e:e instanceof t;return{valid:n,expectedType:o}}function _r(e,t){return"String"===t?`"${e}"`:"Number"===t?`${Number(e)}`:`${e}`}function Ar(e){return["string","number","boolean"].some((t=>e.toLowerCase()===t))}let Sr,kr;function Er(e,t){e.appContext.config.performance&&Ir()&&kr.mark(`vue-${t}-${e.uid}`),co(e,t,Ir()?kr.now():Date.now())}function Pr(e,t){if(e.appContext.config.performance&&Ir()){const n=`vue-${t}-${e.uid}`,o=n+":end";kr.mark(o),kr.measure(`<${ei(e,e.type)}> ${t}`,n,o),kr.clearMarks(n),kr.clearMarks(o)}lo(e,t,Ir()?kr.now():Date.now())}function Ir(){return void 0!==Sr||("undefined"!=typeof window&&window.performance?(Sr=!0,kr=window.performance):Sr=!1),Sr}const Cr=Wn,Tr=Symbol.for("v-fgt"),Br=Symbol.for("v-txt"),Or=Symbol.for("v-cmt"),jr=Symbol.for("v-stc");const Lr=Eo();let Nr=0;function Rr(e,n,r){const i=e.type,s=(n?n.appContext:e.appContext)||Lr,a={uid:Nr++,vnode:e,type:i,parent:n,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new We(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:dr(i,s),emitsOptions:po(i,s),emit:null,emitted:null,propsDefaults:t,inheritAttrs:i.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null,$uniElements:new Map,$templateUniElementRefs:[],$templateUniElementStyles:{},$eS:{},$eA:{}};return a.ctx=function(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(Wo).forEach((n=>{Object.defineProperty(t,n,{configurable:!0,enumerable:!1,get:()=>Wo[n](e),set:o})})),t}(a),a.root=n?n.root:a,a.emit=fo.bind(null,a),e.ce&&e.ce(a),a}let Mr=null;const Fr=()=>Mr||go;let Ur,Dr;Ur=e=>{Mr=e},Dr=e=>{Vr=e};const qr=e=>{const t=Mr;return Ur(e),e.scope.on(),()=>{e.scope.off(),Ur(t)}},Hr=()=>{Mr&&Mr.scope.off(),Ur(null)},zr=e("slot,component");function Qr(e,{isNativeTag:t}){(zr(e)||t(e))&&Pn("Do not use built-in or reserved HTML elements as component id: "+e)}function $r(e){return 4&e.vnode.shapeFlag}let Vr=!1;function Wr(e,t=!1){t&&Dr(t);const{props:n}=e.vnode,r=$r(e);ur(e,n,r,t);const i=r?function(e,t){const n=e.type;n.name&&Qr(n.name,e.appContext.config);if(n.components){const t=Object.keys(n.components);for(let n=0;n<t.length;n++)Qr(t[n],e.appContext.config)}if(n.directives){const e=Object.keys(n.directives);for(let t=0;t<e.length;t++)ko(e[t])}n.compilerOptions&&Kr()&&Pn('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.');e.accessCache=Object.create(null),e.proxy=un(new Proxy(e.ctx,Xo)),function(e){const{ctx:t,propsOptions:[n]}=e;n&&Object.keys(n).forEach((n=>{Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>e.props[n],set:o})}))}(e);const{setup:r}=n;if(r){const n=e.setupContext=r.length>1?function(e){const t=t=>{if(e.exposed&&Pn("expose() should be called only once per setup()."),null!=t){let e=typeof t;"object"===e&&(f(t)?e="array":mn(t)&&(e="ref")),"object"!==e&&Pn(`expose() should be passed a plain object, received ${e}.`)}e.exposed=t||{}};return Object.freeze({get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(dt(e,"get","$attrs"),t[n]),set:()=>(Pn("setupContext.attrs is readonly."),!1),deleteProperty:()=>(Pn("setupContext.attrs is readonly."),!1)}))}(e)},get slots(){return function(e){return e.slotsProxy||(e.slotsProxy=new Proxy(e.slots,{get:(t,n)=>(dt(e,"get","$slots"),t[n])}))}(e)},get emit(){return(t,...n)=>e.emit(t,...n)},expose:t})}(e):null,i=qr(e);nt();const s=Bn(r,e,0,[nn(e.props),n]);ot(),i(),v(s)?(s.then(Hr,Hr),Pn("setup() returned a Promise, but the version of Vue you are using does not support it yet.")):function(e,t,n){h(t)?e.render=t:y(t)?((r=t)&&!0===r.__v_isVNode&&Pn("setup() should not return VNodes directly - return a render function instead."),e.devtoolsRawSetupState=t,e.setupState=xn(t),function(e){const{ctx:t,setupState:n}=e;Object.keys(ln(n)).forEach((e=>{if(!n.__isScriptSetup){if(Ko(e[0]))return void Pn(`setup() return property ${JSON.stringify(e)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);Object.defineProperty(t,e,{enumerable:!0,configurable:!0,get:()=>n[e],set:o})}}))}(e)):void 0!==t&&Pn("setup() should return an object. Received: "+(null===t?"null":typeof t));var r;Jr(e,n)}(e,s,t)}else Jr(e,t)}(e,t):void 0;return t&&Dr(!1),i}const Kr=()=>!0;function Jr(e,t,n){const r=e.type;e.render||(e.render=r.render||o);{const t=qr(e);nt();try{Zo(e)}finally{ot(),t()}}r.render||e.render!==o||t||(r.template?Pn('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Pn("Component is missing template or render function."))}function Xr(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(xn(un(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in Wo}))}const Gr=/(?:^|[-_])(\w)/g,Yr=e=>e.replace(Gr,(e=>e.toUpperCase())).replace(/[-_]/g,"");function Zr(e,t=!0){return h(e)?e.displayName||e.name:e.name||t&&e.__name}function ei(e,t,n=!1){let o=Zr(t);if(!o&&t.__file){const e=t.__file.match(/([^/\\]+)\.\w+$/);e&&(o=e[1])}if(!o&&e&&e.parent){const n=e=>{for(const n in e)if(e[n]===t)return n};o=n(e.components||e.parent.type.components)||n(e.appContext.components)}return o?Yr(o):n?"App":"Anonymous"}const ti=(e,t)=>{const n=function(e,t,n=!1){let o,r;const i=h(e);i?(o=e,r=()=>{Qe("Write operation failed: computed value is readonly")}):(o=e.get,r=e.set);const s=new dn(o,r,i||!r,n);return t&&!n&&(s.effect.onTrack=t.onTrack,s.effect.onTrigger=t.onTrigger),s}(e,t,Vr);{const e=Fr();e&&e.appContext.config.warnRecursiveComputed&&(n._warnRecursive=!0)}return n},ni="3.4.21",oi=Pn;function ri(e){return bn(e)}const ii="[object Array]",si="[object Object]";function ai(e,t){const n={};return ci(e,t),li(e,t,"",n),n}function ci(e,t){if((e=ri(e))===t)return;const n=w(e),o=w(t);if(n==si&&o==si)for(let r in t){const n=e[r];void 0===n?e[r]=null:ci(n,t[r])}else n==ii&&o==ii&&e.length>=t.length&&t.forEach(((t,n)=>{ci(e[n],t)}))}function li(e,t,n,o){if((e=ri(e))===t)return;const r=w(e),i=w(t);if(r==si)if(i!=si||Object.keys(e).length<Object.keys(t).length)ui(o,n,e);else for(let s in e){const r=ri(e[s]),i=t[s],a=w(r),c=w(i);if(a!=ii&&a!=si)r!=i&&ui(o,(""==n?"":n+".")+s,r);else if(a==ii)c!=ii||r.length<i.length?ui(o,(""==n?"":n+".")+s,r):r.forEach(((e,t)=>{li(e,i[t],(""==n?"":n+".")+s+"["+t+"]",o)}));else if(a==si)if(c!=si||Object.keys(r).length<Object.keys(i).length)ui(o,(""==n?"":n+".")+s,r);else for(let e in r)li(r[e],i[e],(""==n?"":n+".")+s+"."+e,o)}else r==ii?i!=ii||e.length<t.length?ui(o,n,e):e.forEach(((e,r)=>{li(e,t[r],n+"["+r+"]",o)})):ui(o,n,e)}function ui(e,t,n){e[t]=n}function fi(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function pi(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!function(e){return Mn.includes(e.update)}(e))return Qn(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push((()=>{t?Bn(t.bind(e.proxy),e,14):o&&o(e.proxy)})),new Promise((e=>{o=e}))}function di(e,t){const n=typeof(e=ri(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(f(e)){const o=e.length;n=new Array(o),t.set(e,n);for(let r=0;r<o;r++)n[r]=di(e[r],t)}else{n={},t.set(e,n);for(const o in e)u(e,o)&&(n[o]=di(e[o],t))}return n}if("symbol"!==n)return e}function hi(e){return di(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function gi(e,t,n){if(!t)return;(t=hi(t)).$eS=e.$eS||{},t.$eA=e.$eA||{};const o=e.ctx,r=o.mpType;if("page"===r||"component"===r){t.r0=1;const r=o.$scope,i=Object.keys(t),s=ai(t,n||function(e,t){const n=e.data,o=Object.create(null);return t.forEach((e=>{o[e]=n[e]})),o}(r,i));Object.keys(s).length?(o.__next_tick_pending=!0,r.setData(s,(()=>{o.__next_tick_pending=!1,fi(e)})),Kn()):fi(e)}}function mi(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const e=Object.keys(o);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function yi(e,t=!1){const{setupState:n,$templateRefs:o,$templateUniElementRefs:r,ctx:{$scope:i,$mpPlatform:s}}=e;if("mp-alipay"===s)return;if(!i||!o&&!r)return;if(t)return o&&o.forEach((e=>vi(e,null,n))),void(r&&r.forEach((e=>vi(e,null,n))));const a="mp-baidu"===s||"mp-toutiao"===s,c=e=>{if(0===e.length)return[];const t=(i.selectAllComponents(".r")||[]).concat(i.selectAllComponents(".r-i-f")||[]);return e.filter((e=>{const o=function(e,t){const n=e.find((e=>e&&(e.properties||e.props).uI===t));if(n){const e=n.$vm;return e?Xr(e.$)||e:function(e){y(e)&&un(e);return e}(n)}return null}(t,e.i);return!(!a||null!==o)||(vi(e,o,n),!1)}))},l=()=>{if(o){const t=c(o);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},(()=>{c(t)}))}};r&&r.length&&pi(e,(()=>{r.forEach((e=>{f(e.v)?e.v.forEach((t=>{vi(e,t,n)})):vi(e,e.v,n)}))})),i._$setRef?i._$setRef(l):pi(e,l)}function vi({r:e,f:t},n,o){if(h(e))e(n,{});else{const r=g(e),i=mn(e);if(r||i)if(t){if(!i)return;f(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;n.$&&Do((()=>c(t,n)),n.$)}}else r?u(o,e)&&(o[e]=n):mn(e)?e.value=n:bi(e);else bi(e)}}function bi(e){oi("Invalid template ref type:",e,`(${typeof e})`)}const wi=Wn;function xi(e,t){const n=e.component=Rr(e,t.parentComponent,null);return n.ctx.$onApplyOptions=mi,n.ctx.$children=[],"app"===t.mpType&&(n.render=o),t.onBeforeSetup&&t.onBeforeSetup(n,t),kn(e),Er(n,"mount"),Er(n,"init"),Wr(n),Pr(n,"init"),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(Xr(n)||n.proxy),function(e){const t=Ei.bind(e);e.$updateScopedSlots=()=>Qn((()=>$n(t)));const n=()=>{if(e.isMounted){const{next:t,bu:n,u:o}=e;kn(t||e.vnode),Pi(e,!1),ki(),n&&L(n),Pi(e,!0),Er(e,"patch"),gi(e,Ai(e)),Pr(e,"patch"),o&&wi(o),io(e),En()}else Do((()=>{yi(e,!0)}),e),Er(e,"patch"),gi(e,Ai(e)),Pr(e,"patch"),ro(e)},r=e.effect=new Ke(n,o,(()=>$n(i)),e.scope),i=e.update=()=>{r.dirty&&r.run()};i.id=e.uid,Pi(e,!0),r.onTrack=e.rtc?t=>L(e.rtc,t):void 0,r.onTrigger=e.rtg?t=>L(e.rtg,t):void 0,i.ownerInstance=e,i()}(n),En(),Pr(n,"mount"),n.proxy}const _i=e=>{let t;for(const n in e)("class"===n||"style"===n||i(n))&&((t||(t={}))[n]=e[n]);return t};function Ai(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:c,emit:l,render:u,renderCache:f,data:p,setupState:d,ctx:h,uid:g,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:m}}}},inheritAttrs:y}=e;let v;e.$uniElementIds=new Map,e.$templateRefs=[],e.$templateUniElementRefs=[],e.$templateUniElementStyles={},e.$ei=0,m(g),e.__counter=0===e.__counter?1:0;const b=mo(e);try{if(4&n.shapeFlag){Si(y,i,s,c);const e=r||o;v=u.call(e,e,f,i,d,p,h)}else{Si(y,i,s,t.props?c:_i(c));const e=t;v=e.length>1?e(i,{attrs:c,slots:a,emit:l}):e(i,null)}}catch(w){jn(w,e,1),v=!1}return yi(e),mo(b),v}function Si(e,t,n,o){if(t&&o&&!1!==e){const e=Object.keys(o).filter((e=>"class"!==e&&"style"!==e));if(!e.length)return;n&&e.some(s)?e.forEach((e=>{s(e)&&e.slice(9)in n||(t[e]=o[e])})):e.forEach((e=>t[e]=o[e]))}}const ki=e=>{nt(),Kn(),ot()};function Ei(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach((({path:e,index:t,data:r})=>{const i=Te(n,e),s=g(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===i||void 0===i[t])o[s]=r;else{const e=ai(r,i[t]);Object.keys(e).forEach((t=>{o[s+"."+t]=e[t]}))}})),e.length=0,Object.keys(o).length&&t.setData(o)}function Pi({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Ii(e){const{bum:t,scope:n,update:o,um:r}=e;t&&L(t);{const t=e.parent;if(t){const n=t.ctx.$children,o=Xr(e)||e.proxy,r=n.indexOf(o);r>-1&&n.splice(r,1)}}var i;n.stop(),o&&(o.active=!1),r&&wi(r),wi((()=>{e.isUnmounted=!0})),i=e,Zn&&"function"==typeof Zn.cleanupBuffer&&!Zn.cleanupBuffer(i)&&so(i)}const Ci=function(e,t=null){h(e)||(e=a({},e)),null==t||y(t)||(Pn("root props passed to app.mount() must be an object."),t=null);const n=Eo(),o=new WeakSet,r=n.app={_uid:Po++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:ni,get config(){return n.config},set config(e){Pn("app.config cannot be replaced. Modify individual options instead.")},use:(e,...t)=>(o.has(e)?Pn("Plugin has already been applied to target app."):e&&h(e.install)?(o.add(e),e.install(r,...t)):h(e)?(o.add(e),e(r,...t)):Pn('A plugin must either be a function or an object with an "install" function.'),r),mixin:e=>(n.mixins.includes(e)?Pn("Mixin has already been applied to target app"+(e.name?`: ${e.name}`:"")):n.mixins.push(e),r),component:(e,t)=>(Qr(e,n.config),t?(n.components[e]&&Pn(`Component "${e}" has already been registered in target app.`),n.components[e]=t,r):n.components[e]),directive:(e,t)=>(ko(e),t?(n.directives[e]&&Pn(`Directive "${e}" has already been registered in target app.`),n.directives[e]=t,r):n.directives[e]),mount(){},unmount(){},provide:(e,t)=>(e in n.provides&&Pn(`App already provides property with key "${String(e)}". It will be overwritten with the new value.`),n.provides[e]=t,r),runWithContext(e){const t=Io;Io=r;try{return e()}finally{Io=t}}};return r};function Ti(e,t=null){const n="undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0;n.__VUE__=!0,oo(n.__VUE_DEVTOOLS_GLOBAL_HOOK__,n);const r=Ci(e,t),i=r._context;i.config.globalProperties.$nextTick=function(e){return pi(this.$,e)};const s=e=>(e.appContext=i,e.shapeFlag=6,e),a=function(e,t){return xi(s(e),t)},c=function(e){return e&&Ii(e.$)};return r.mount=function(){e.render=o;const t=xi(s({type:e}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});return r._instance=t.$,function(e,t){no("app:init",e,t,{Fragment:Tr,Text:Br,Comment:Or,Static:jr})}(r,ni),t.$app=r,t.$createComponent=a,t.$destroyComponent=c,i.$appInstance=t,t},r.unmount=function(){oi("Cannot unmount an app.")},r}function Bi(e,t,n,o){h(t)&&Lo(e,t.bind(n),o)}function Oi(e,t,n){!function(e,t,n){const o=e.mpType||n.$mpType;o&&"component"!==o&&Object.keys(e).forEach((o=>{if(Fe(o,e[o],!1)){const r=e[o];f(r)?r.forEach((e=>Bi(o,e,n,t))):Bi(o,r,n,t)}}))}(e,t,n)}function ji(e,t,n){return e[t]=n}function Li(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Ni(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i[re]?i.proxy.$callHook(re,n):Ln(n,r,o?o.$.vnode:null,!1)}}function Ri(e,t){return e?[...new Set([].concat(e,t))]:t}let Mi;const Fi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ui=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Di(){const e=ac.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(Mi(o).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function qi(e){const t=e.config;var n;t.errorHandler=qe(e,Ni),n=t.optionMergeStrategies,Re.forEach((e=>{n[e]=Ri}));const o=t.globalProperties;!function(e){e.uniIDHasRole=function(e){const{role:t}=Di();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=Di();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=Di();return e>Date.now()}}(o),o.$set=ji,o.$applyOptions=Oi,o.$callMethod=Li,ac.invokeCreateVueAppHook(e)}Mi="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Ui.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",i=0;i<e.length;)t=Fi.indexOf(e.charAt(i++))<<18|Fi.indexOf(e.charAt(i++))<<12|(n=Fi.indexOf(e.charAt(i++)))<<6|(o=Fi.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;const Hi=Object.create(null);function zi(e){const{uid:t,__counter:n}=Fr(),o=(Hi[t]||(Hi[t]=[])).push(function(e){return e?cn(e)||"__vInternal"in e?a({},e):e:null}(e))-1;return t+","+o+","+n}function Qi(e){delete Hi[e]}function $i(e){if(!e)return;const[t,n]=e.split(",");return Hi[t]?Hi[t][parseInt(n)]:void 0}var Vi={install(e){qi(e),e.config.globalProperties.pruneComponentPropsCache=Qi;const t=e.mount;e.mount=function(n){const o=t.call(e,n),r=function(){const e="createApp";if("undefined"!=typeof global&&void 0!==global[e])return global[e];if("undefined"!=typeof my)return my[e]}();return r?r(o):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(o),o}}};function Wi(e){return g(e)?e:function(e){let t="";if(!e||g(e))return t;for(const n in e)t+=`${n.startsWith("--")?n:T(n)}:${e[n]};`;return t}(R(e))}function Ki(e,t){const n=Fr(),r=n.ctx,i=void 0===t||"mp-weixin"!==r.$mpPlatform&&"mp-qq"!==r.$mpPlatform&&"mp-xhs"!==r.$mpPlatform||!g(t)&&"number"!=typeof t?"":"_"+t,s="e"+n.$ei+++i,c=r.$scope;if(!e)return delete c[s],s;const l=c[s];return l?l.value=e:c[s]=function(e,t){const n=e=>{var r;(r=e).type&&r.target&&(r.preventDefault=o,r.stopPropagation=o,r.stopImmediatePropagation=o,u(r,"detail")||(r.detail={}),u(r,"markerId")&&(r.detail="object"==typeof r.detail?r.detail:{},r.detail.markerId=r.markerId),_(r.detail)&&u(r.detail,"checked")&&!u(r.detail,"value")&&(r.detail.value=r.detail.checked),_(r.detail)&&(r.target=a({},r.target,r.detail)));let i=[e];t&&t.ctx.$getTriggerEventDetail&&"number"==typeof e.detail&&(e.detail=t.ctx.$getTriggerEventDetail(e.detail)),e.detail&&e.detail.__args__&&(i=e.detail.__args__);const s=n.value,c=()=>On(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e(t)))}return t}(e,s),t,5,i),l=e.target,p=!!l&&(!!l.dataset&&"true"===String(l.dataset.eventsync));if(!Ji.includes(e.type)||p){const t=c();if("input"===e.type&&(f(t)||v(t)))return;return t}setTimeout(c)};return n.value=e,n}(e,n),s}const Ji=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function Xi(e,t={},n){const o=Fr(),{parent:r,isMounted:i,ctx:{$scope:s}}=o,a=(s.properties||s.props).uI;if(!a)return;if(!r&&!i)return void Mo((()=>{Xi(e,t,n)}),o);const c=function(e,t){let n=t.parent;for(;n;){const t=n.$ssi;if(t&&t[e])return t[e];n=n.parent}}(a,o);c&&c(e,t,n)}function Gi(e){return e.$scope.virtualHostId}function Yi(e,t){return!function(e){return!!Gi(e)}(e)||function(e){return e.$.propsOptions&&e.$.propsOptions[0]&&"id"in e.$.propsOptions[0]}(e)?t:Gi(e)}const Zi=function(e,t,n){return Yi(e,t)||n||""};const es=function(e,t=null){return e&&(e.mpType="app"),Ti(e,t).use(Vi)};function ts(e,t){console.warn(`${e}: ${t}`)}function ns(e,t,n,o){o||(o=ts);for(const r in n){const i=os(r,t[r],n[r],!u(t,r));g(i)&&o(e,i)}}function os(e,t,n,o){_(n)||(n={type:n});const{type:r,required:i,validator:s}=n;if(i&&o)return'Missing required args: "'+e+'"';if(null!=t||i){if(null!=r){let n=!1;const o=f(r)?r:[r],i=[];for(let e=0;e<o.length&&!n;e++){const{valid:r,expectedType:s}=is(t,o[e]);i.push(s||""),n=r}if(!n)return function(e,t,n){let o=`Invalid args: type check failed for args "${e}". Expected ${n.map(B).join(", ")}`;const r=n[0],i=x(t),s=ss(t,r),a=ss(t,i);1===n.length&&as(r)&&!function(...e){return e.some((e=>"boolean"===e.toLowerCase()))}(r,i)&&(o+=` with value ${s}`);o+=`, got ${i} `,as(i)&&(o+=`with value ${a}.`);return o}(e,t,i)}return s?s(t):void 0}}const rs=e("String,Number,Boolean,Function,Symbol");function is(e,t){let n;const o=function(e){const t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}(t);if(rs(o)){const r=typeof e;n=r===o.toLowerCase(),n||"object"!==r||(n=e instanceof t)}else n="Object"===o?y(e):"Array"===o?f(e):e instanceof t;return{valid:n,expectedType:o}}function ss(e,t){return"String"===t?`"${e}"`:"Number"===t?`${Number(e)}`:`${e}`}function as(e){return["string","number","boolean"].some((t=>e.toLowerCase()===t))}function cs(e){return function(){try{return e.apply(e,arguments)}catch(ah){console.error(ah)}}}let ls=1;const us={};function fs(e,t,n){if("number"==typeof e){const o=us[e];if(o)return o.keepAlive||delete us[e],o.callback(t,n)}return t}const ps="success",ds="fail",hs="complete";function gs(e,t={},{beforeAll:n,beforeSuccess:o}={}){_(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];h(o)&&(t[n]=cs(o),delete e[n])}return t}(t),a=h(r),c=h(i),l=h(s),u=ls++;return function(e,t,n,o=!1){us[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),h(n)&&n(u),u.errMsg===e+":ok"?(h(o)&&o(u,t),a&&r(u)):c&&i(u),l&&s(u)})),u}const ms="success",ys="fail",vs="complete",bs={},ws={};function xs(e,t){return function(n){return e(n,t)||n}}function _s(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(xs(i,n));else{const e=i(t,n);if(v(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function As(e,t={}){return[ms,ys,vs].forEach((n=>{const o=e[n];if(!f(o))return;const r=t[n];t[n]=function(e){_s(o,e,t).then((e=>h(r)&&r(e)||e))}})),t}function Ss(e,t){const n=[];f(bs.returnValue)&&n.push(...bs.returnValue);const o=ws[e];return o&&f(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Es(e){const t=Object.create(null);Object.keys(bs).forEach((e=>{"returnValue"!==e&&(t[e]=bs[e].slice())}));const n=ws[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Ps(e,t,n,o){const r=Es(e);if(r&&Object.keys(r).length){if(f(r.invoke)){return _s(r.invoke,n).then((n=>t(As(Es(e),n),...o)))}return t(As(r,n),...o)}return t(n,...o)}function Is(e,t){return(n={},...o)=>function(e){return!(!_(e)||![ps,ds,hs].find((t=>h(e[t]))))}(n)?Ss(e,Ps(e,t,n,o)):Ss(e,new Promise(((r,i)=>{Ps(e,t,a(n,{success:r,fail:i}),o)})))}function Cs(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,fs(e,a({errMsg:i},o))}function Ts(e,t,n,o){!function(e,t,n,o){if(!n)return;if(!f(n))return ns(e,t[0]||Object.create(null),n,o);const r=n.length,i=t.length;for(let s=0;s<r;s++){const r=n[s],a=Object.create(null);i>s&&(a[r.name]=t[s]),ns(e,a,{[r.name]:r},o)}}(e,t,n);const r=function(e){e[0]}(t);if(r)return r}function Bs(e,t,n,o){return r=>{const i=gs(e,r,o),s=Ts(e,[r],n);return s?Cs(i,e,s):t(r,{resolve:t=>function(e,t,n){return fs(e,a(n||{},{errMsg:t+":ok"}))}(i,e,t),reject:(t,n)=>Cs(i,e,function(e){return!e||g(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Os(e,t,n,o){return function(e,t,n){return(...o)=>{const r=Ts(e,o,n);if(r)throw new Error(r);return t.apply(null,o)}}(e,t,n)}let js=!1,Ls=0,Ns=0;const Rs=Os("upx2px",((e,t)=>{if(0===Ls&&function(){var e,t;let n,o,r;{const i=(null===(e=wx.getWindowInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync(),s=(null===(t=wx.getDeviceInfo)||void 0===t?void 0:t.call(wx))||wx.getSystemInfoSync();n=i.windowWidth,o=i.pixelRatio,r=s.platform}Ls=n,Ns=o,js="ios"===r}(),0===(e=Number(e)))return 0;let n=e/750*(t||Ls);return n<0&&(n=-n),n=Math.floor(n+1e-4),0===n&&(n=1!==Ns&&js?.5:1),e<0?-n:n}),[{name:"upx",type:[Number,String],required:!0}]);const Ms=[{name:"method",type:[String,Object],required:!0}],Fs=Ms;function Us(e,t){Object.keys(t).forEach((n=>{h(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):f(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function Ds(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];f(o)&&h(r)&&c(o,r)}))}const qs=Os("addInterceptor",((e,t)=>{g(e)&&_(t)?Us(ws[e]||(ws[e]={}),t):_(e)&&Us(bs,e)}),Ms),Hs=Os("removeInterceptor",((e,t)=>{g(e)?_(t)?Ds(ws[e],t):delete ws[e]:_(e)&&Ds(bs,e)}),Fs),zs=[{name:"event",type:String,required:!0},{name:"callback",type:Function,required:!0}],Qs=zs,$s=[{name:"event",type:[String,Array]},{name:"callback",type:[Function,Number]}],Vs=[{name:"event",type:String,required:!0}];const Ws=new class{constructor(){this.$emitter=new ze}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},Ks=Os("$on",((e,t)=>(Ws.on(e,t),()=>Ws.off(e,t))),zs),Js=Os("$once",((e,t)=>(Ws.once(e,t),()=>Ws.off(e,t))),Qs),Xs=Os("$off",((e,t)=>{f(e)||(e=e?[e]:[]),e.forEach((e=>{Ws.off(e,t)}))}),$s),Gs=Os("$emit",((e,...t)=>{Ws.emit(e,...t)}),Vs);let Ys,Zs,ea;function ta(e){try{return JSON.parse(e)}catch(ah){}return e}const na=[];function oa(e,t){na.forEach((n=>{n(e,t)})),na.length=0}const ra=Is(ia="getPushClientId",function(e,t,n,o){return Bs(e,t,n,o)}(ia,((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===ea&&(ea=!1,Ys="",Zs="uniPush is not enabled"),na.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==Ys&&oa(Ys,Zs)}))}),sa,aa));var ia,sa,aa;const ca=[],la=/^\$|__f__|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|rpx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,ua=/^create|Manager$/,fa=["createBLEConnection"],pa=["request","downloadFile","uploadFile","connectSocket"],da=["createBLEConnection"],ha=/^on|^off/;function ga(e){return ua.test(e)&&-1===fa.indexOf(e)}function ma(e){return la.test(e)&&-1===da.indexOf(e)}function ya(e){return-1!==pa.indexOf(e)}function va(e){return!(ga(e)||ma(e)||function(e){return ha.test(e)&&"onPush"!==e}(e))}function ba(e,t){return va(e)&&h(t)?function(n={},...o){return h(n.success)||h(n.fail)||h(n.complete)?Ss(e,Ps(e,t,n,o)):Ss(e,new Promise(((r,i)=>{Ps(e,t,a({},n,{success:r,fail:i}),o)})))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e&&e()).then((()=>n))),(n=>t.resolve(e&&e()).then((()=>{throw n}))))});const wa=["success","fail","cancel","complete"];const xa=()=>{const e=h(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=Z(n&&n.language?n.language:J)||J}return t}()},_a=[];"undefined"!=typeof global&&(global.getLocale=xa);const Aa="__DC_STAT_UUID";let Sa;function ka(e=wx){return function(t,n){Sa=Sa||e.getStorageSync(Aa),Sa||(Sa=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:Aa,data:Sa})),n.deviceId=Sa}}function Ea(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function Pa(e,t){let n="",o="";switch(n=e.split(" ")[0]||t,o=e.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows"}return{osName:n,osVersion:o}}function Ia(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(e),r=t.toLocaleLowerCase();for(let t=0;t<o.length;t++){const i=o[t];if(-1!==r.indexOf(i)){n=e[i];break}}}return n}function Ca(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function Ta(e){return xa?xa():e}function Ba(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const Oa={returnValue:(e,t)=>{Ea(e,t),ka()(e,t),function(e,t){const{brand:n="",model:o="",system:r="",language:i="",theme:s,version:c,platform:l,fontSizeSetting:u,SDKVersion:f,pixelRatio:p,deviceOrientation:d}=e,{osName:h,osVersion:g}=Pa(r,l);let m=c,y=Ia(e,o),v=Ca(n),b=Ba(e),w=d,x=p,_=f;const A=(i||"").replace(/_/g,"-"),S={appId:"__UNI__0820E41",appName:"诺盾教育",appVersion:"1.0.0",appVersionCode:"100",appLanguage:Ta(A),uniCompileVersion:"4.66",uniCompilerVersion:"4.66",uniRuntimeVersion:"4.66",uniPlatform:"mp-weixin",deviceBrand:v,deviceModel:o,deviceType:y,devicePixelRatio:x,deviceOrientation:w,osName:h,osVersion:g,hostTheme:s,hostVersion:m,hostLanguage:A,hostName:b,hostSDKVersion:_,hostFontSizeSetting:u,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};a(t,S)}(e,t)}},ja=Oa,La={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!f(o))return;const r=o.length;return r?(n<0?n=0:n>=r&&(n=r-1),n>0?(t.current=o[n],t.urls=o.filter(((e,t)=>!(t<n)||e!==o[n]))):t.current=o[0],{indicator:!1,loop:!1}):void 0}},Na={args(e,t){t.alertText=e.title}},Ra={returnValue:(e,t)=>{const{brand:n,model:o,system:r="",platform:i=""}=e;let s=Ia(e,o),c=Ca(n);ka()(e,t);const{osName:l,osVersion:u}=Pa(r,i);t=Be(a(t,{deviceType:s,deviceBrand:c,deviceModel:o,osName:l,osVersion:u}))}},Ma={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:r,theme:i}=e;let s=Ba(e),c=(o||"").replace(/_/g,"-");const l={hostVersion:n,hostLanguage:c,hostName:s,hostSDKVersion:r,hostTheme:i,appId:"__UNI__0820E41",appName:"诺盾教育",appVersion:"1.0.0",appVersionCode:"100",appLanguage:Ta(c),isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.66",uniCompilerVersion:"4.66",uniRuntimeVersion:"4.66"};a(t,l)}},Fa={returnValue:(e,t)=>{Ea(e,t),t=Be(a(t,{windowTop:0,windowBottom:0}))}},Ua={args(e){const t=getApp({allowDefault:!0})||{};t.$vm?Lo(re,e,t.$vm.$):(wx.$onErrorHandlers||(wx.$onErrorHandlers=[]),wx.$onErrorHandlers.push(e))}},Da={args(e){const t=getApp({allowDefault:!0})||{};if(t.$vm){if(e.__weh){const n=t.$vm.$[re];if(n){const t=n.indexOf(e.__weh);t>-1&&n.splice(t,1)}}}else{if(!wx.$onErrorHandlers)return;const t=wx.$onErrorHandlers.findIndex((t=>t===e));-1!==t&&wx.$onErrorHandlers.splice(t,1)}}},Ha={args(){if(wx.__uni_console__){if(wx.__uni_console_warned__)return;wx.__uni_console_warned__=!0,console.warn("开发模式下小程序日志回显会使用 socket 连接，为了避免冲突，建议使用 SocketTask 的方式去管理 WebSocket 或手动关闭日志回显功能。[详情](https://uniapp.dcloud.net.cn/tutorial/run/mp-log.html)")}}},za=Ha,Qa={$on:Ks,$off:Xs,$once:Js,$emit:Gs,upx2px:Rs,rpx2px:Rs,interceptors:{},addInterceptor:qs,removeInterceptor:Hs,onCreateVueApp:function(e){if(Ue)return e(Ue);De.push(e)},invokeCreateVueAppHook:function(e){Ue=e,De.forEach((t=>t(e)))},getLocale:xa,setLocale:e=>{const t=h(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,_a.forEach((t=>t({locale:e}))),!0)},onLocaleChange:e=>{-1===_a.indexOf(e)&&_a.push(e)},getPushClientId:ra,onPushMessage:e=>{-1===ca.indexOf(e)&&ca.push(e)},offPushMessage:e=>{if(e){const t=ca.indexOf(e);t>-1&&ca.splice(t,1)}else ca.length=0},invokePushCallback:function(e){if("enabled"===e.type)ea=!0;else if("clientId"===e.type)Ys=e.cid,Zs=e.errMsg,oa(Ys,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:ta(e.message)};for(let e=0;e<ca.length;e++){if((0,ca[e])(t),t.stopped)break}}else"click"===e.type&&ca.forEach((t=>{t({type:"click",data:ta(e.message)})}))},__f__:function(e,t,...n){t&&n.push(t),console[e].apply(console,n)}};const $a=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],Va=["lanDebug","router","worklet"],Wa=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function Ka(e){return(!Wa||1154!==Wa.scene||!Va.includes(e))&&($a.indexOf(e)>-1||"function"==typeof wx[e])}function Ja(){const e={};for(const t in wx)Ka(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const Xa=["__route__","__wxExparserNodeId__","__wxWebviewId__"],Ga=(Ya={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]},function({service:e,success:t,fail:n,complete:o}){let r;Ya[e]?(r={errMsg:"getProvider:ok",service:e,provider:Ya[e]},h(t)&&t(r)):(r={errMsg:"getProvider:fail:服务["+e+"]不存在"},h(n)&&n(r)),h(o)&&o(r)});var Ya;const Za=Ja();Za.canIUse("getAppBaseInfo")||(Za.getAppBaseInfo=Za.getSystemInfoSync),Za.canIUse("getWindowInfo")||(Za.getWindowInfo=Za.getSystemInfoSync),Za.canIUse("getDeviceInfo")||(Za.getDeviceInfo=Za.getSystemInfoSync);let ec=Za.getAppBaseInfo&&Za.getAppBaseInfo();ec||(ec=Za.getSystemInfoSync());const tc=ec?ec.host:null,nc=tc&&"SAAASDK"===tc.env?Za.miniapp.shareVideoMessage:Za.shareVideoMessage;var oc=Object.freeze({__proto__:null,createSelectorQuery:function(){const e=Za.createSelectorQuery(),t=e.in;return e.in=function(e){return e.$scope?t.call(this,e.$scope):t.call(this,function(e){const t=Object.create(null);return Xa.forEach((n=>{t[n]=e[n]})),t}(e))},e},getProvider:Ga,shareVideoMessage:nc});const rc={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var ic=Object.freeze({__proto__:null,compressImage:rc,getAppAuthorizeSetting:{returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},getAppBaseInfo:Ma,getDeviceInfo:Ra,getSystemInfo:Oa,getSystemInfoSync:ja,getWindowInfo:Fa,offError:Da,onError:Ua,onSocketMessage:za,onSocketOpen:Ha,previewImage:La,redirectTo:{},showActionSheet:Na});const sc=Ja();var ac=function(e,t,n=wx){const o=function(e){function t(e,t,n){return function(r){return t(o(e,r,n))}}function n(e,n,o={},r={},i=!1){if(_(n)){const s=!0===i?n:{};h(o)&&(o=o(n,s)||{});for(const a in n)if(u(o,a)){let t=o[a];h(t)&&(t=t(n[a],n,s)),t?g(t)?s[t]=n[a]:_(t)&&(s[t.name?t.name:a]=t.value):console.warn(`微信小程序 ${e} 暂不支持 ${a}`)}else if(-1!==wa.indexOf(a)){const o=n[a];h(o)&&(s[a]=t(e,o,r))}else i||u(s,a)||(s[a]=n[a]);return s}return h(n)&&(h(o)&&o(n,{}),n=t(e,n,r)),n}function o(t,o,r,i=!1){return h(e.returnValue)&&(o=e.returnValue(t,o)),n(t,o,r,{},i||!1)}return function(t,r){const i=u(e,t);if(!i&&"function"!=typeof wx[t])return r;const s=i||h(e.returnValue)||ga(t)||ya(t),a=i||h(r);if(!i&&!r)return function(){console.error(`微信小程序 暂不支持${t}`)};if(!s||!a)return r;const c=e[t];return function(e,r){let i=c||{};h(c)&&(i=c(e));const s=[e=n(t,e,i.args,i.returnValue)];void 0!==r&&s.push(r);const a=wx[i.name||t].apply(wx,s);return(ga(t)||ya(t))&&a&&!a.__v_skip&&(a.__v_skip=!0),ma(t)?o(t,a,i.returnValue,ga(t)):a}}}(t);return new Proxy({},{get:(t,r)=>u(t,r)?t[r]:u(e,r)?ba(r,e[r]):u(Qa,r)?ba(r,Qa[r]):ba(r,o(r,n[r]))})}(oc,ic,sc);function cc(e,t,n){return""==e||""==t||""==n?Promise.resolve(null):e.split(",").reduce(((e,o)=>e.then((e=>null!=e?Promise.resolve(e):function(e,t,n){return new Promise(((o,r)=>{const i=ac.connectSocket({url:`ws://${e}:${t}/${n}`,multiple:!0,fail(){o(null)}}),s=setTimeout((()=>{i.close({code:1006,reason:"connect timeout"}),o(null)}),lc);i.onOpen((e=>{clearTimeout(s),o(i)})),i.onClose((e=>{clearTimeout(s),o(null)})),i.onError((e=>{clearTimeout(s),o(null)}))}))}(o,t,n)))),Promise.resolve(null))}const lc=500;const uc=["log","warn","error","info","debug"],fc=uc.reduce(((e,t)=>(e[t]=console[t].bind(console),e)),{});let pc=null;const dc=new Set,hc={};function gc(e){if(null==pc)return void e.forEach((e=>{dc.add(e)}));const t=e.map((e=>{if("string"==typeof e)return e;const t=e&&"promise"in e&&"reason"in e,n=t?"UnhandledPromiseRejection: ":"";if(t&&(e=e.reason),e instanceof Error&&e.stack)return e.message&&!e.stack.includes(e.message)?`${n}${e.message}\n${e.stack}`:`${n}${e.stack}`;if("object"==typeof e&&null!==e)try{return n+JSON.stringify(e)}catch(o){return n+String(o)}return n+String(e)})).filter(Boolean);t.length>0&&pc(JSON.stringify(Object.assign({type:"error",data:t},hc)))}function mc(){function e(e){try{if("undefined"!=typeof PromiseRejectionEvent&&e instanceof PromiseRejectionEvent&&e.reason instanceof Error&&e.reason.message&&e.reason.message.includes("Cannot create property 'errMsg' on string 'taskId"))return;fc.error(e),gc([e])}catch(t){fc.error(t)}}return"function"==typeof ac.onError&&ac.onError(e),"function"==typeof ac.onUnhandledRejection&&ac.onUnhandledRejection(e),function(){"function"==typeof ac.offError&&ac.offError(e),"function"==typeof ac.offUnhandledRejection&&ac.offUnhandledRejection(e)}}function yc(e,t){try{return{type:e,args:vc(t)}}catch(ah){}return{type:e,args:[]}}function vc(e){return e.map((e=>bc(e)))}function bc(e,t=0){if(t>=7)return{type:"object",value:"[Maximum depth reached]"};switch(typeof e){case"string":return{type:"string",value:e};case"number":return function(e){return{type:"number",value:String(e)}}(e);case"boolean":return function(e){return{type:"boolean",value:String(e)}}(e);case"object":try{return function(e,t){if(null===e)return{type:"null"};if(function(e){return e.$&&wc(e.$)}(e))return function(e,t){return{type:"object",className:"ComponentPublicInstance",value:{properties:Object.entries(e.$.type).map((([e,n])=>xc(e,n,t+1)))}}}(e,t);if(wc(e))return function(e,t){return{type:"object",className:"ComponentInternalInstance",value:{properties:Object.entries(e.type).map((([e,n])=>xc(e,n,t+1)))}}}(e,t);if(function(e){return e.style&&null!=e.tagName&&null!=e.nodeName}(e))return function(e,t){return{type:"object",value:{properties:Object.entries(e).filter((([e])=>["id","tagName","nodeName","dataset","offsetTop","offsetLeft","style"].includes(e))).map((([e,n])=>xc(e,n,t+1)))}}}(e,t);if(function(e){return"function"==typeof e.getPropertyValue&&"function"==typeof e.setProperty&&e.$styles}(e))return function(e,t){return{type:"object",value:{properties:Object.entries(e.$styles).map((([e,n])=>xc(e,n,t+1)))}}}(e,t);if(Array.isArray(e))return{type:"object",subType:"array",value:{properties:e.map(((e,n)=>function(e,t,n){const o=bc(e,n);return o.name=`${t}`,o}(e,n,t+1)))}};if(e instanceof Set)return{type:"object",subType:"set",className:"Set",description:`Set(${e.size})`,value:{entries:Array.from(e).map((e=>function(e,t){return{value:bc(e,t)}}(e,t+1)))}};if(e instanceof Map)return{type:"object",subType:"map",className:"Map",description:`Map(${e.size})`,value:{entries:Array.from(e.entries()).map((e=>function(e,t){return{key:bc(e[0],t),value:bc(e[1],t)}}(e,t+1)))}};if(e instanceof Promise)return{type:"object",subType:"promise",value:{properties:[]}};if(e instanceof RegExp)return{type:"object",subType:"regexp",value:String(e),className:"Regexp"};if(e instanceof Date)return{type:"object",subType:"date",value:String(e),className:"Date"};if(e instanceof Error)return{type:"object",subType:"error",value:e.message||String(e),className:e.name||"Error"};let n;{const t=e.constructor;t&&t.get$UTSMetadata$&&(n=t.get$UTSMetadata$().name)}let o=Object.entries(e);(function(e){return e.modifier&&e.modifier._attribute&&e.nodeContent})(e)&&(o=o.filter((([e])=>"modifier"!==e&&"nodeContent"!==e)));return{type:"object",className:n,value:{properties:o.map((e=>xc(e[0],e[1],t+1)))}}}(e,t)}catch(ah){return{type:"object",value:{properties:[]}}}case"undefined":return{type:"undefined"};case"function":return function(e){return{type:"function",value:`function ${e.name}() {}`}}(e);case"symbol":return function(e){return{type:"symbol",value:e.description}}(e);case"bigint":return function(e){return{type:"bigint",value:String(e)}}(e)}}function wc(e){return e.type&&null!=e.uid&&e.appContext}function xc(e,t,n){const o=bc(t,n);return o.name=e,o}let _c=null;const Ac=[],Sc={},kc="---BEGIN:EXCEPTION---";function Ec(e){null!=_c?_c(JSON.stringify(Object.assign({type:"console",data:e},Sc))):Ac.push(...e)}const Pc=/^\s*at\s+[\w/./-]+:\d+$/;function Ic(){if(function(){const e=console.log,t=Symbol();try{console.log=t}catch(o){return!1}const n=console.log===t;return console.log=e,n}())return uc.forEach((e=>{console[e]=function(e){return function(...t){const n=[...t];if(n.length){const e=n[n.length-1];"string"==typeof e&&Pc.test(e)&&n.pop()}if(fc[e](...n),"error"===e&&1===t.length){const e=t[0];if("string"==typeof e&&e.startsWith(kc)){const t=21,n=e.length-19;return void gc([e.slice(t,n)])}if(e instanceof Error)return void gc([e])}Ec([yc(e,t)])}}(e)})),function(){uc.forEach((e=>{console[e]=fc[e]}))};if(void 0!==ac&&ac.__f__){const e=ac.__f__;if(e)return ac.__f__=function(...t){const[n,o,...r]=t;e(n,"",...r),Ec([yc(n,[...r,o])])},function(){ac.__f__=e}}return function(){}}const Cc="‌";function Tc(e){return`${Cc}${e}${Cc}`}!function(){const e="undefined"!=typeof swan;let t=e?()=>{}:mc(),n=e?()=>{}:Ic();Promise.resolve().then((()=>(e&&(t=mc(),n=Ic()),cc("192.168.112.217,127.0.0.1","8090","mp-weixin_tjVNcA").then((e=>e?(void 0!==sc?sc.__uni_console__=!0:"undefined"!=typeof my?my.__uni_console__=!0:"undefined"!=typeof tt?tt.__uni_console__=!0:"undefined"!=typeof swan?swan.__uni_console__=!0:"undefined"!=typeof qq?qq.__uni_console__=!0:"undefined"!=typeof ks?ks.__uni_console__=!0:"undefined"!=typeof jd?jd.__uni_console__=!0:"undefined"!=typeof xhs?xhs.__uni_console__=!0:"undefined"!=typeof has?has.__uni_console__=!0:"undefined"!=typeof qa&&(qa.__uni_console__=!0),e.onClose((()=>{fc.error(Tc("开发模式下日志通道 socket 连接关闭，请在 HBuilderX 中重新运行。")),t(),n()})),function(e,t={}){if(_c=e,Object.assign(Sc,t),null!=e&&Ac.length>0){const e=Ac.slice();Ac.length=0,Ec(e)}}((t=>{e.send({data:t})})),function(e,t={}){if(pc=e,Object.assign(hc,t),null!=e&&dc.size>0){const e=Array.from(dc);dc.clear(),gc(e)}}((t=>{e.send({data:t})})),!0):(t(),n(),fc.error(Tc("开发模式下日志通道建立 socket 连接失败。")),fc.error(Tc("小程序平台，请勾选不校验合法域名配置。")),fc.error(Tc("如果是运行到真机，请确认手机与电脑处于同一网络。")),!1))))))}();const Bc=["externalClasses"];const Oc=/_(.*)_worklet_factory_/;function jc(e,t){const n=e.$children;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.$scope._$vueId===t)return e}let o;for(let r=n.length-1;r>=0;r--)if(o=jc(n[r],t),o)return o}const Lc=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function Nc(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,Object.defineProperties(n,{[Ie]:{get(){const e=this.$scope.data[Ie];return void 0===e?"":e}}}),n.$mp={},n._self={},e.slots={},f(t.slots)&&t.slots.length&&(t.slots.forEach((t=>{e.slots[t]=!0})),e.slots.d&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=Rc,n.$callHook=Mc,e.emit=function(e,t){return function(n,...o){const r=t.$scope;if(r&&n){const e={__args__:o};r.triggerEvent(n,e)}return e.apply(this,[n,...o])}}(e.emit,n)}function Rc(e){const t=this.$[e];return!(!t||!t.length)}function Mc(e,t){"mounted"===e&&(Mc.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(n,t)}const Fc=[ce,te,ne,ue,de,me,ye,ve,xe];function Uc(e,t=new Set){if(e){Object.keys(e).forEach((n=>{Fe(n,e[n])&&t.add(n)}));{const{extends:n,mixins:o}=e;o&&o.forEach((e=>Uc(e,t))),n&&Uc(n,t)}}return t}function Dc(e,t,n){-1!==n.indexOf(t)||u(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const qc=[le];function Hc(e,t,n=qc){t.forEach((t=>Dc(e,t,n)))}function zc(e,t,n=qc){Uc(t).forEach((t=>Dc(e,t,n)))}const Qc=Ce((()=>{const e=[],t=h(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(f(n)){const t=Object.keys(Me);n.forEach((n=>{t.forEach((t=>{u(n,t)&&!e.includes(t)&&e.push(t)}))}))}}return e}));const $c=[te,ne,re,ie,se,ae];function Vc(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const o=n.ctx;this.$vm&&o.$scope&&o.$callHook||(Nc(n,{mpType:"app",mpInstance:this,slots:[]}),o.globalData=this.globalData,e.$callHook(oe,t))}},r=wx.$onErrorHandlers;r&&(r.forEach((e=>{Lo(re,e,n)})),r.length=0),function(e){const t=yn(function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=Z(n&&n.language?n.language:J)||J}return t}());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(e);const i=e.$.type;Hc(o,$c),zc(o,i);{const e=i.methods;e&&a(o,e)}return o}function Wc(e,t){if(h(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}h(e.onShow)&&wx.onAppShow&&wx.onAppShow((e=>{t.$callHook("onShow",e)})),h(e.onHide)&&wx.onAppHide&&wx.onAppHide((e=>{t.$callHook("onHide",e)}))}const Kc=["eO","uR","uRIF","uI","uT","uP","uS"];function Jc(e){e.properties||(e.properties={}),a(e.properties,function(e,t=!1){const n={};if(!t){let e=function(e){const t=Object.create(null);e&&e.forEach((e=>{t[e]=!0})),this.setData({$slots:t})};Kc.forEach((e=>{n[e]={type:null,value:""}})),n.uS={type:null,value:[]},n.uS.observer=e}return e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}(e),function(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""},t.virtualHostHidden={type:null,value:""},t[Ie]={type:null,value:""}),t}(e.options))}const Xc=[String,Number,Boolean,Object,Array,null];function Gc(e,t){const n=function(e){return f(e)&&1===e.length?e[0]:e}(e);return-1!==Xc.indexOf(n)?n:null}function Yc(e,t){return(t?function(e){const t={};_(e)&&Object.keys(e).forEach((n=>{-1===Kc.indexOf(n)&&(t[n]=e[n])}));return t}(e):$i(e.uP))||{}}function Zc(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?function(e,t){const n=ln(t.props),o=$i(e)||{};el(n,o)&&(!function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=ln(r),[c]=e.propsOptions;let l=!1;if(!(o||s>0)||16&s){let o;fr(e,t,r,i)&&(l=!0);for(const i in a)t&&(u(t,i)||(o=T(i))!==i&&u(t,o))||(c?!n||void 0===n[i]&&void 0===n[o]||(r[i]=pr(c,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&u(t,e)||(delete i[e],l=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(ho(e.emitsOptions,s))continue;const f=t[s];if(c)if(u(i,s))f!==i[s]&&(i[s]=f,l=!0);else{const t=I(s);r[t]=pr(c,a,t,f,e,!1)}else f!==i[s]&&(i[s]=f,l=!0)}}l&&ht(e,"set","$attrs"),vr(t||{},r,e)}(t,o,n,!1),r=t.update,Mn.indexOf(r)>-1&&function(e){const t=Mn.indexOf(e);t>Fn&&Mn.splice(t,1)}(t.update),t.update());var r}(e,this.$vm.$):"m"===this.properties.uT&&function(e,t){const n=t.properties,o=$i(e)||{};el(n,o,!1)&&t.setData(o)}(e,this))};e.observers||(e.observers={}),e.observers.uP=t}function el(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const n=o[r];if(t[n]!==e[n])return!0}return!1}function tl(e,t){e.data={},e.behaviors=function(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return f(t)&&t.forEach((e=>{o.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(f(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),o}(t)}function nl(e,{parse:t,mocks:n,isPage:o,isPageInProject:r,initRelation:i,handleLink:s,initLifetimes:c}){e=e.default||e;const l={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};f(e.mixins)&&e.mixins.forEach((e=>{y(e.options)&&a(l,e.options)})),e.options&&a(l,e.options);const p={options:l,lifetimes:c({mocks:n,isPage:o,initRelation:i,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:s}};var d,h,g,m;return tl(p,e),Jc(p),Zc(p),function(e,t){Bc.forEach((n=>{u(t,n)&&(e[n]=t[n])}))}(p,e),d=p.methods,h=e.wxsCallMethods,f(h)&&h.forEach((e=>{d[e]=function(t){return this.$vm[e](t)}})),g=p.methods,(m=e.methods)&&Object.keys(m).forEach((e=>{const t=e.match(Oc);if(t){const n=t[1];g[e]=m[e],g[n]=m[n]}})),t&&t(p,{handleLink:s}),p}let ol,rl;function il(){return getApp().$vm}function sl(e,t){const{parse:n,mocks:o,isPage:r,initRelation:i,handleLink:s,initLifetimes:a}=t,c=nl(e,{mocks:o,isPage:r,isPageInProject:!0,initRelation:i,handleLink:s,initLifetimes:a});!function({properties:e},t){f(t)?t.forEach((t=>{e[t]={type:String,value:""}})):_(t)&&Object.keys(t).forEach((n=>{const o=t[n];if(_(o)){let t=o.default;h(t)&&(t=t());const r=o.type;o.type=Gc(r),e[n]={type:o.type,value:t}}else e[n]={type:Gc(o)}}))}(c,(e.default||e).props);const l=c.methods;return l.onLoad=function(e){var t;return this.options=e,this.$page={fullPath:(t=this.route+Le(e),function(e){return 0===e.indexOf("/")}(t)?t:"/"+t)},this.$vm&&this.$vm.$callHook(ce,e)},Hc(l,Fc),zc(l,e),function(e,t){if(!t)return;Object.keys(Me).forEach((n=>{t&Me[n]&&Dc(e,n,[])}))}(l,e.__runtimeHooks),Hc(l,Qc()),n&&n(c,{handleLink:s}),c}const al=Page,cl=Component;function ll(e){const t=e.triggerEvent,n=function(n,...o){return t.apply(e,[(r=n,I(r.replace(Oe,"-"))),...o]);var r};try{e.triggerEvent=n}catch(o){e._triggerEvent=n}}function ul(e,t,n){const o=t[e];t[e]=o?function(...e){return ll(this),o.apply(this,e)}:function(){ll(this)}}Page=function(e){return ul(ce,e),al(e)},Component=function(e){ul("created",e);return e.properties&&e.properties.uP||(Jc(e),Zc(e)),cl(e)};var fl=Object.freeze({__proto__:null,handleLink:function(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=jc(this.$vm,n)),o||(o=this.$vm),t.parent=o},initLifetimes:function({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let r=this.properties;!function(e,t){if(!e)return;const n=e.split(","),o=n.length;1===o?t._$vueId=n[0]:2===o&&(t._$vueId=n[0],t._$vuePid=n[1])}(r.uI,this);const i={vuePid:this._$vuePid};n(this,i);const s=this,a=t(s);let c=r;this.$vm=function(e,t){ol||(ol=il().$createComponent);const n=ol(e,t);return Xr(n.$)||n}({type:o,props:Yc(c,a)},{mpType:a?"page":"component",mpInstance:s,slots:r.uS||{},parentComponent:i.parent&&i.parent.$,onBeforeSetup(t,n){!function(e,t){Object.defineProperty(e,"refs",{get(){const e={};return function(e,t,n){e.selectAllComponents(t).forEach((e=>{const t=e.properties.uR;n[t]=e.$vm||e}))}(t,".r",e),t.selectAllComponents(".r-i-f").forEach((t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))})),e}})}(t,s),function(e,t,n){const o=e.ctx;n.forEach((n=>{u(t,n)&&(e[n]=o[n]=t[n])}))}(t,s,e),function(e,t){Nc(e,t);const n=e.ctx;Lc.forEach((e=>{n[e]=function(...t){const o=n.$scope;if(o&&o[e])return o[e].apply(o,t)}}))}(t,n)}}),a||function(e){const t=e.$options;f(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",(()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})}),{immediate:!0})}(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook(le))},detached(){var e;this.$vm&&(Qi(this.$vm.$.uid),e=this.$vm,rl||(rl=il().$destroyComponent),rl(e))}}},initRelation:function(e,t){e.triggerEvent("__l",t)},isPage:function(e){return!!e.route},mocks:["__route__","__wxExparserNodeId__","__wxWebviewId__"]});const pl=function(e){return App(Vc(e))},dl=(hl=fl,function(e){return Component(sl(e,hl))});var hl;const gl=function(e){return function(t){return Component(nl(t,e))}}(fl),ml=function(e){Wc(Vc(e),e)},yl=function(e){const t=Vc(e),n=h(getApp)&&getApp({allowDefault:!0});if(!n)return;e.$.ctx.$scope=n;const o=n.globalData;o&&Object.keys(t.globalData).forEach((e=>{u(o,e)||(o[e]=t.globalData[e])})),Object.keys(t).forEach((e=>{u(n,e)||(n[e]=t[e])})),Wc(t,e)};wx.createApp=global.createApp=pl,wx.createPage=dl,wx.createComponent=gl,wx.createPluginApp=global.createPluginApp=ml,wx.createSubpackageApp=global.createSubpackageApp=yl;
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */
var vl="store";function bl(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function wl(e,t){if(!e)throw new Error("[vuex] "+t)}function xl(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function _l(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;Sl(e,n,[],e._modules.root,!0),Al(e,n,t)}function Al(e,t,n){var o=e._state,r=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var i=e._wrappedGetters,s={},a={},c=new We(!0);c.run((function(){bl(i,(function(t,n){s[n]=function(e,t){return function(){return e(t)}}(t,e),a[n]=ti((function(){return s[n]()})),Object.defineProperty(e.getters,n,{get:function(){return a[n].value},enumerable:!0})}))})),e._state=en({data:t}),e._scope=c,e.strict&&function(e){wo((function(){return e._state.data}),(function(){wl(e._committing,"do not mutate vuex store state outside mutation handlers.")}),{deep:!0,flush:"sync"})}(e),o&&n&&e._withCommit((function(){o.data=null})),r&&r.stop()}function Sl(e,t,n,o,r){var i=!n.length,s=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[s]&&console.error("[vuex] duplicate namespace "+s+" for the namespaced module "+n.join("/")),e._modulesNamespaceMap[s]=o),!i&&!r){var a=kl(t,n.slice(0,-1)),c=n[n.length-1];e._withCommit((function(){c in a&&console.warn('[vuex] state field "'+c+'" was overridden by a module with the same name at "'+n.join(".")+'"'),a[c]=o.state}))}var l=o.context=function(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var i=El(n,o,r),s=i.payload,a=i.options,c=i.type;if(a&&a.root||(c=t+c,e._actions[c]))return e.dispatch(c,s);console.error("[vuex] unknown local action type: "+i.type+", global type: "+c)},commit:o?e.commit:function(n,o,r){var i=El(n,o,r),s=i.payload,a=i.options,c=i.type;a&&a.root||(c=t+c,e._mutations[c])?e.commit(c,s,a):console.error("[vuex] unknown local mutation type: "+i.type+", global type: "+c)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return function(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var i=r.slice(o);Object.defineProperty(n,i,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}(e,t)}},state:{get:function(){return kl(e.state,n)}}}),r}(e,s,n);o.forEachMutation((function(t,n){!function(e,t,n,o){var r=e._mutations[t]||(e._mutations[t]=[]);r.push((function(t){n.call(e,o.state,t)}))}(e,s+n,t,l)})),o.forEachAction((function(t,n){var o=t.root?n:s+n,r=t.handler||t;!function(e,t,n,o){var r=e._actions[t]||(e._actions[t]=[]);r.push((function(t){var r,i=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return(r=i)&&"function"==typeof r.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,o,r,l)})),o.forEachGetter((function(t,n){!function(e,t,n,o){if(e._wrappedGetters[t])return void console.error("[vuex] duplicate getter key: "+t);e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)}}(e,s+n,t,l)})),o.forEachChild((function(o,i){Sl(e,t,n.concat(i),o,r)}))}function kl(e,t){return t.reduce((function(e,t){return e[t]}),e)}function El(e,t,n){var o;return null!==(o=e)&&"object"==typeof o&&e.type&&(n=t,t=e,e=e.type),wl("string"==typeof e,"expects string as the type, but found "+typeof e+"."),{type:e,payload:t,options:n}}var Pl=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"==typeof n?n():n)||{}},Il={namespaced:{configurable:!0}};Il.namespaced.get=function(){return!!this._rawModule.namespaced},Pl.prototype.addChild=function(e,t){this._children[e]=t},Pl.prototype.removeChild=function(e){delete this._children[e]},Pl.prototype.getChild=function(e){return this._children[e]},Pl.prototype.hasChild=function(e){return e in this._children},Pl.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},Pl.prototype.forEachChild=function(e){bl(this._children,e)},Pl.prototype.forEachGetter=function(e){this._rawModule.getters&&bl(this._rawModule.getters,e)},Pl.prototype.forEachAction=function(e){this._rawModule.actions&&bl(this._rawModule.actions,e)},Pl.prototype.forEachMutation=function(e){this._rawModule.mutations&&bl(this._rawModule.mutations,e)},Object.defineProperties(Pl.prototype,Il);var Cl=function(e){this.register([],e,!1)};function Tl(e,t,n){if(jl(e,n),t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return void console.warn("[vuex] trying to add a new module '"+o+"' on hot reloading, manual reload is needed");Tl(e.concat(o),t.getChild(o),n.modules[o])}}Cl.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},Cl.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return e+((t=t.getChild(n)).namespaced?n+"/":"")}),"")},Cl.prototype.update=function(e){Tl([],this.root,e)},Cl.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0),jl(e,t);var r=new Pl(t,n);0===e.length?this.root=r:this.get(e.slice(0,-1)).addChild(e[e.length-1],r);t.modules&&bl(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},Cl.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o?o.runtime&&t.removeChild(n):console.warn("[vuex] trying to unregister module '"+n+"', which is not registered")},Cl.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var Bl={assert:function(e){return"function"==typeof e},expected:"function"},Ol={getters:Bl,mutations:Bl,actions:{assert:function(e){return"function"==typeof e||"object"==typeof e&&"function"==typeof e.handler},expected:'function or object with "handler" function'}};function jl(e,t){Object.keys(Ol).forEach((function(n){if(t[n]){var o=Ol[n];bl(t[n],(function(t,r){wl(o.assert(t),function(e,t,n,o,r){var i=t+" should be "+r+' but "'+t+"."+n+'"';e.length>0&&(i+=' in module "'+e.join(".")+'"');return i+=" is "+JSON.stringify(o)+".",i}(e,n,r,t,o.expected))}))}}))}var Ll=function e(t){var n=this;void 0===t&&(t={}),wl("undefined"!=typeof Promise,"vuex requires a Promise polyfill in this browser."),wl(this instanceof e,"store must be called with the new operator.");var o=t.plugins;void 0===o&&(o=[]);var r=t.strict;void 0===r&&(r=!1);var i=t.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new Cl(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=i;var s=this,a=this.dispatch,c=this.commit;this.dispatch=function(e,t){return a.call(s,e,t)},this.commit=function(e,t,n){return c.call(s,e,t,n)},this.strict=r;var l=this._modules.root.state;Sl(this,l,[],this._modules.root),Al(this,l),o.forEach((function(e){return e(n)}))},Nl={state:{configurable:!0}};Ll.prototype.install=function(e,t){e.provide(t||vl,this),e.config.globalProperties.$store=this,void 0===this._devtools||this._devtools},Nl.state.get=function(){return this._state.data},Nl.state.set=function(e){wl(!1,"use store.replaceState() to explicit replace store state.")},Ll.prototype.commit=function(e,t,n){var o=this,r=El(e,t,n),i=r.type,s=r.payload,a=r.options,c={type:i,payload:s},l=this._mutations[i];l?(this._withCommit((function(){l.forEach((function(e){e(s)}))})),this._subscribers.slice().forEach((function(e){return e(c,o.state)})),a&&a.silent&&console.warn("[vuex] mutation type: "+i+". Silent option has been removed. Use the filter functionality in the vue-devtools")):console.error("[vuex] unknown mutation type: "+i)},Ll.prototype.dispatch=function(e,t){var n=this,o=El(e,t),r=o.type,i=o.payload,s={type:r,payload:i},a=this._actions[r];if(a){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(s,n.state)}))}catch(ah){console.warn("[vuex] error in before action subscribers: "),console.error(ah)}var c=a.length>1?Promise.all(a.map((function(e){return e(i)}))):a[0](i);return new Promise((function(e,t){c.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(s,n.state)}))}catch(ah){console.warn("[vuex] error in after action subscribers: "),console.error(ah)}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(s,n.state,e)}))}catch(ah){console.warn("[vuex] error in error action subscribers: "),console.error(ah)}t(e)}))}))}console.error("[vuex] unknown action type: "+r)},Ll.prototype.subscribe=function(e,t){return xl(e,this._subscribers,t)},Ll.prototype.subscribeAction=function(e,t){return xl("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},Ll.prototype.watch=function(e,t,n){var o=this;return wl("function"==typeof e,"store.watch only accepts a function."),wo((function(){return e(o.state,o.getters)}),t,Object.assign({},n))},Ll.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},Ll.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"==typeof e&&(e=[e]),wl(Array.isArray(e),"module path must be a string or an Array."),wl(e.length>0,"cannot register the root module by using registerModule."),this._modules.register(e,t),Sl(this,this.state,e,this._modules.get(e),n.preserveState),Al(this,this.state)},Ll.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),wl(Array.isArray(e),"module path must be a string or an Array."),this._modules.unregister(e),this._withCommit((function(){delete kl(t.state,e.slice(0,-1))[e[e.length-1]]})),_l(this)},Ll.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),wl(Array.isArray(e),"module path must be a string or an Array."),this._modules.isRegistered(e)},Ll.prototype.hotUpdate=function(e){this._modules.update(e),_l(this,!0)},Ll.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(Ll.prototype,Nl);var Rl="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Ml(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Fl(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var o=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,o.get?o:{enumerable:!0,get:function(){return e[t]}})})),n}var Ul={exports:{}};var Dl={exports:{}};const ql=new Proxy({},{get(e,t){throw new Error(`Module "" has been externalized for browser compatibility. Cannot access ".${t}" in client code.  See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)}}),Hl=Fl(Object.freeze(Object.defineProperty({__proto__:null,default:ql},Symbol.toStringTag,{value:"Module"})));var zl;function Ql(){return zl||(zl=1,Dl.exports=(e=e||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==Rl&&Rl.crypto&&(n=Rl.crypto),!n)try{n=Hl}catch(g){}var o=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(g){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(g){}}throw new Error("Native crypto module could not be used to get secure random number.")},r=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),i={},s=i.lib={},a=s.Base=function(){return{extend:function(e){var t=r(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),c=s.WordArray=a.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,o=this.sigBytes,r=e.sigBytes;if(this.clamp(),o%4)for(var i=0;i<r;i++){var s=n[i>>>2]>>>24-i%4*8&255;t[o+i>>>2]|=s<<24-(o+i)%4*8}else for(var a=0;a<r;a+=4)t[o+a>>>2]=n[a>>>2];return this.sigBytes+=r,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(o());return new c.init(t,e)}}),l=i.enc={},u=l.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;o.push((i>>>4).toString(16)),o.push((15&i).toString(16))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o+=2)n[o>>>3]|=parseInt(e.substr(o,2),16)<<24-o%8*4;return new c.init(n,t/2)}},f=l.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;o.push(String.fromCharCode(i))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>2]|=(255&e.charCodeAt(o))<<24-o%4*8;return new c.init(n,t)}},p=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(ah){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},d=s.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=p.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,o=this._data,r=o.words,i=o.sigBytes,s=this.blockSize,a=i/(4*s),l=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,u=e.min(4*l,i);if(l){for(var f=0;f<l;f+=s)this._doProcessBlock(r,f);n=r.splice(0,l),o.sigBytes-=u}return new c.init(n,u)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});s.Hasher=d.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=i.algo={};return i}(Math),e)),Dl.exports;var e}var $l,Vl={exports:{}};function Wl(){return $l?Vl.exports:($l=1,Vl.exports=(s=Ql(),n=(t=s).lib,o=n.Base,r=n.WordArray,(i=t.x64={}).Word=o.extend({init:function(e,t){this.high=e,this.low=t}}),i.WordArray=o.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:8*t.length},toX32:function(){for(var e=this.words,t=e.length,n=[],o=0;o<t;o++){var i=e[o];n.push(i.high),n.push(i.low)}return r.create(n,this.sigBytes)},clone:function(){for(var e=o.clone.call(this),t=e.words=this.words.slice(0),n=t.length,r=0;r<n;r++)t[r]=t[r].clone();return e}}),s));var e,t,n,o,r,i,s}var Kl,Jl={exports:{}};function Xl(){return Kl||(Kl=1,Jl.exports=(e=Ql(),function(){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,n=t.init,o=t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,o=[],r=0;r<t;r++)o[r>>>2]|=e[r]<<24-r%4*8;n.call(this,o,t)}else n.apply(this,arguments)};o.prototype=t}}(),e.lib.WordArray)),Jl.exports;var e}var Gl,Yl={exports:{}};function Zl(){return Gl?Yl.exports:(Gl=1,Yl.exports=(e=Ql(),function(){var t=e,n=t.lib.WordArray,o=t.enc;function r(e){return e<<8&4278255360|e>>>8&16711935}o.Utf16=o.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r+=2){var i=t[r>>>2]>>>16-r%4*8&65535;o.push(String.fromCharCode(i))}return o.join("")},parse:function(e){for(var t=e.length,o=[],r=0;r<t;r++)o[r>>>1]|=e.charCodeAt(r)<<16-r%2*16;return n.create(o,2*t)}},o.Utf16LE={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],i=0;i<n;i+=2){var s=r(t[i>>>2]>>>16-i%4*8&65535);o.push(String.fromCharCode(s))}return o.join("")},parse:function(e){for(var t=e.length,o=[],i=0;i<t;i++)o[i>>>1]|=r(e.charCodeAt(i)<<16-i%2*16);return n.create(o,2*t)}}}(),e.enc.Utf16));var e}var eu,tu={exports:{}};function nu(){return eu?tu.exports:(eu=1,tu.exports=(e=Ql(),function(){var t=e,n=t.lib.WordArray;function o(e,t,o){for(var r=[],i=0,s=0;s<t;s++)if(s%4){var a=o[e.charCodeAt(s-1)]<<s%4*2|o[e.charCodeAt(s)]>>>6-s%4*2;r[i>>>2]|=a<<24-i%4*8,i++}return n.create(r,i)}t.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,o=this._map;e.clamp();for(var r=[],i=0;i<n;i+=3)for(var s=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,a=0;a<4&&i+.75*a<n;a++)r.push(o.charAt(s>>>6*(3-a)&63));var c=o.charAt(64);if(c)for(;r.length%4;)r.push(c);return r.join("")},parse:function(e){var t=e.length,n=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var i=0;i<n.length;i++)r[n.charCodeAt(i)]=i}var s=n.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return o(e,t,r)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64));var e}var ou,ru={exports:{}};function iu(){return ou?ru.exports:(ou=1,ru.exports=(e=Ql(),function(){var t=e,n=t.lib.WordArray;function o(e,t,o){for(var r=[],i=0,s=0;s<t;s++)if(s%4){var a=o[e.charCodeAt(s-1)]<<s%4*2|o[e.charCodeAt(s)]>>>6-s%4*2;r[i>>>2]|=a<<24-i%4*8,i++}return n.create(r,i)}t.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var n=e.words,o=e.sigBytes,r=t?this._safe_map:this._map;e.clamp();for(var i=[],s=0;s<o;s+=3)for(var a=(n[s>>>2]>>>24-s%4*8&255)<<16|(n[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|n[s+2>>>2]>>>24-(s+2)%4*8&255,c=0;c<4&&s+.75*c<o;c++)i.push(r.charAt(a>>>6*(3-c)&63));var l=r.charAt(64);if(l)for(;i.length%4;)i.push(l);return i.join("")},parse:function(e,t){void 0===t&&(t=!0);var n=e.length,r=t?this._safe_map:this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var s=0;s<r.length;s++)i[r.charCodeAt(s)]=s}var a=r.charAt(64);if(a){var c=e.indexOf(a);-1!==c&&(n=c)}return o(e,n,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),e.enc.Base64url));var e}var su,au={exports:{}};function cu(){return su?au.exports:(su=1,au.exports=(e=Ql(),function(t){var n=e,o=n.lib,r=o.WordArray,i=o.Hasher,s=n.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var c=s.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var o=t+n,r=e[o];e[o]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var i=this._hash.words,s=e[t+0],c=e[t+1],d=e[t+2],h=e[t+3],g=e[t+4],m=e[t+5],y=e[t+6],v=e[t+7],b=e[t+8],w=e[t+9],x=e[t+10],_=e[t+11],A=e[t+12],S=e[t+13],k=e[t+14],E=e[t+15],P=i[0],I=i[1],C=i[2],T=i[3];P=l(P,I,C,T,s,7,a[0]),T=l(T,P,I,C,c,12,a[1]),C=l(C,T,P,I,d,17,a[2]),I=l(I,C,T,P,h,22,a[3]),P=l(P,I,C,T,g,7,a[4]),T=l(T,P,I,C,m,12,a[5]),C=l(C,T,P,I,y,17,a[6]),I=l(I,C,T,P,v,22,a[7]),P=l(P,I,C,T,b,7,a[8]),T=l(T,P,I,C,w,12,a[9]),C=l(C,T,P,I,x,17,a[10]),I=l(I,C,T,P,_,22,a[11]),P=l(P,I,C,T,A,7,a[12]),T=l(T,P,I,C,S,12,a[13]),C=l(C,T,P,I,k,17,a[14]),P=u(P,I=l(I,C,T,P,E,22,a[15]),C,T,c,5,a[16]),T=u(T,P,I,C,y,9,a[17]),C=u(C,T,P,I,_,14,a[18]),I=u(I,C,T,P,s,20,a[19]),P=u(P,I,C,T,m,5,a[20]),T=u(T,P,I,C,x,9,a[21]),C=u(C,T,P,I,E,14,a[22]),I=u(I,C,T,P,g,20,a[23]),P=u(P,I,C,T,w,5,a[24]),T=u(T,P,I,C,k,9,a[25]),C=u(C,T,P,I,h,14,a[26]),I=u(I,C,T,P,b,20,a[27]),P=u(P,I,C,T,S,5,a[28]),T=u(T,P,I,C,d,9,a[29]),C=u(C,T,P,I,v,14,a[30]),P=f(P,I=u(I,C,T,P,A,20,a[31]),C,T,m,4,a[32]),T=f(T,P,I,C,b,11,a[33]),C=f(C,T,P,I,_,16,a[34]),I=f(I,C,T,P,k,23,a[35]),P=f(P,I,C,T,c,4,a[36]),T=f(T,P,I,C,g,11,a[37]),C=f(C,T,P,I,v,16,a[38]),I=f(I,C,T,P,x,23,a[39]),P=f(P,I,C,T,S,4,a[40]),T=f(T,P,I,C,s,11,a[41]),C=f(C,T,P,I,h,16,a[42]),I=f(I,C,T,P,y,23,a[43]),P=f(P,I,C,T,w,4,a[44]),T=f(T,P,I,C,A,11,a[45]),C=f(C,T,P,I,E,16,a[46]),P=p(P,I=f(I,C,T,P,d,23,a[47]),C,T,s,6,a[48]),T=p(T,P,I,C,v,10,a[49]),C=p(C,T,P,I,k,15,a[50]),I=p(I,C,T,P,m,21,a[51]),P=p(P,I,C,T,A,6,a[52]),T=p(T,P,I,C,h,10,a[53]),C=p(C,T,P,I,x,15,a[54]),I=p(I,C,T,P,c,21,a[55]),P=p(P,I,C,T,b,6,a[56]),T=p(T,P,I,C,E,10,a[57]),C=p(C,T,P,I,y,15,a[58]),I=p(I,C,T,P,S,21,a[59]),P=p(P,I,C,T,g,6,a[60]),T=p(T,P,I,C,_,10,a[61]),C=p(C,T,P,I,d,15,a[62]),I=p(I,C,T,P,w,21,a[63]),i[0]=i[0]+P|0,i[1]=i[1]+I|0,i[2]=i[2]+C|0,i[3]=i[3]+T|0},_doFinalize:function(){var e=this._data,n=e.words,o=8*this._nDataBytes,r=8*e.sigBytes;n[r>>>5]|=128<<24-r%32;var i=t.floor(o/4294967296),s=o;n[15+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(r+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,o,r,i,s){var a=e+(t&n|~t&o)+r+s;return(a<<i|a>>>32-i)+t}function u(e,t,n,o,r,i,s){var a=e+(t&o|n&~o)+r+s;return(a<<i|a>>>32-i)+t}function f(e,t,n,o,r,i,s){var a=e+(t^n^o)+r+s;return(a<<i|a>>>32-i)+t}function p(e,t,n,o,r,i,s){var a=e+(n^(t|~o))+r+s;return(a<<i|a>>>32-i)+t}n.MD5=i._createHelper(c),n.HmacMD5=i._createHmacHelper(c)}(Math),e.MD5));var e}var lu,uu={exports:{}};function fu(){return lu?uu.exports:(lu=1,uu.exports=(a=Ql(),t=(e=a).lib,n=t.WordArray,o=t.Hasher,r=e.algo,i=[],s=r.SHA1=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],r=n[1],s=n[2],a=n[3],c=n[4],l=0;l<80;l++){if(l<16)i[l]=0|e[t+l];else{var u=i[l-3]^i[l-8]^i[l-14]^i[l-16];i[l]=u<<1|u>>>31}var f=(o<<5|o>>>27)+c+i[l];f+=l<20?1518500249+(r&s|~r&a):l<40?1859775393+(r^s^a):l<60?(r&s|r&a|s&a)-1894007588:(r^s^a)-899497514,c=a,a=s,s=r<<30|r>>>2,r=o,o=f}n[0]=n[0]+o|0,n[1]=n[1]+r|0,n[2]=n[2]+s|0,n[3]=n[3]+a|0,n[4]=n[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[14+(o+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(o+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=o._createHelper(s),e.HmacSHA1=o._createHmacHelper(s),a.SHA1));var e,t,n,o,r,i,s,a}var pu,du={exports:{}};function hu(){return pu?du.exports:(pu=1,du.exports=(e=Ql(),function(t){var n=e,o=n.lib,r=o.WordArray,i=o.Hasher,s=n.algo,a=[],c=[];!function(){function e(e){for(var n=t.sqrt(e),o=2;o<=n;o++)if(!(e%o))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var o=2,r=0;r<64;)e(o)&&(r<8&&(a[r]=n(t.pow(o,.5))),c[r]=n(t.pow(o,1/3)),r++),o++}();var l=[],u=s.SHA256=i.extend({_doReset:function(){this._hash=new r.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],r=n[1],i=n[2],s=n[3],a=n[4],u=n[5],f=n[6],p=n[7],d=0;d<64;d++){if(d<16)l[d]=0|e[t+d];else{var h=l[d-15],g=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,m=l[d-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[d]=g+l[d-7]+y+l[d-16]}var v=o&r^o&i^r&i,b=(o<<30|o>>>2)^(o<<19|o>>>13)^(o<<10|o>>>22),w=p+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&f)+c[d]+l[d];p=f,f=u,u=a,a=s+w|0,s=i,i=r,r=o,o=w+(b+v)|0}n[0]=n[0]+o|0,n[1]=n[1]+r|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+f|0,n[7]=n[7]+p|0},_doFinalize:function(){var e=this._data,n=e.words,o=8*this._nDataBytes,r=8*e.sigBytes;return n[r>>>5]|=128<<24-r%32,n[14+(r+64>>>9<<4)]=t.floor(o/4294967296),n[15+(r+64>>>9<<4)]=o,e.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});n.SHA256=i._createHelper(u),n.HmacSHA256=i._createHmacHelper(u)}(Math),e.SHA256));var e}var gu,mu={exports:{}};var yu,vu={exports:{}};function bu(){return yu||(yu=1,vu.exports=(e=Ql(),Wl(),function(){var t=e,n=t.lib.Hasher,o=t.x64,r=o.Word,i=o.WordArray,s=t.algo;function a(){return r.create.apply(r,arguments)}var c=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],l=[];!function(){for(var e=0;e<80;e++)l[e]=a()}();var u=s.SHA512=n.extend({_doReset:function(){this._hash=new i.init([new r.init(1779033703,4089235720),new r.init(3144134277,2227873595),new r.init(1013904242,4271175723),new r.init(2773480762,1595750129),new r.init(1359893119,2917565137),new r.init(2600822924,725511199),new r.init(528734635,4215389547),new r.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],r=n[1],i=n[2],s=n[3],a=n[4],u=n[5],f=n[6],p=n[7],d=o.high,h=o.low,g=r.high,m=r.low,y=i.high,v=i.low,b=s.high,w=s.low,x=a.high,_=a.low,A=u.high,S=u.low,k=f.high,E=f.low,P=p.high,I=p.low,C=d,T=h,B=g,O=m,j=y,L=v,N=b,R=w,M=x,F=_,U=A,D=S,q=k,H=E,z=P,Q=I,$=0;$<80;$++){var V,W,K=l[$];if($<16)W=K.high=0|e[t+2*$],V=K.low=0|e[t+2*$+1];else{var J=l[$-15],X=J.high,G=J.low,Y=(X>>>1|G<<31)^(X>>>8|G<<24)^X>>>7,Z=(G>>>1|X<<31)^(G>>>8|X<<24)^(G>>>7|X<<25),ee=l[$-2],te=ee.high,ne=ee.low,oe=(te>>>19|ne<<13)^(te<<3|ne>>>29)^te>>>6,re=(ne>>>19|te<<13)^(ne<<3|te>>>29)^(ne>>>6|te<<26),ie=l[$-7],se=ie.high,ae=ie.low,ce=l[$-16],le=ce.high,ue=ce.low;W=(W=(W=Y+se+((V=Z+ae)>>>0<Z>>>0?1:0))+oe+((V+=re)>>>0<re>>>0?1:0))+le+((V+=ue)>>>0<ue>>>0?1:0),K.high=W,K.low=V}var fe,pe=M&U^~M&q,de=F&D^~F&H,he=C&B^C&j^B&j,ge=T&O^T&L^O&L,me=(C>>>28|T<<4)^(C<<30|T>>>2)^(C<<25|T>>>7),ye=(T>>>28|C<<4)^(T<<30|C>>>2)^(T<<25|C>>>7),ve=(M>>>14|F<<18)^(M>>>18|F<<14)^(M<<23|F>>>9),be=(F>>>14|M<<18)^(F>>>18|M<<14)^(F<<23|M>>>9),we=c[$],xe=we.high,_e=we.low,Ae=z+ve+((fe=Q+be)>>>0<Q>>>0?1:0),Se=ye+ge;z=q,Q=H,q=U,H=D,U=M,D=F,M=N+(Ae=(Ae=(Ae=Ae+pe+((fe+=de)>>>0<de>>>0?1:0))+xe+((fe+=_e)>>>0<_e>>>0?1:0))+W+((fe+=V)>>>0<V>>>0?1:0))+((F=R+fe|0)>>>0<R>>>0?1:0)|0,N=j,R=L,j=B,L=O,B=C,O=T,C=Ae+(me+he+(Se>>>0<ye>>>0?1:0))+((T=fe+Se|0)>>>0<fe>>>0?1:0)|0}h=o.low=h+T,o.high=d+C+(h>>>0<T>>>0?1:0),m=r.low=m+O,r.high=g+B+(m>>>0<O>>>0?1:0),v=i.low=v+L,i.high=y+j+(v>>>0<L>>>0?1:0),w=s.low=w+R,s.high=b+N+(w>>>0<R>>>0?1:0),_=a.low=_+F,a.high=x+M+(_>>>0<F>>>0?1:0),S=u.low=S+D,u.high=A+U+(S>>>0<D>>>0?1:0),E=f.low=E+H,f.high=k+q+(E>>>0<H>>>0?1:0),I=p.low=I+Q,p.high=P+z+(I>>>0<Q>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[30+(o+128>>>10<<5)]=Math.floor(n/4294967296),t[31+(o+128>>>10<<5)]=n,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=n._createHelper(u),t.HmacSHA512=n._createHmacHelper(u)}(),e.SHA512)),vu.exports;var e}var wu,xu={exports:{}};var _u,Au={exports:{}};function Su(){return _u?Au.exports:(_u=1,Au.exports=(e=Ql(),Wl(),function(t){var n=e,o=n.lib,r=o.WordArray,i=o.Hasher,s=n.x64.Word,a=n.algo,c=[],l=[],u=[];!function(){for(var e=1,t=0,n=0;n<24;n++){c[e+5*t]=(n+1)*(n+2)/2%64;var o=(2*e+3*t)%5;e=t%5,t=o}for(e=0;e<5;e++)for(t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var r=1,i=0;i<24;i++){for(var a=0,f=0,p=0;p<7;p++){if(1&r){var d=(1<<p)-1;d<32?f^=1<<d:a^=1<<d-32}128&r?r=r<<1^113:r<<=1}u[i]=s.create(a,f)}}();var f=[];!function(){for(var e=0;e<25;e++)f[e]=s.create()}();var p=a.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,o=this.blockSize/2,r=0;r<o;r++){var i=e[t+2*r],s=e[t+2*r+1];i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(I=n[r]).high^=s,I.low^=i}for(var a=0;a<24;a++){for(var p=0;p<5;p++){for(var d=0,h=0,g=0;g<5;g++)d^=(I=n[p+5*g]).high,h^=I.low;var m=f[p];m.high=d,m.low=h}for(p=0;p<5;p++){var y=f[(p+4)%5],v=f[(p+1)%5],b=v.high,w=v.low;for(d=y.high^(b<<1|w>>>31),h=y.low^(w<<1|b>>>31),g=0;g<5;g++)(I=n[p+5*g]).high^=d,I.low^=h}for(var x=1;x<25;x++){var _=(I=n[x]).high,A=I.low,S=c[x];S<32?(d=_<<S|A>>>32-S,h=A<<S|_>>>32-S):(d=A<<S-32|_>>>64-S,h=_<<S-32|A>>>64-S);var k=f[l[x]];k.high=d,k.low=h}var E=f[0],P=n[0];for(E.high=P.high,E.low=P.low,p=0;p<5;p++)for(g=0;g<5;g++){var I=n[x=p+5*g],C=f[x],T=f[(p+1)%5+5*g],B=f[(p+2)%5+5*g];I.high=C.high^~T.high&B.high,I.low=C.low^~T.low&B.low}I=n[0];var O=u[a];I.high^=O.high,I.low^=O.low}},_doFinalize:function(){var e=this._data,n=e.words;this._nDataBytes;var o=8*e.sigBytes,i=32*this.blockSize;n[o>>>5]|=1<<24-o%32,n[(t.ceil((o+1)/i)*i>>>5)-1]|=128,e.sigBytes=4*n.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,c=a/8,l=[],u=0;u<c;u++){var f=s[u],p=f.high,d=f.low;p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),l.push(d),l.push(p)}return new r.init(l,a)},clone:function(){for(var e=i.clone.call(this),t=e._state=this._state.slice(0),n=0;n<25;n++)t[n]=t[n].clone();return e}});n.SHA3=i._createHelper(p),n.HmacSHA3=i._createHmacHelper(p)}(Math),e.SHA3));var e}var ku,Eu={exports:{}};var Pu,Iu={exports:{}};function Cu(){return Pu?Iu.exports:(Pu=1,Iu.exports=(e=Ql(),n=(t=e).lib.Base,o=t.enc.Utf8,void(t.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),s=this._iKey=t.clone(),a=i.words,c=s.words,l=0;l<n;l++)a[l]^=1549556828,c[l]^=909522486;i.sigBytes=s.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}}))));var e,t,n,o}var Tu,Bu={exports:{}};var Ou,ju={exports:{}};function Lu(){return Ou?ju.exports:(Ou=1,ju.exports=(a=Ql(),fu(),Cu(),t=(e=a).lib,n=t.Base,o=t.WordArray,r=e.algo,i=r.MD5,s=r.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:i,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,r=this.cfg,i=r.hasher.create(),s=o.create(),a=s.words,c=r.keySize,l=r.iterations;a.length<c;){n&&i.update(n),n=i.update(e).finalize(t),i.reset();for(var u=1;u<l;u++)n=i.finalize(n),i.reset();s.concat(n)}return s.sigBytes=4*c,s}}),e.EvpKDF=function(e,t,n){return s.create(n).compute(e,t)},a.EvpKDF));var e,t,n,o,r,i,s,a}var Nu,Ru={exports:{}};function Mu(){return Nu?Ru.exports:(Nu=1,Ru.exports=(e=Ql(),Lu(),void(e.lib.Cipher||function(t){var n=e,o=n.lib,r=o.Base,i=o.WordArray,s=o.BufferedBlockAlgorithm,a=n.enc;a.Utf8;var c=a.Base64,l=n.algo.EvpKDF,u=o.Cipher=s.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:y}return function(t){return{encrypt:function(n,o,r){return e(o).encrypt(t,n,o,r)},decrypt:function(n,o,r){return e(o).decrypt(t,n,o,r)}}}}()});o.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var f=n.mode={},p=o.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),d=f.CBC=function(){var e=p.extend();function n(e,n,o){var r,i=this._iv;i?(r=i,this._iv=t):r=this._prevBlock;for(var s=0;s<o;s++)e[n+s]^=r[s]}return e.Encryptor=e.extend({processBlock:function(e,t){var o=this._cipher,r=o.blockSize;n.call(this,e,t,r),o.encryptBlock(e,t),this._prevBlock=e.slice(t,t+r)}}),e.Decryptor=e.extend({processBlock:function(e,t){var o=this._cipher,r=o.blockSize,i=e.slice(t,t+r);o.decryptBlock(e,t),n.call(this,e,t,r),this._prevBlock=i}}),e}(),h=(n.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,o=n-e.sigBytes%n,r=o<<24|o<<16|o<<8|o,s=[],a=0;a<o;a+=4)s.push(r);var c=i.create(s,o);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};o.BlockCipher=u.extend({cfg:u.cfg.extend({mode:d,padding:h}),reset:function(){var e;u.reset.call(this);var t=this.cfg,n=t.iv,o=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=o.createEncryptor:(e=o.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(o,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var g=o.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),m=(n.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?i.create([1398893684,1701076831]).concat(n).concat(t):t).toString(c)},parse:function(e){var t,n=c.parse(e),o=n.words;return 1398893684==o[0]&&1701076831==o[1]&&(t=i.create(o.slice(2,4)),o.splice(0,4),n.sigBytes-=16),g.create({ciphertext:n,salt:t})}},y=o.SerializableCipher=r.extend({cfg:r.extend({format:m}),encrypt:function(e,t,n,o){o=this.cfg.extend(o);var r=e.createEncryptor(n,o),i=r.finalize(t),s=r.cfg;return g.create({ciphertext:i,key:n,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:o.format})},decrypt:function(e,t,n,o){return o=this.cfg.extend(o),t=this._parse(t,o.format),e.createDecryptor(n,o).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),v=(n.kdf={}).OpenSSL={execute:function(e,t,n,o,r){if(o||(o=i.random(8)),r)s=l.create({keySize:t+n,hasher:r}).compute(e,o);else var s=l.create({keySize:t+n}).compute(e,o);var a=i.create(s.words.slice(t),4*n);return s.sigBytes=4*t,g.create({key:s,iv:a,salt:o})}},b=o.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:v}),encrypt:function(e,t,n,o){var r=(o=this.cfg.extend(o)).kdf.execute(n,e.keySize,e.ivSize,o.salt,o.hasher);o.iv=r.iv;var i=y.encrypt.call(this,e,t,r.key,o);return i.mixIn(r),i},decrypt:function(e,t,n,o){o=this.cfg.extend(o),t=this._parse(t,o.format);var r=o.kdf.execute(n,e.keySize,e.ivSize,t.salt,o.hasher);return o.iv=r.iv,y.decrypt.call(this,e,t,r.key,o)}})}())));var e}var Fu,Uu={exports:{}};var Du,qu={exports:{}};var Hu,zu={exports:{}};function Qu(){return Hu?zu.exports:(Hu=1,zu.exports=(e=Ql(),Mu(),
/** @preserve
       * Counter block mode compatible with  Dr Brian Gladman fileenc.c
       * derived from CryptoJS.mode.CTR
       * <NAME_EMAIL>
       */
e.mode.CTRGladman=function(){var t=e.lib.BlockCipherMode.extend();function n(e){if(255&~(e>>24))e+=1<<24;else{var t=e>>16&255,n=e>>8&255,o=255&e;255===t?(t=0,255===n?(n=0,255===o?o=0:++o):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=o}return e}function o(e){return 0===(e[0]=n(e[0]))&&(e[1]=n(e[1])),e}var r=t.Encryptor=t.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,i=this._iv,s=this._counter;i&&(s=this._counter=i.slice(0),this._iv=void 0),o(s);var a=s.slice(0);n.encryptBlock(a,0);for(var c=0;c<r;c++)e[t+c]^=a[c]}});return t.Decryptor=r,t}(),e.mode.CTRGladman));var e}var $u,Vu={exports:{}};var Wu,Ku={exports:{}};var Ju,Xu={exports:{}};var Gu,Yu={exports:{}};var Zu,ef={exports:{}};var tf,nf={exports:{}};var of,rf={exports:{}};var sf,af={exports:{}};var cf,lf={exports:{}};var uf,ff={exports:{}};function pf(){return uf?ff.exports:(uf=1,ff.exports=(e=Ql(),nu(),cu(),Lu(),Mu(),function(){var t=e,n=t.lib,o=n.WordArray,r=n.BlockCipher,i=t.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=i.DES=r.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var o=s[n]-1;t[n]=e[o>>>5]>>>31-o%32&1}for(var r=this._subKeys=[],i=0;i<16;i++){var l=r[i]=[],u=c[i];for(n=0;n<24;n++)l[n/6|0]|=t[(a[n]-1+u)%28]<<31-n%6,l[4+(n/6|0)]|=t[28+(a[n+24]-1+u)%28]<<31-n%6;for(l[0]=l[0]<<1|l[0]>>>31,n=1;n<7;n++)l[n]=l[n]>>>4*(n-1)+3;l[7]=l[7]<<5|l[7]>>>27}var f=this._invSubKeys=[];for(n=0;n<16;n++)f[n]=r[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],p.call(this,4,252645135),p.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),p.call(this,1,1431655765);for(var o=0;o<16;o++){for(var r=n[o],i=this._lBlock,s=this._rBlock,a=0,c=0;c<8;c++)a|=l[c][((s^r[c])&u[c])>>>0];this._lBlock=s,this._rBlock=i^a}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,p.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),p.call(this,16,65535),p.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function p(e,t){var n=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=n,this._lBlock^=n<<e}function d(e,t){var n=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=n,this._rBlock^=n<<e}t.DES=r._createHelper(f);var h=i.TripleDES=r.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),n=e.length<4?e.slice(0,2):e.slice(2,4),r=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=f.createEncryptor(o.create(t)),this._des2=f.createEncryptor(o.create(n)),this._des3=f.createEncryptor(o.create(r))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=r._createHelper(h)}(),e.TripleDES));var e}var df,hf={exports:{}};var gf,mf={exports:{}};var yf,vf={exports:{}};var bf,wf,xf,_f,Af,Sf,kf,Ef={exports:{}};function Pf(){return bf?Ef.exports:(bf=1,Ef.exports=(e=Ql(),nu(),cu(),Lu(),Mu(),function(){var t=e,n=t.lib.BlockCipher,o=t.algo;const r=16,i=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],s=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var a={pbox:[],sbox:[]};function c(e,t){let n=t>>24&255,o=t>>16&255,r=t>>8&255,i=255&t,s=e.sbox[0][n]+e.sbox[1][o];return s^=e.sbox[2][r],s+=e.sbox[3][i],s}function l(e,t,n){let o,i=t,s=n;for(let a=0;a<r;++a)i^=e.pbox[a],s=c(e,i)^s,o=i,i=s,s=o;return o=i,i=s,s=o,s^=e.pbox[r],i^=e.pbox[r+1],{left:i,right:s}}function u(e,t,n){let o,i=t,s=n;for(let a=r+1;a>1;--a)i^=e.pbox[a],s=c(e,i)^s,o=i,i=s,s=o;return o=i,i=s,s=o,s^=e.pbox[1],i^=e.pbox[0],{left:i,right:s}}function f(e,t,n){for(let r=0;r<4;r++){e.sbox[r]=[];for(let t=0;t<256;t++)e.sbox[r][t]=s[r][t]}let o=0;for(let s=0;s<r+2;s++)e.pbox[s]=i[s]^t[o],o++,o>=n&&(o=0);let a=0,c=0,u=0;for(let i=0;i<r+2;i+=2)u=l(e,a,c),a=u.left,c=u.right,e.pbox[i]=a,e.pbox[i+1]=c;for(let r=0;r<4;r++)for(let t=0;t<256;t+=2)u=l(e,a,c),a=u.left,c=u.right,e.sbox[r][t]=a,e.sbox[r][t+1]=c;return!0}var p=o.Blowfish=n.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4;f(a,t,n)}},encryptBlock:function(e,t){var n=l(a,e[t],e[t+1]);e[t]=n.left,e[t+1]=n.right},decryptBlock:function(e,t){var n=u(a,e[t],e[t+1]);e[t]=n.left,e[t+1]=n.right},blockSize:2,keySize:4,ivSize:2});t.Blowfish=n._createHelper(p)}(),e.Blowfish));var e}const If=Ml(Ul.exports=function(e){return e}(Ql(),Wl(),Xl(),Zl(),nu(),iu(),cu(),fu(),hu(),gu||(gu=1,mu.exports=(kf=Ql(),hu(),xf=(wf=kf).lib.WordArray,_f=wf.algo,Af=_f.SHA256,Sf=_f.SHA224=Af.extend({_doReset:function(){this._hash=new xf.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=Af._doFinalize.call(this);return e.sigBytes-=4,e}}),wf.SHA224=Af._createHelper(Sf),wf.HmacSHA224=Af._createHmacHelper(Sf),kf.SHA224)),bu(),function(){return wu?xu.exports:(wu=1,xu.exports=(a=Ql(),Wl(),bu(),t=(e=a).x64,n=t.Word,o=t.WordArray,r=e.algo,i=r.SHA512,s=r.SHA384=i.extend({_doReset:function(){this._hash=new o.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=16,e}}),e.SHA384=i._createHelper(s),e.HmacSHA384=i._createHmacHelper(s),a.SHA384));var e,t,n,o,r,i,s,a}(),Su(),function(){return ku?Eu.exports:(ku=1,Eu.exports=(e=Ql(),
/** @preserve
      			(c) 2012 by Cédric Mesnil. All rights reserved.
      
      			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
      
      			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
      			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
      
      			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      			*/
function(){var t=e,n=t.lib,o=n.WordArray,r=n.Hasher,i=t.algo,s=o.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),a=o.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=o.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=o.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=o.create([0,1518500249,1859775393,2400959708,2840853838]),f=o.create([1352829926,1548603684,1836072691,2053994217,0]),p=i.RIPEMD160=r.extend({_doReset:function(){this._hash=o.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var o=t+n,r=e[o];e[o]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var i,p,b,w,x,_,A,S,k,E,P,I=this._hash.words,C=u.words,T=f.words,B=s.words,O=a.words,j=c.words,L=l.words;for(_=i=I[0],A=p=I[1],S=b=I[2],k=w=I[3],E=x=I[4],n=0;n<80;n+=1)P=i+e[t+B[n]]|0,P+=n<16?d(p,b,w)+C[0]:n<32?h(p,b,w)+C[1]:n<48?g(p,b,w)+C[2]:n<64?m(p,b,w)+C[3]:y(p,b,w)+C[4],P=(P=v(P|=0,j[n]))+x|0,i=x,x=w,w=v(b,10),b=p,p=P,P=_+e[t+O[n]]|0,P+=n<16?y(A,S,k)+T[0]:n<32?m(A,S,k)+T[1]:n<48?g(A,S,k)+T[2]:n<64?h(A,S,k)+T[3]:d(A,S,k)+T[4],P=(P=v(P|=0,L[n]))+E|0,_=E,E=k,k=v(S,10),S=A,A=P;P=I[1]+b+k|0,I[1]=I[2]+w+E|0,I[2]=I[3]+x+_|0,I[3]=I[4]+i+A|0,I[4]=I[0]+p+S|0,I[0]=P},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;t[o>>>5]|=128<<24-o%32,t[14+(o+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var r=this._hash,i=r.words,s=0;s<5;s++){var a=i[s];i[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return r},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});function d(e,t,n){return e^t^n}function h(e,t,n){return e&t|~e&n}function g(e,t,n){return(e|~t)^n}function m(e,t,n){return e&n|t&~n}function y(e,t,n){return e^(t|~n)}function v(e,t){return e<<t|e>>>32-t}t.RIPEMD160=r._createHelper(p),t.HmacRIPEMD160=r._createHmacHelper(p)}(),e.RIPEMD160));var e}(),Cu(),function(){return Tu?Bu.exports:(Tu=1,Bu.exports=(c=Ql(),hu(),Cu(),t=(e=c).lib,n=t.Base,o=t.WordArray,r=e.algo,i=r.SHA256,s=r.HMAC,a=r.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:i,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,r=s.create(n.hasher,e),i=o.create(),a=o.create([1]),c=i.words,l=a.words,u=n.keySize,f=n.iterations;c.length<u;){var p=r.update(t).finalize(a);r.reset();for(var d=p.words,h=d.length,g=p,m=1;m<f;m++){g=r.finalize(g),r.reset();for(var y=g.words,v=0;v<h;v++)d[v]^=y[v]}i.concat(p),l[0]++}return i.sigBytes=4*u,i}}),e.PBKDF2=function(e,t,n){return a.create(n).compute(e,t)},c.PBKDF2));var e,t,n,o,r,i,s,a,c}(),Lu(),Mu(),function(){return Fu?Uu.exports:(Fu=1,Uu.exports=(e=Ql(),Mu(),e.mode.CFB=function(){var t=e.lib.BlockCipherMode.extend();function n(e,t,n,o){var r,i=this._iv;i?(r=i.slice(0),this._iv=void 0):r=this._prevBlock,o.encryptBlock(r,0);for(var s=0;s<n;s++)e[t+s]^=r[s]}return t.Encryptor=t.extend({processBlock:function(e,t){var o=this._cipher,r=o.blockSize;n.call(this,e,t,r,o),this._prevBlock=e.slice(t,t+r)}}),t.Decryptor=t.extend({processBlock:function(e,t){var o=this._cipher,r=o.blockSize,i=e.slice(t,t+r);n.call(this,e,t,r,o),this._prevBlock=i}}),t}(),e.mode.CFB));var e}(),function(){return Du?qu.exports:(Du=1,qu.exports=(n=Ql(),Mu(),n.mode.CTR=(e=n.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize,r=this._iv,i=this._counter;r&&(i=this._counter=r.slice(0),this._iv=void 0);var s=i.slice(0);n.encryptBlock(s,0),i[o-1]=i[o-1]+1|0;for(var a=0;a<o;a++)e[t+a]^=s[a]}}),e.Decryptor=t,e),n.mode.CTR));var e,t,n}(),Qu(),function(){return $u?Vu.exports:($u=1,Vu.exports=(n=Ql(),Mu(),n.mode.OFB=(e=n.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize,r=this._iv,i=this._keystream;r&&(i=this._keystream=r.slice(0),this._iv=void 0),n.encryptBlock(i,0);for(var s=0;s<o;s++)e[t+s]^=i[s]}}),e.Decryptor=t,e),n.mode.OFB));var e,t,n}(),function(){return Wu?Ku.exports:(Wu=1,Ku.exports=(t=Ql(),Mu(),t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e),t.mode.ECB));var e,t}(),function(){return Ju?Xu.exports:(Ju=1,Xu.exports=(e=Ql(),Mu(),e.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,o=4*t,r=o-n%o,i=n+r-1;e.clamp(),e.words[i>>>2]|=r<<24-i%4*8,e.sigBytes+=r},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923));var e}(),function(){return Gu?Yu.exports:(Gu=1,Yu.exports=(e=Ql(),Mu(),e.pad.Iso10126={pad:function(t,n){var o=4*n,r=o-t.sigBytes%o;t.concat(e.lib.WordArray.random(r-1)).concat(e.lib.WordArray.create([r<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Iso10126));var e}(),function(){return Zu?ef.exports:(Zu=1,ef.exports=(e=Ql(),Mu(),e.pad.Iso97971={pad:function(t,n){t.concat(e.lib.WordArray.create([2147483648],1)),e.pad.ZeroPadding.pad(t,n)},unpad:function(t){e.pad.ZeroPadding.unpad(t),t.sigBytes--}},e.pad.Iso97971));var e}(),function(){return tf?nf.exports:(tf=1,nf.exports=(e=Ql(),Mu(),e.pad.ZeroPadding={pad:function(e,t){var n=4*t;e.clamp(),e.sigBytes+=n-(e.sigBytes%n||n)},unpad:function(e){var t=e.words,n=e.sigBytes-1;for(n=e.sigBytes-1;n>=0;n--)if(t[n>>>2]>>>24-n%4*8&255){e.sigBytes=n+1;break}}},e.pad.ZeroPadding));var e}(),function(){return of?rf.exports:(of=1,rf.exports=(e=Ql(),Mu(),e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding));var e}(),function(){return sf?af.exports:(sf=1,af.exports=(o=Ql(),Mu(),t=(e=o).lib.CipherParams,n=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(n)},parse:function(e){var o=n.parse(e);return t.create({ciphertext:o})}},o.format.Hex));var e,t,n,o}(),function(){return cf?lf.exports:(cf=1,lf.exports=(e=Ql(),nu(),cu(),Lu(),Mu(),function(){var t=e,n=t.lib.BlockCipher,o=t.algo,r=[],i=[],s=[],a=[],c=[],l=[],u=[],f=[],p=[],d=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,o=0;for(t=0;t<256;t++){var h=o^o<<1^o<<2^o<<3^o<<4;h=h>>>8^255&h^99,r[n]=h,i[h]=n;var g=e[n],m=e[g],y=e[m],v=257*e[h]^16843008*h;s[n]=v<<24|v>>>8,a[n]=v<<16|v>>>16,c[n]=v<<8|v>>>24,l[n]=v,v=16843009*y^65537*m^257*g^16843008*n,u[h]=v<<24|v>>>8,f[h]=v<<16|v>>>16,p[h]=v<<8|v>>>24,d[h]=v,n?(n=g^e[e[e[y^g]]],o^=e[e[o]]):n=o=1}}();var h=[0,1,2,4,8,16,32,64,128,27,54],g=o.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,o=4*((this._nRounds=n+6)+1),i=this._keySchedule=[],s=0;s<o;s++)s<n?i[s]=t[s]:(l=i[s-1],s%n?n>6&&s%n==4&&(l=r[l>>>24]<<24|r[l>>>16&255]<<16|r[l>>>8&255]<<8|r[255&l]):(l=r[(l=l<<8|l>>>24)>>>24]<<24|r[l>>>16&255]<<16|r[l>>>8&255]<<8|r[255&l],l^=h[s/n|0]<<24),i[s]=i[s-n]^l);for(var a=this._invKeySchedule=[],c=0;c<o;c++){if(s=o-c,c%4)var l=i[s];else l=i[s-4];a[c]=c<4||s<=4?l:u[r[l>>>24]]^f[r[l>>>16&255]]^p[r[l>>>8&255]]^d[r[255&l]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,c,l,r)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,u,f,p,d,i),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,o,r,i,s,a){for(var c=this._nRounds,l=e[t]^n[0],u=e[t+1]^n[1],f=e[t+2]^n[2],p=e[t+3]^n[3],d=4,h=1;h<c;h++){var g=o[l>>>24]^r[u>>>16&255]^i[f>>>8&255]^s[255&p]^n[d++],m=o[u>>>24]^r[f>>>16&255]^i[p>>>8&255]^s[255&l]^n[d++],y=o[f>>>24]^r[p>>>16&255]^i[l>>>8&255]^s[255&u]^n[d++],v=o[p>>>24]^r[l>>>16&255]^i[u>>>8&255]^s[255&f]^n[d++];l=g,u=m,f=y,p=v}g=(a[l>>>24]<<24|a[u>>>16&255]<<16|a[f>>>8&255]<<8|a[255&p])^n[d++],m=(a[u>>>24]<<24|a[f>>>16&255]<<16|a[p>>>8&255]<<8|a[255&l])^n[d++],y=(a[f>>>24]<<24|a[p>>>16&255]<<16|a[l>>>8&255]<<8|a[255&u])^n[d++],v=(a[p>>>24]<<24|a[l>>>16&255]<<16|a[u>>>8&255]<<8|a[255&f])^n[d++],e[t]=g,e[t+1]=m,e[t+2]=y,e[t+3]=v},keySize:8});t.AES=n._createHelper(g)}(),e.AES));var e}(),pf(),function(){return df?hf.exports:(df=1,hf.exports=(e=Ql(),nu(),cu(),Lu(),Mu(),function(){var t=e,n=t.lib.StreamCipher,o=t.algo,r=o.RC4=n.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,o=this._S=[],r=0;r<256;r++)o[r]=r;r=0;for(var i=0;r<256;r++){var s=r%n,a=t[s>>>2]>>>24-s%4*8&255;i=(i+o[r]+a)%256;var c=o[r];o[r]=o[i],o[i]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var e=this._S,t=this._i,n=this._j,o=0,r=0;r<4;r++){n=(n+e[t=(t+1)%256])%256;var i=e[t];e[t]=e[n],e[n]=i,o|=e[(e[t]+e[n])%256]<<24-8*r}return this._i=t,this._j=n,o}t.RC4=n._createHelper(r);var s=o.RC4Drop=r.extend({cfg:r.cfg.extend({drop:192}),_doReset:function(){r._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)i.call(this)}});t.RC4Drop=n._createHelper(s)}(),e.RC4));var e}(),function(){return gf?mf.exports:(gf=1,mf.exports=(e=Ql(),nu(),cu(),Lu(),Mu(),function(){var t=e,n=t.lib.StreamCipher,o=t.algo,r=[],i=[],s=[],a=o.Rabbit=n.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=0;n<4;n++)e[n]=16711935&(e[n]<<8|e[n]>>>24)|4278255360&(e[n]<<24|e[n]>>>8);var o=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,n=0;n<4;n++)c.call(this);for(n=0;n<8;n++)r[n]^=o[n+4&7];if(t){var i=t.words,s=i[0],a=i[1],l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=l>>>16|4294901760&u,p=u<<16|65535&l;for(r[0]^=l,r[1]^=f,r[2]^=u,r[3]^=p,r[4]^=l,r[5]^=f,r[6]^=u,r[7]^=p,n=0;n<4;n++)c.call(this)}},_doProcessBlock:function(e,t){var n=this._X;c.call(this),r[0]=n[0]^n[5]>>>16^n[3]<<16,r[1]=n[2]^n[7]>>>16^n[5]<<16,r[2]=n[4]^n[1]>>>16^n[7]<<16,r[3]=n[6]^n[3]>>>16^n[1]<<16;for(var o=0;o<4;o++)r[o]=16711935&(r[o]<<8|r[o]>>>24)|4278255360&(r[o]<<24|r[o]>>>8),e[t+o]^=r[o]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,n=0;n<8;n++)i[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<i[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<i[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<i[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<i[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<i[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<i[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<i[6]>>>0?1:0)|0,this._b=t[7]>>>0<i[7]>>>0?1:0,n=0;n<8;n++){var o=e[n]+t[n],r=65535&o,a=o>>>16,c=((r*r>>>17)+r*a>>>15)+a*a,l=((4294901760&o)*o|0)+((65535&o)*o|0);s[n]=c^l}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.Rabbit=n._createHelper(a)}(),e.Rabbit));var e}(),function(){return yf?vf.exports:(yf=1,vf.exports=(e=Ql(),nu(),cu(),Lu(),Mu(),function(){var t=e,n=t.lib.StreamCipher,o=t.algo,r=[],i=[],s=[],a=o.RabbitLegacy=n.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],o=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var r=0;r<4;r++)c.call(this);for(r=0;r<8;r++)o[r]^=n[r+4&7];if(t){var i=t.words,s=i[0],a=i[1],l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=l>>>16|4294901760&u,p=u<<16|65535&l;for(o[0]^=l,o[1]^=f,o[2]^=u,o[3]^=p,o[4]^=l,o[5]^=f,o[6]^=u,o[7]^=p,r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(e,t){var n=this._X;c.call(this),r[0]=n[0]^n[5]>>>16^n[3]<<16,r[1]=n[2]^n[7]>>>16^n[5]<<16,r[2]=n[4]^n[1]>>>16^n[7]<<16,r[3]=n[6]^n[3]>>>16^n[1]<<16;for(var o=0;o<4;o++)r[o]=16711935&(r[o]<<8|r[o]>>>24)|4278255360&(r[o]<<24|r[o]>>>8),e[t+o]^=r[o]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,n=0;n<8;n++)i[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<i[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<i[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<i[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<i[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<i[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<i[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<i[6]>>>0?1:0)|0,this._b=t[7]>>>0<i[7]>>>0?1:0,n=0;n<8;n++){var o=e[n]+t[n],r=65535&o,a=o>>>16,c=((r*r>>>17)+r*a>>>15)+a*a,l=((4294901760&o)*o|0)+((65535&o)*o|0);s[n]=c^l}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.RabbitLegacy=n._createHelper(a)}(),e.RabbitLegacy));var e}(),Pf())),Cf={pages:[{path:"pages/tabbar/tabbar",style:{navigationBarTitleText:"首页",navigationStyle:"custom"}}],subPackages:[{root:"pages/home",pages:[{path:"follow/list",style:{navigationBarTitleText:"学生跟进"}},{path:"follow/detail",style:{navigationBarTitleText:"跟进详情"}},{path:"stInfo/list",style:{navigationBarTitleText:"学生列表"}},{path:"stInfo/detail",style:{navigationBarTitleText:"学生详情"}},{path:"contract/list",style:{navigationBarTitleText:"合同列表"}},{path:"contract/detail",style:{navigationBarTitleText:"合同详情"}},{path:"material/materialUpload",style:{navigationBarTitleText:"资料上传"}}]},{root:"pages/mine",pages:[{path:"resetPwd/resetPwd",style:{navigationBarTitleText:"修改密码"}},{path:"resetPwd/resetPwdByCode",style:{navigationBarTitleText:"验证码修改密码"}},{path:"agreements/chan/agreementList",style:{navigationBarTitleText:"我的合同"}},{path:"agreements/chan/agreementDetail",style:{navigationBarTitleText:"合同详情"}},{path:"agreements/chan/agreementSign",style:{navigationBarTitleText:"协议签署"}},{path:"agreements/stInfo/school/agreementDetail",style:{navigationBarTitleText:"定校详情"}},{path:"agreements/stInfo/school/agreementSign",style:{navigationBarTitleText:"定校签署"}},{path:"agreements/stInfo/school/agreementHistory",style:{navigationBarTitleText:"定校记录"}},{path:"agreements/stInfo/contract/agreementDetail",style:{navigationBarTitleText:"合同详情"}},{path:"agreements/stInfo/contract/agreementSign",style:{navigationBarTitleText:"合同签署"}},{path:"agreements/stInfo/contract/installmentProof",style:{navigationBarTitleText:"付款凭证管理"}}]},{root:"pages/login",pages:[{path:"login",style:{navigationBarTitleText:"登录"}},{path:"bindPhone",style:{navigationBarTitleText:"绑定手机号"}}]},{root:"pages/transition",pages:[{path:"transition",style:{navigationBarTitleText:"加载中"}},{path:"webView",style:{navigationBarTitleText:"详情",navigationStyle:"custom"}},{path:"filePreview",style:{navigationBarTitleText:"文件预览",navigationStyle:"custom"}}]},{root:"pages/error",pages:[{path:"404",style:{navigationBarTitleText:"页面不存在"}}]}],globalStyle:{backgroundTextStyle:"light",navigationBarTextStyle:"white",navigationBarTitleText:"学生跟进",navigationStyle:"custom"},easycom:{autoscan:!0,custom:{"^u--(.*)":"@/node_modules/uview-plus/components/u-$1/u-$1.vue","^up-(.*)":"@/node_modules/uview-plus/components/u-$1/u-$1.vue","^u-([^-].*)":"@/node_modules/uview-plus/components/u-$1/u-$1.vue"}}};function Tf(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function Bf(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function Of(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function jf(e){return"function"==typeof e}const Lf={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},date:function(e){if(!e)return!1;if("number"==typeof e)return(10===e.toString().length||13===e.toString().length)&&!isNaN(new Date(e).getTime());if("string"==typeof e){const t=Number(e);if(!isNaN(t)&&(10===t.toString().length||13===t.toString().length))return!isNaN(new Date(t).getTime());if(e.length<10||e.length>19)return!1;if(!/^\d{4}[-\/]\d{2}[-\/]\d{2}( \d{1,2}:\d{2}(:\d{2})?)?$/.test(e))return!1;const n=new Date(e);return!isNaN(n.getTime())}return!1},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:Tf,digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:Bf,isEmpty:Bf,jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(ah){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)},array:Of,code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},func:jf,promise:function(e){return function(e){return"[object Promise]"===Object.prototype.toString.call(e)}(e)&&jf(e.then)&&jf(e.catch)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)},image:function(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e}};function Nf(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function Rf(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function Mf(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=Rf(e);return t>0?Nf(Number(e)*Math.pow(10,t)):Number(e)}function Ff(e){(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&ac.__f__("warn","at node_modules/uview-plus/libs/function/digit.js:45",`${e} 超出了精度限制，结果可能不正确`)}function Uf(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function Df(...e){if(e.length>2)return Uf(e,Df);const[t,n]=e,o=Mf(t),r=Mf(n),i=Rf(t)+Rf(n),s=o*r;return Ff(s),s/Math.pow(10,i)}function qf(...e){if(e.length>2)return Uf(e,qf);const[t,n]=e,o=Mf(t),r=Mf(n);return Ff(o),Ff(r),Df(o/r,Nf(Math.pow(10,Rf(n)-Rf(t))))}ac.__f__("log","at node_modules/uview-plus/libs/config/config.js:5","\n %c uview-plus V3 %c https://ijry.github.io/uview-plus/ \n\n","color: #ffffff; background: #3c9cff; padding:5px 0;","color: #3c9cff;background: #ffffff; padding:5px 0;");const Hf={v:"3",version:"3",type:["primary","success","info","error","warning"],color:{"u-primary":"#2979ff","u-warning":"#ff9900","u-success":"#19be6b","u-error":"#fa3534","u-info":"#909399","u-main-color":"#303133","u-content-color":"#606266","u-tips-color":"#909399","u-light-color":"#c0c4cc","up-primary":"#2979ff","up-warning":"#ff9900","up-success":"#19be6b","up-error":"#fa3534","up-info":"#909399","up-main-color":"#303133","up-content-color":"#606266","up-tips-color":"#909399","up-light-color":"#c0c4cc"},iconUrl:"https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf",customIcon:{family:"",url:""},customIcons:{},unit:"px",interceptor:{navbarLeftClick:null}};function zf(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))}function Qf(e=void 0){let t=this.$parent;for(;t;){if(e=e.replace(/up-([a-zA-Z0-9-_]+)/g,"u-$1"),!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function $f(e,t="object"){if(Bf(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=Yf(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[Yf(o[0])]=Yf(o[1])}return n}let n="";return"object"==typeof e&&e.forEach(((e,t)=>{const o=t.replace(/([A-Z])/g,"-$1").toLowerCase();n+=`${o}:${e};`})),Yf(n)}function Vf(e="auto",t=""){return t||(t=Hf.unit||"px"),"rpx"==t&&Tf(String(e))&&(e*=2),Tf(e=String(e))?`${e}${t}`:e}function Wf(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;const t=Of(e)?[]:{};for(const n in e)e.hasOwnProperty(n)&&(t[n]="object"==typeof e[n]?Wf(e[n]):e[n]);return t}function Kf(e={},t={}){let n=Wf(e);if("object"!=typeof n||"object"!=typeof t)return!1;for(const o in t)t.hasOwnProperty(o)&&(o in n?null==t[o]||"object"!=typeof n[o]||"object"!=typeof t[o]?n[o]=t[o]:n[o].concat&&t[o].concat?n[o]=n[o].concat(t[o]):n[o]=Kf(n[o],t[o]):n[o]=t[o]);return n}function Jf(e,t={}){if("object"!=typeof e||"object"!=typeof t)return!1;for(const n in t)t.hasOwnProperty(n)&&(n in e?null==t[n]||"object"!=typeof e[n]||"object"!=typeof t[n]?e[n]=t[n]:e[n].concat&&t[n].concat?e[n]=e[n].concat(t[n]):e[n]=Jf(e[n],t[n]):e[n]=t[n]);return e}function Xf(e){ac.__f__("error","at node_modules/uview-plus/libs/function/index.js:304",`uView提示：${e}`)}function Gf(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):new Date("string"==typeof e?e.replace(/-/g,"/"):e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function Yf(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function Zf(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function ep(e,t=2e3){ac.showToast({title:String(e),icon:"none",duration:t})}function tp(e,t){const n=Qf.call(e,"u-form-item"),o=Qf.call(e,"u-form");n&&o&&o.validateField(n.prop,(()=>{}),t)}function np(e,t){if("object"!=typeof e||null==e)return"";if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}function op(e,t,n){if("object"!=typeof e||null==e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n}function rp(){const e=getCurrentPages();return`/${e[e.length-1].route||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const ip={range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},getPx:function(e,t=!1){return Tf(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${ac.upx2px(parseInt(e))}px`:Number(ac.upx2px(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},sleep:zf,os:function(){return ac.getDeviceInfo().platform.toLowerCase()},sys:function(){return ac.getSystemInfoSync()},getWindowInfo:function(){let e={};return e=ac.getWindowInfo(),e},random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")},$parent:Qf,addStyle:$f,addUnit:Vf,deepClone:Wf,deepMerge:Kf,shallowMerge:Jf,error:Xf,randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},timeFormat:Gf,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:Gf(e,t)}return o},trim:Yf,queryParams:Zf,toast:ep,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,s=void 0===o?",":o,a=void 0===n?".":n;let c="";c=(i?function(e,t){const n=Math.pow(10,t);let o=qf(Math.round(Math.abs(Df(e,n))),n);return e<0&&0!==o&&(o=Df(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const l=/(-?\d+)(\d{3})/;for(;l.test(c[0]);)c[0]=c[0].replace(l,`$1${s}$2`);return(c[1]||"").length<i&&(c[1]=c[1]||"",c[1]+=new Array(i-c[1].length+1).join("0")),c.join(a)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},padZero:function(e){return`00${e}`.slice(-2)},formValidate:tp,getProperty:np,setProperty:op,page:rp,pages:function(){return getCurrentPages()},getValueByPath:function(e,t){return t.split(".").reduce(((e,t)=>e&&void 0!==e[t]?e[t]:void 0),e)},genLightColor:function(e,t=95){const n=function(e){const t=e.toLowerCase().trim();if(t.startsWith("#")){const e=t.replace("#",""),n=3===e.length?e.split("").map((e=>e+e)).join(""):e;return{r:parseInt(n.substring(0,2),16),g:parseInt(n.substring(2,4),16),b:parseInt(n.substring(4,6),16)}}const n=t.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);if(n)return{r:+n[1],g:+n[2],b:+n[3]};throw new Error("Invalid color format")}(e),o=function(e,t,n){e/=255,t/=255,n/=255;const o=Math.max(e,t,n),r=Math.min(e,t,n);let i,s,a=(o+r)/2;if(o===r)i=s=0;else{const c=o-r;switch(s=a>.5?c/(2-o-r):c/(o+r),o){case e:i=(t-n)/c+(t<n?6:0);break;case t:i=(n-e)/c+2;break;case n:i=(e-t)/c+4}i=(60*i).toFixed(1)}return{h:+i,s:+(100*s).toFixed(1),l:+(100*a).toFixed(1)}}(n.r,n.g,n.b),r={h:o.h,s:o.s,l:Math.min(t,95)};return function(e,t,n){n/=100;const o=t*Math.min(n,1-n)/100,r=t=>{const r=(t+e/30)%12,i=n-o*Math.max(Math.min(r-3,9-r,1),-1);return Math.round(255*i).toString(16).padStart(2,"0")};return`#${r(0)}${r(8)}${r(4)}`}(r.h,r.s,r.l)}};const sp=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=Zf(t,!1),e+`&${n}`):(n=Zf(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=Kf(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==rp())if(t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=Kf(this.config,n),"function"==typeof ac.$u.routeIntercept){await new Promise(((e,t)=>{ac.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||ac.navigateTo({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||ac.redirectTo({url:t}),"switchTab"!=e.type&&"tab"!=e.type||ac.switchTab({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||ac.reLaunch({url:t}),"navigateBack"!=e.type&&"back"!=e.type||ac.navigateBack({delta:o})}}).route,ap={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},created(){this.$u.getRect=this.$uGetRect},computed:{$u:()=>Kf(ac.$u,{props:void 0,http:void 0,mixin:void 0}),bem:()=>function(e,t,n){const o=`u-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&sp({type:this.linkType,url:t})},navTo(e="",t="navigateTo"){sp({type:this.linkType,url:e})},$uGetRect(e,t){return new Promise((n=>{ac.createSelectorQuery().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=Qf.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){ac.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&Lf.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},cp={options:{virtualHost:!0}};function lp(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=up(e,!1),r=o[0],i=o[1],s=o[2],a=up(t,!1),c=(a[0]-r)/n,l=(a[1]-i)/n,u=(a[2]-s)/n,f=[];for(let p=0;p<n;p++){let o=fp(`rgb(${Math.round(c*p+r)},${Math.round(l*p+i)},${Math.round(u*p+s)})`);0===p&&(o=fp(e)),p===n-1&&(o=fp(t)),f.push(o)}return f}function up(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function fp(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}const pp={colorGradient:lp,hexToRgb:up,rgbToHex:fp,colorToRgba:function(e,t){e=fp(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n}};let dp,hp=null;function gp(e,t=500,n=!0){n?dp||(dp=!0,"function"==typeof e&&e(),setTimeout((()=>{dp=!1}),t)):dp||(dp=!0,setTimeout((()=>{dp=!1,"function"==typeof e&&e()}),t))}const mp={add:function(e,t){var n,o,r;try{n=e.toString().split(".")[1].length}catch(ah){n=0}try{o=t.toString().split(".")[1].length}catch(ah){o=0}return(e*(r=Math.pow(10,Math.max(n,o)))+t*r)/r},sub:function(e,t){var n,o,r,i;try{n=e.toString().split(".")[1].length}catch(ah){n=0}try{o=t.toString().split(".")[1].length}catch(ah){o=0}return r=Math.pow(10,Math.max(n,o)),i=n>=o?n:o,Math.abs(((e*r-t*r)/r).toFixed(i))},mul:function(e,t){var n=0,o=e.toString(),r=t.toString();try{n+=o.split(".")[1].length}catch(i){}try{n+=r.split(".")[1].length}catch(i){}return Number(o.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,n)},div:function(e,t){var n,o,r=0,i=0;try{r=e.toString().split(".")[1].length}catch(s){}try{i=t.toString().split(".")[1].length}catch(s){}return n=Number(e.toString().replace(".","")),o=Number(t.toString().replace(".","")),xyutil.mul(n/o,Math.pow(10,i-r))}},yp={toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},vp={primary:"#3c9cff",info:"#909399",default:"#909399",warning:"#f9ae3d",error:"#f56c6c",success:"#5ac725",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},{toString:bp}=Object.prototype;function wp(e){return"[object Array]"===bp.call(e)}function xp(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),wp(e))for(let n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.call(null,e[n],n,e)}function _p(){const e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=_p(e[n],t):e[n]="object"==typeof t?_p({},t):t}for(let n=0,o=arguments.length;n<o;n++)xp(arguments[n],t);return e}function Ap(e){return void 0===e}function Sp(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function kp(e,t){if(!t)return e;let n;if(o=t,"undefined"!=typeof URLSearchParams&&o instanceof URLSearchParams)n=t.toString();else{const e=[];xp(t,((t,n)=>{null!=t&&(wp(t)?n=`${n}[]`:t=[t],xp(t,(t=>{!function(e){return"[object Date]"===bp.call(e)}(t)?function(e){return null!==e&&"object"==typeof e}(t)&&(t=JSON.stringify(t)):t=t.toISOString(),e.push(`${Sp(n)}=${Sp(t)}`)})))})),n=e.join("&")}var o;if(n){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}const Ep=(e,t)=>{const n={};return e.forEach((e=>{Ap(t[e])||(n[e]=t[e])})),n},Pp=e=>(e=>new Promise(((t,n)=>{const o=kp((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?`${e.replace(/\/+$/,"")}/${t.replace(/^\/+/,"")}`:e}(r,i):i),e.params);var r,i;const s={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e;try{"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(ah){}!function(e,t,n){const{validateStatus:o}=n.config,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let a;if("UPLOAD"===e.method){delete s.header["content-type"],delete s.header["Content-Type"];const t={filePath:e.filePath,name:e.name},n=["formData"];a=ac.uploadFile({...s,...t,...Ep(n,e)})}else if("DOWNLOAD"===e.method)a=ac.downloadFile(s);else{const t=["data","method","timeout","dataType","responseType"];a=ac.request({...s,...Ep(t,e)})}e.getTask&&e.getTask(a,e)})))(e);function Ip(){this.handlers=[]}Ip.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Ip.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Ip.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const Cp=(e,t,n)=>{const o={};return e.forEach((e=>{Ap(n[e])?Ap(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},Tp={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,validateStatus:function(e){return e>=200&&e<300}};var Bp=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(a){t=function(){}}try{n=Set}catch(a){n=function(){}}try{o=Promise}catch(a){o=function(){}}function r(i,a,c,l,u){"object"==typeof a&&(c=a.depth,l=a.prototype,u=a.includeNonEnumerable,a=a.circular);var f=[],p=[],d="undefined"!=typeof Buffer;return void 0===a&&(a=!0),void 0===c&&(c=1/0),function i(c,h){if(null===c)return null;if(0===h)return c;var g,m;if("object"!=typeof c)return c;if(e(c,t))g=new t;else if(e(c,n))g=new n;else if(e(c,o))g=new o((function(e,t){c.then((function(t){e(i(t,h-1))}),(function(e){t(i(e,h-1))}))}));else if(r.__isArray(c))g=[];else if(r.__isRegExp(c))g=new RegExp(c.source,s(c)),c.lastIndex&&(g.lastIndex=c.lastIndex);else if(r.__isDate(c))g=new Date(c.getTime());else{if(d&&Buffer.isBuffer(c))return Buffer.from?g=Buffer.from(c):(g=new Buffer(c.length),c.copy(g)),g;e(c,Error)?g=Object.create(c):void 0===l?(m=Object.getPrototypeOf(c),g=Object.create(m)):(g=Object.create(l),m=l)}if(a){var y=f.indexOf(c);if(-1!=y)return p[y];f.push(c),p.push(g)}for(var v in e(c,t)&&c.forEach((function(e,t){var n=i(t,h-1),o=i(e,h-1);g.set(n,o)})),e(c,n)&&c.forEach((function(e){var t=i(e,h-1);g.add(t)})),c){Object.getOwnPropertyDescriptor(c,v)&&(g[v]=i(c[v],h-1));try{if("undefined"===Object.getOwnPropertyDescriptor(c,v).set)continue;g[v]=i(c[v],h-1)}catch(ah){if(ah instanceof TypeError)continue;if(ah instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(c);for(v=0;v<b.length;v++){var w=b[v];(!(_=Object.getOwnPropertyDescriptor(c,w))||_.enumerable||u)&&(g[w]=i(c[w],h-1),Object.defineProperty(g,w,_))}}if(u){var x=Object.getOwnPropertyNames(c);for(v=0;v<x.length;v++){var _,A=x[v];(_=Object.getOwnPropertyDescriptor(c,A))&&_.enumerable||(g[A]=i(c[A],h-1),Object.defineProperty(g,A,_))}}return g}(i,c)}function i(e){return Object.prototype.toString.call(e)}function s(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=s,r}();const Op=new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},ac.__f__("warn","at node_modules/uview-plus/libs/luch-request/core/Request.js:40","设置全局参数必须接收一个Object")),this.config=Bp({...Tp,...e}),this.interceptors={request:new Ip,response:new Ip}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:_p(e.header||{},t.header||{})};if(o={...o,...Cp(["getTask","validateStatus"],e,t)},"DOWNLOAD"===n);else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["filePath","name","formData"].forEach((e=>{Ap(t[e])||(o[e]=t[e])}));else{const n=["data","timeout","dataType","responseType"];o={...o,...Cp(n,e,t)}}return o})(this.config,e);const t=[Pp,void 0];let n=Promise.resolve(e);for(this.interceptors.request.forEach((e=>{t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((e=>{t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}},jp={calendar:{title:"日期选择",showTitle:!0,showSubtitle:!0,mode:"single",startText:"开始",endText:"结束",customList:[],color:"#3c9cff",minDate:0,maxDate:0,defaultDate:null,maxCount:Number.MAX_SAFE_INTEGER,rowHeight:56,formatter:null,showLunar:!1,showMark:!0,confirmText:"确定",confirmDisabledText:"确定",show:!1,closeOnClickOverlay:!1,readonly:!1,showConfirm:!0,maxRange:Number.MAX_SAFE_INTEGER,rangePrompt:"",showRangePrompt:!0,allowSameDay:!1,round:0,monthNum:3,weekText:["一","二","三","四","五","六","日"],forbidDays:[],forbidDaysToast:"该日期已禁用"}},Lp={datetimePicker:{show:!1,popupMode:"bottom",showToolbar:!0,value:"",title:"",mode:"datetime",maxDate:new Date((new Date).getFullYear()+10,0,1).getTime(),minDate:new Date((new Date).getFullYear()-10,0,1).getTime(),minHour:0,maxHour:23,minMinute:0,maxMinute:59,filter:null,formatter:null,loading:!1,itemHeight:44,cancelText:"取消",confirmText:"确认",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,closeOnClickOverlay:!1,defaultIndex:[],inputBorder:"surround",disabled:!1,disabledColor:"",placeholder:"请选择",inputProps:{}}},{color:Np}=Hf,Rp={icon:{name:"",color:Np["u-content-color"],size:"16px",bold:!1,index:"",hoverClass:"",customPrefix:"uicon",label:"",labelPos:"right",labelSize:"15px",labelColor:Np["u-content-color"],space:"3px",imgMode:"",width:"",height:"",top:0,stop:!1}},{color:Mp}=Hf,Fp={link:{color:Mp["u-primary"],fontSize:15,underLine:!1,href:"",mpTips:"链接已复制，请在浏览器打开",lineColor:"",text:""}},{color:Up}=Hf,Dp={actionSheet:{show:!1,title:"",description:"",actions:[],index:"",cancelText:"",closeOnClickAction:!0,safeAreaInsetBottom:!0,openType:"",closeOnClickOverlay:!0,round:0,wrapMaxHeight:"600px"},album:{urls:[],keyName:"",singleSize:180,multipleSize:70,space:6,singleMode:"scaleToFill",multipleMode:"aspectFill",maxCount:9,previewFullImage:!0,rowCount:3,showMore:!0,autoWrap:!1,unit:"px",stop:!0},alert:{title:"",type:"warning",description:"",closable:!1,showIcon:!1,effect:"light",center:!1,fontSize:14},avatar:{src:"",shape:"circle",size:40,mode:"scaleToFill",text:"",bgColor:"#c0c4cc",color:"#ffffff",fontSize:18,icon:"",mpAvatar:!1,randomBgColor:!1,defaultUrl:"",colorIndex:"",name:""},avatarGroup:{urls:[],maxCount:5,shape:"circle",mode:"scaleToFill",showMore:!0,size:40,keyName:"",gap:.5,extraValue:0},backtop:{mode:"circle",icon:"arrow-upward",text:"",duration:100,scrollTop:0,top:400,bottom:100,right:20,zIndex:9,iconStyle:{color:"#909399",fontSize:"19px"}},badge:{isDot:!1,value:"",show:!0,max:999,type:"error",showZero:!1,bgColor:null,color:null,shape:"circle",numberType:"overflow",offset:[],inverted:!1,absolute:!1},button:{hairline:!1,type:"info",size:"normal",shape:"square",plain:!1,disabled:!1,loading:!1,loadingText:"",loadingMode:"spinner",loadingSize:15,openType:"",formType:"",appParameter:"",hoverStopPropagation:!0,lang:"en",sessionFrom:"",sendMessageTitle:"",sendMessagePath:"",sendMessageImg:"",showMessageCard:!1,dataName:"",throttleTime:0,hoverStartTime:0,hoverStayTime:200,text:"",icon:"",iconColor:"",color:"",stop:!0},...jp,carKeyboard:{random:!1},card:{full:!1,title:"",titleColor:"#303133",titleSize:"15px",subTitle:"",subTitleColor:"#909399",subTitleSize:"13px",border:!0,index:"",margin:"15px",borderRadius:"8px",headStyle:{},bodyStyle:{},footStyle:{},headBorderBottom:!0,footBorderTop:!0,thumb:"",thumbWidth:"30px",thumbCircle:!1,padding:"15px",paddingHead:"",paddingBody:"",paddingFoot:"",showHead:!0,showFoot:!0,boxShadow:"none"},cell:{customClass:"",title:"",label:"",value:"",icon:"",disabled:!1,border:!0,center:!1,url:"",linkType:"navigateTo",clickable:!1,isLink:!1,required:!1,arrowDirection:"",iconStyle:{},rightIconStyle:{},rightIcon:"arrow-right",titleStyle:{},size:"",stop:!0,name:""},cellGroup:{title:"",border:!0,customStyle:{}},checkbox:{name:"",shape:"",size:"",checkbox:!1,disabled:"",activeColor:"",inactiveColor:"",iconSize:"",iconColor:"",label:"",labelSize:"",labelColor:"",labelDisabled:""},checkboxGroup:{name:"",value:[],shape:"square",disabled:!1,activeColor:"#2979ff",inactiveColor:"#c8c9cc",size:18,placement:"row",labelSize:14,labelColor:"#303133",labelDisabled:!1,iconColor:"#ffffff",iconSize:12,iconPlacement:"left",borderBottom:!1},circleProgress:{percentage:30},code:{seconds:60,startText:"获取验证码",changeText:"X秒重新获取",endText:"重新获取",keepRunning:!1,uniqueKey:""},codeInput:{adjustPosition:!0,maxlength:6,dot:!1,mode:"box",hairline:!1,space:10,value:"",focus:!1,bold:!1,color:"#606266",fontSize:18,size:35,disabledKeyboard:!1,borderColor:"#c9cacc",disabledDot:!0},col:{span:12,offset:0,justify:"start",align:"stretch",textAlign:"left"},collapse:{value:null,accordion:!1,border:!0},collapseItem:{title:"",value:"",label:"",disabled:!1,isLink:!0,clickable:!0,border:!0,align:"left",name:"",icon:"",duration:300,showRight:!0,titleStyle:{},iconStyle:{},rightIconStyle:{},cellCustomStyle:{},cellCustomClass:""},columnNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80,step:!1,duration:1500,disableTouch:!0,justifyContent:"flex-start"},countDown:{time:0,format:"HH:mm:ss",autoStart:!0,millisecond:!1},countTo:{startVal:0,endVal:0,duration:2e3,autoplay:!0,decimals:0,useEasing:!0,decimal:".",color:"#606266",fontSize:22,bold:!1,separator:""},...Lp,divider:{dashed:!1,hairline:!0,dot:!1,textPosition:"center",text:"",textSize:14,textColor:"#909399",lineColor:"#dcdfe6"},empty:{icon:"",text:"",textColor:"#c0c4cc",textSize:14,iconColor:"#c0c4cc",iconSize:90,mode:"data",width:160,height:160,show:!0,marginTop:0},form:{model:{},rules:{},errorType:"message",borderBottom:!0,labelPosition:"left",labelWidth:45,labelAlign:"left",labelStyle:{}},formItem:{label:"",prop:"",rules:[],borderBottom:"",labelPosition:"",labelWidth:"",rightIcon:"",leftIcon:"",required:!1,leftIconStyle:""},gap:{bgColor:"transparent",height:20,marginTop:0,marginBottom:0,customStyle:{}},grid:{col:3,border:!1,align:"left"},gridItem:{name:null,bgColor:"transparent"},...Rp,image:{src:"",mode:"aspectFill",width:"300",height:"225",shape:"square",radius:0,lazyLoad:!0,showMenuByLongpress:!0,loadingIcon:"photo",errorIcon:"error-circle",showLoading:!0,showError:!0,fade:!0,webp:!1,duration:500,bgColor:"#f3f4f6"},indexAnchor:{text:"",color:"#606266",size:14,bgColor:"#dedede",height:32},indexList:{inactiveColor:"#606266",activeColor:"#5677fc",indexList:[],sticky:!0,customNavHeight:0,safeBottomFix:!1},input:{value:"",type:"text",fixed:!1,disabled:!1,disabledColor:"#f5f7fa",clearable:!1,password:!1,maxlength:140,placeholder:null,placeholderClass:"input-placeholder",placeholderStyle:"color: #c0c4cc",showWordLimit:!1,confirmType:"done",confirmHold:!1,holdKeyboard:!1,focus:!1,autoBlur:!1,disableDefaultPadding:!1,cursor:-1,cursorSpacing:30,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,inputAlign:"left",fontSize:"15px",color:"#303133",prefixIcon:"",prefixIconStyle:"",suffixIcon:"",suffixIconStyle:"",border:"surround",readonly:!1,shape:"square",formatter:null},keyboard:{mode:"number",dotDisabled:!1,tooltip:!0,showTips:!0,tips:"",showCancel:!0,showConfirm:!0,random:!1,safeAreaInsetBottom:!0,closeOnClickOverlay:!0,show:!1,overlay:!0,zIndex:10075,cancelText:"取消",confirmText:"确定",autoChange:!1},line:{color:"#d6d7d9",length:"100%",direction:"row",hairline:!0,margin:0,dashed:!1},lineProgress:{activeColor:"#19be6b",inactiveColor:"#ececec",percentage:0,showText:!0,height:12,fromRight:!1},...Fp,list:{showScrollbar:!1,lowerThreshold:50,upperThreshold:0,scrollTop:0,offsetAccuracy:10,enableFlex:!1,pagingEnabled:!1,scrollable:!0,scrollIntoView:"",scrollWithAnimation:!1,enableBackToTop:!1,height:0,width:0,preLoadScreen:1},listItem:{anchor:""},...{loadingIcon:{show:!0,color:Up["u-tips-color"],textColor:Up["u-tips-color"],vertical:!1,mode:"spinner",size:24,textSize:15,text:"",timingFunction:"ease-in-out",duration:1200,inactiveColor:""}},loadingPage:{loadingText:"正在加载",image:"",loadingMode:"circle",loading:!1,bgColor:"#ffffff",color:"#C8C8C8",fontSize:19,iconSize:28,loadingColor:"#C8C8C8",zIndex:10},loadmore:{status:"loadmore",bgColor:"transparent",icon:!0,fontSize:14,iconSize:17,color:"#606266",loadingIcon:"spinner",loadmoreText:"加载更多",loadingText:"正在加载...",nomoreText:"没有更多了",isDot:!1,iconColor:"#b7b7b7",marginTop:10,marginBottom:10,height:"auto",line:!1,lineColor:"#E6E8EB",dashed:!1},modal:{show:!1,title:"",content:"",confirmText:"确认",cancelText:"取消",showConfirmButton:!0,showCancelButton:!1,confirmColor:"#2979ff",cancelColor:"#606266",buttonReverse:!1,zoom:!0,asyncClose:!1,closeOnClickOverlay:!1,negativeTop:0,width:"650rpx",confirmButtonShape:"",duration:400,contentTextAlign:"left",asyncCloseTip:"操作中...",asyncCancelClose:!1,contentStyle:{}},...{navbar:{safeAreaInsetTop:!0,placeholder:!1,fixed:!0,border:!1,leftIcon:"arrow-left",leftText:"",rightText:"",rightIcon:"",title:"",titleColor:"",bgColor:"#ffffff",titleWidth:"400rpx",height:"44px",leftIconSize:20,leftIconColor:vp.mainColor,autoBack:!1,titleStyle:""}},noNetwork:{tips:"哎呀，网络信号丢失",zIndex:"",image:"data:image/png;base64,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"},noticeBar:{text:[],direction:"row",step:!1,icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",speed:80,fontSize:14,duration:2e3,disableTouch:!0,url:"",linkType:"navigateTo",justifyContent:"flex-start"},notify:{top:0,type:"primary",color:"#ffffff",bgColor:"",message:"",duration:3e3,fontSize:15,safeAreaInsetTop:!1},...{numberBox:{name:"",value:0,min:1,max:Number.MAX_SAFE_INTEGER,step:1,integer:!1,disabled:!1,disabledInput:!1,asyncChange:!1,inputWidth:35,showMinus:!0,showPlus:!0,decimalLength:null,longPress:!0,color:"#323233",buttonWidth:30,buttonSize:30,buttonRadius:"0px",bgColor:"#EBECEE",inputBgColor:"#EBECEE",cursorSpacing:100,disableMinus:!1,disablePlus:!1,iconStyle:"",miniMode:!1}},numberKeyboard:{mode:"number",dotDisabled:!1,random:!1},overlay:{show:!1,zIndex:10070,duration:300,opacity:.5},parse:{copyLink:!0,errorImg:"",lazyLoad:!1,loadingImg:"",pauseVideo:!0,previewImg:!0,setTitle:!0,showImgMenu:!0},picker:{show:!1,popupMode:"bottom",showToolbar:!0,title:"",columns:[],loading:!1,itemHeight:44,cancelText:"取消",confirmText:"确定",cancelColor:"#909193",confirmColor:"",visibleItemCount:5,keyName:"text",valueName:"value",closeOnClickOverlay:!1,defaultIndex:[],immediateChange:!0,zIndex:10076,disabled:!1,disabledColor:"",placeholder:"请选择",inputProps:{}},popup:{show:!1,overlay:!0,mode:"bottom",duration:300,closeable:!1,overlayStyle:{},closeOnClickOverlay:!0,zIndex:10075,safeAreaInsetBottom:!0,safeAreaInsetTop:!1,closeIconPos:"top-right",round:0,zoom:!0,bgColor:"",overlayOpacity:.5},radio:{name:"",shape:"",disabled:"",labelDisabled:"",activeColor:"",inactiveColor:"",iconSize:"",labelSize:"",label:"",labelColor:"",size:"",iconColor:"",placement:""},radioGroup:{value:"",disabled:!1,shape:"circle",activeColor:"#2979ff",inactiveColor:"#c8c9cc",name:"",size:18,placement:"row",label:"",labelColor:"#303133",labelSize:14,labelDisabled:!1,iconColor:"#ffffff",iconSize:12,borderBottom:!1,iconPlacement:"left",gap:"10px"},rate:{value:1,count:5,disabled:!1,size:18,inactiveColor:"#b2b2b2",activeColor:"#FA3534",gutter:4,minCount:1,allowHalf:!1,activeIcon:"star-fill",inactiveIcon:"star",touchable:!0},readMore:{showHeight:400,toggle:!1,closeText:"展开阅读全文",openText:"收起",color:"#2979ff",fontSize:14,textIndent:"2em",name:""},row:{gutter:0,justify:"start",align:"center"},rowNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80},scrollList:{indicatorWidth:50,indicatorBarWidth:20,indicator:!0,indicatorColor:"#f2f2f2",indicatorActiveColor:"#3c9cff",indicatorStyle:""},search:{shape:"round",bgColor:"#f2f2f2",placeholder:"请输入关键字",clearabled:!0,focus:!1,showAction:!0,actionStyle:{},actionText:"搜索",inputAlign:"left",inputStyle:{},disabled:!1,borderColor:"transparent",searchIconColor:"#909399",searchIconSize:22,color:"#606266",placeholderColor:"#909399",searchIcon:"search",iconPosition:"left",margin:"0",animation:!1,value:"",maxlength:"-1",height:32,label:null},section:{title:"",subTitle:"更多",right:!0,fontSize:15,bold:!0,color:"#303133",subColor:"#909399",showLine:!0,lineColor:"",arrow:!0},skeleton:{loading:!0,animate:!0,rows:0,rowsWidth:"100%",rowsHeight:18,title:!0,titleWidth:"50%",titleHeight:18,avatar:!1,avatarSize:32,avatarShape:"circle"},slider:{value:0,blockSize:18,min:0,max:100,step:1,activeColor:"#2979ff",inactiveColor:"#c0c4cc",blockColor:"#ffffff",showValue:!1,disabled:!1,blockStyle:{},useNative:!1,height:"2px"},statusBar:{bgColor:"transparent",height:0},steps:{direction:"row",current:0,activeColor:"#3c9cff",inactiveColor:"#969799",activeIcon:"",inactiveIcon:"",dot:!1},stepsItem:{title:"",desc:"",iconSize:17,error:!1},sticky:{offsetTop:0,customNavHeight:0,disabled:!1,bgColor:"transparent",zIndex:"",index:""},subsection:{list:[],current:0,activeColor:"#3c9cff",inactiveColor:"#303133",mode:"button",fontSize:12,bold:!0,bgColor:"#eeeeef",keyName:"name"},swipeAction:{autoClose:!0},swipeActionItem:{show:!1,closeOnClick:!0,name:"",disabled:!1,threshold:20,autoClose:!0,options:[],duration:300},swiper:{list:[],indicator:!1,indicatorActiveColor:"#FFFFFF",indicatorInactiveColor:"rgba(255, 255, 255, 0.35)",indicatorStyle:"",indicatorMode:"line",autoplay:!0,current:0,currentItemId:"",interval:3e3,duration:300,circular:!1,previousMargin:0,nextMargin:0,acceleration:!1,displayMultipleItems:1,easingFunction:"default",keyName:"url",imgMode:"aspectFill",height:130,bgColor:"#f3f4f6",radius:4,loading:!1,showTitle:!1},swiperIndicator:{length:0,current:0,indicatorActiveColor:"",indicatorInactiveColor:"",indicatorMode:"line"},switch:{loading:!1,disabled:!1,size:25,activeColor:"#2979ff",inactiveColor:"#ffffff",value:!1,activeValue:!0,inactiveValue:!1,asyncChange:!1,space:0},tabbar:{value:null,safeAreaInsetBottom:!0,border:!0,zIndex:1,activeColor:"#1989fa",inactiveColor:"#7d7e80",fixed:!0,placeholder:!0},tabbarItem:{name:null,icon:"",badge:null,dot:!1,text:"",badgeStyle:"top: 6px;right:2px;"},tabs:{duration:300,list:[],lineColor:"#3c9cff",activeStyle:{color:"#303133"},inactiveStyle:{color:"#606266"},lineWidth:20,lineHeight:3,lineBgSize:"cover",itemStyle:{height:"44px"},scrollable:!0,current:0,keyName:"name",iconStyle:{}},tag:{type:"primary",disabled:!1,size:"medium",shape:"square",text:"",bgColor:"",color:"",borderColor:"",closeColor:"#C6C7CB",name:"",plainFill:!1,plain:!1,closable:!1,show:!0,icon:"",iconColor:"",textSize:"",height:"",padding:"",borderRadius:"",autoBgColor:0},text:{type:"",show:!0,text:"",prefixIcon:"",suffixIcon:"",mode:"",href:"",format:"",call:!1,openType:"",bold:!1,block:!1,lines:"",color:"#303133",size:15,iconStyle:{fontSize:"15px"},decoration:"none",margin:0,lineHeight:"",align:"left",wordWrap:"normal",flex1:!0},textarea:{value:"",placeholder:"",placeholderClass:"textarea-placeholder",placeholderStyle:"color: #c0c4cc",height:70,confirmType:"done",disabled:!1,count:!1,focus:!1,autoHeight:!1,fixed:!1,cursorSpacing:0,cursor:"",showConfirmBar:!0,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,disableDefaultPadding:!1,holdKeyboard:!1,maxlength:140,border:"surround",formatter:null},toast:{zIndex:10090,loading:!1,message:"",icon:"",type:"",loadingMode:"",show:"",overlay:!1,position:"center",params:{},duration:2e3,isTab:!1,url:"",callback:null,back:!1},toolbar:{show:!0,cancelText:"取消",confirmText:"确认",cancelColor:"#909193",confirmColor:"",title:""},tooltip:{text:"",copyText:"",size:14,color:"#606266",bgColor:"transparent",direction:"top",zIndex:10071,showCopy:!0,buttons:[],overlay:!0,showToast:!0},transition:{show:!1,mode:"fade",duration:"300",timingFunction:"ease-out"},...{upload:{accept:"image",extension:[],capture:["album","camera"],compressed:!0,camera:"back",maxDuration:60,uploadIcon:"camera-fill",uploadIconColor:"#D3D4D6",useBeforeRead:!1,previewFullImage:!0,maxCount:52,disabled:!1,imageMode:"aspectFill",name:"",sizeType:["original","compressed"],multiple:!1,deletable:!0,maxSize:Number.MAX_VALUE,fileList:[],uploadText:"",width:80,height:80,previewImage:!0,autoDelete:!1,autoUpload:!1,autoUploadApi:"",autoUploadAuthUrl:"",autoUploadDriver:"",autoUploadHeader:{},getVideoThumb:!1,customAfterAutoUpload:!1}}};if(ac&&ac.upuiParams){ac.__f__("log","at node_modules/uview-plus/libs/config/props.js:206","setting uview-plus");let e=ac.upuiParams();e.httpIns&&e.httpIns(Op),e.options&&(qp=e.options,Jf(Hf,qp.config||{}),Jf(Dp,qp.props||{}),Jf(vp,qp.color||{}),Jf(yp,qp.zIndex||{}))}var qp;let Hp="none";Hp="vue3",Hp="mp",Hp="weixin";function zp(e){ip.shallowMerge(Hf,e.config||{}),ip.shallowMerge(Dp,e.props||{}),ip.shallowMerge(vp,e.color||{}),ip.shallowMerge(yp,e.zIndex||{})}ip.setConfig=zp;const Qp={route:sp,date:ip.timeFormat,colorGradient:pp.colorGradient,hexToRgb:pp.hexToRgb,rgbToHex:pp.rgbToHex,colorToRgba:pp.colorToRgba,test:Lf,type:["primary","success","error","warning","info"],http:Op,config:Hf,zIndex:yp,debounce:function(e,t=500,n=!1){if(null!==hp&&clearTimeout(hp),n){const n=!hp;hp=setTimeout((()=>{hp=null}),t),n&&"function"==typeof e&&e()}else hp=setTimeout((()=>{"function"==typeof e&&e()}),t)},throttle:gp,calc:mp,mixin:ap,mpMixin:cp,props:Dp,...ip,color:vp,platform:"weixin"},$p={install:(e,t="")=>{if(t){ac.upuiParams=t;let e=t();e.httpIns&&e.httpIns(Op),e.options&&zp(e.options)}ac.$u=Qp,e.config.globalProperties.$u=Qp,e.mixin(ap)}},Vp=e=>(t,n=Fr())=>{!Vr&&Lo(e,t,n)},Wp=Vp(te),Kp=Vp(ce),Jp=Vp(ue);function Xp(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var Gp=Xp((function(e,t){var n;e.exports=(n=n||function(e){var t=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),n={},o=n.lib={},r=o.Base={extend:function(e){var n=t(this);return e&&n.mixIn(e),n.hasOwnProperty("init")&&this.init!==n.init||(n.init=function(){n.$super.init.apply(this,arguments)}),n.init.prototype=n,n.$super=this,n},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},i=o.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||a).stringify(this)},concat:function(e){var t=this.words,n=e.words,o=this.sigBytes,r=e.sigBytes;if(this.clamp(),o%4)for(var i=0;i<r;i++){var s=n[i>>>2]>>>24-i%4*8&255;t[o+i>>>2]|=s<<24-(o+i)%4*8}else for(i=0;i<r;i+=4)t[o+i>>>2]=n[i>>>2];return this.sigBytes+=r,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,o=[],r=function(t){var n=987654321,o=4294967295;return function(){var r=((n=36969*(65535&n)+(n>>16)&o)<<16)+(t=18e3*(65535&t)+(t>>16)&o)&o;return r/=4294967296,(r+=.5)*(e.random()>.5?1:-1)}},s=0;s<t;s+=4){var a=r(4294967296*(n||e.random()));n=987654071*a(),o.push(4294967296*a()|0)}return new i.init(o,t)}}),s=n.enc={},a=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;o.push((i>>>4).toString(16)),o.push((15&i).toString(16))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o+=2)n[o>>>3]|=parseInt(e.substr(o,2),16)<<24-o%8*4;return new i.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;o.push(String.fromCharCode(i))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>2]|=(255&e.charCodeAt(o))<<24-o%4*8;return new i.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},u=o.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new i.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,o=n.words,r=n.sigBytes,s=this.blockSize,a=r/(4*s),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,l=e.min(4*c,r);if(c){for(var u=0;u<c;u+=s)this._doProcessBlock(o,u);var f=o.splice(0,c);n.sigBytes-=l}return new i.init(f,l)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=u.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){u.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}});var f=n.algo={};return n}(Math),n)})),Yp=Gp,Zp=(Xp((function(e,t){var n;e.exports=(n=Yp,function(e){var t=n,o=t.lib,r=o.WordArray,i=o.Hasher,s=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=s.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var o=t+n,r=e[o];e[o]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var i=this._hash.words,s=e[t+0],c=e[t+1],d=e[t+2],h=e[t+3],g=e[t+4],m=e[t+5],y=e[t+6],v=e[t+7],b=e[t+8],w=e[t+9],x=e[t+10],_=e[t+11],A=e[t+12],S=e[t+13],k=e[t+14],E=e[t+15],P=i[0],I=i[1],C=i[2],T=i[3];P=l(P,I,C,T,s,7,a[0]),T=l(T,P,I,C,c,12,a[1]),C=l(C,T,P,I,d,17,a[2]),I=l(I,C,T,P,h,22,a[3]),P=l(P,I,C,T,g,7,a[4]),T=l(T,P,I,C,m,12,a[5]),C=l(C,T,P,I,y,17,a[6]),I=l(I,C,T,P,v,22,a[7]),P=l(P,I,C,T,b,7,a[8]),T=l(T,P,I,C,w,12,a[9]),C=l(C,T,P,I,x,17,a[10]),I=l(I,C,T,P,_,22,a[11]),P=l(P,I,C,T,A,7,a[12]),T=l(T,P,I,C,S,12,a[13]),C=l(C,T,P,I,k,17,a[14]),P=u(P,I=l(I,C,T,P,E,22,a[15]),C,T,c,5,a[16]),T=u(T,P,I,C,y,9,a[17]),C=u(C,T,P,I,_,14,a[18]),I=u(I,C,T,P,s,20,a[19]),P=u(P,I,C,T,m,5,a[20]),T=u(T,P,I,C,x,9,a[21]),C=u(C,T,P,I,E,14,a[22]),I=u(I,C,T,P,g,20,a[23]),P=u(P,I,C,T,w,5,a[24]),T=u(T,P,I,C,k,9,a[25]),C=u(C,T,P,I,h,14,a[26]),I=u(I,C,T,P,b,20,a[27]),P=u(P,I,C,T,S,5,a[28]),T=u(T,P,I,C,d,9,a[29]),C=u(C,T,P,I,v,14,a[30]),P=f(P,I=u(I,C,T,P,A,20,a[31]),C,T,m,4,a[32]),T=f(T,P,I,C,b,11,a[33]),C=f(C,T,P,I,_,16,a[34]),I=f(I,C,T,P,k,23,a[35]),P=f(P,I,C,T,c,4,a[36]),T=f(T,P,I,C,g,11,a[37]),C=f(C,T,P,I,v,16,a[38]),I=f(I,C,T,P,x,23,a[39]),P=f(P,I,C,T,S,4,a[40]),T=f(T,P,I,C,s,11,a[41]),C=f(C,T,P,I,h,16,a[42]),I=f(I,C,T,P,y,23,a[43]),P=f(P,I,C,T,w,4,a[44]),T=f(T,P,I,C,A,11,a[45]),C=f(C,T,P,I,E,16,a[46]),P=p(P,I=f(I,C,T,P,d,23,a[47]),C,T,s,6,a[48]),T=p(T,P,I,C,v,10,a[49]),C=p(C,T,P,I,k,15,a[50]),I=p(I,C,T,P,m,21,a[51]),P=p(P,I,C,T,A,6,a[52]),T=p(T,P,I,C,h,10,a[53]),C=p(C,T,P,I,x,15,a[54]),I=p(I,C,T,P,c,21,a[55]),P=p(P,I,C,T,b,6,a[56]),T=p(T,P,I,C,E,10,a[57]),C=p(C,T,P,I,y,15,a[58]),I=p(I,C,T,P,S,21,a[59]),P=p(P,I,C,T,g,6,a[60]),T=p(T,P,I,C,_,10,a[61]),C=p(C,T,P,I,d,15,a[62]),I=p(I,C,T,P,w,21,a[63]),i[0]=i[0]+P|0,i[1]=i[1]+I|0,i[2]=i[2]+C|0,i[3]=i[3]+T|0},_doFinalize:function(){var t=this._data,n=t.words,o=8*this._nDataBytes,r=8*t.sigBytes;n[r>>>5]|=128<<24-r%32;var i=e.floor(o/4294967296),s=o;n[15+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(r+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,o,r,i,s){var a=e+(t&n|~t&o)+r+s;return(a<<i|a>>>32-i)+t}function u(e,t,n,o,r,i,s){var a=e+(t&o|n&~o)+r+s;return(a<<i|a>>>32-i)+t}function f(e,t,n,o,r,i,s){var a=e+(t^n^o)+r+s;return(a<<i|a>>>32-i)+t}function p(e,t,n,o,r,i,s){var a=e+(n^(t|~o))+r+s;return(a<<i|a>>>32-i)+t}t.MD5=i._createHelper(c),t.HmacMD5=i._createHmacHelper(c)}(Math),n.MD5)})),Xp((function(e,t){var n,o,r;e.exports=(o=(n=Yp).lib.Base,r=n.enc.Utf8,void(n.algo.HMAC=o.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),s=this._iKey=t.clone(),a=i.words,c=s.words,l=0;l<n;l++)a[l]^=1549556828,c[l]^=909522486;i.sigBytes=s.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),Xp((function(e,t){e.exports=Yp.HmacMD5}))),ed=Xp((function(e,t){e.exports=Yp.enc.Utf8})),td=Xp((function(e,t){var n,o,r;e.exports=(r=(o=n=Yp).lib.WordArray,o.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,o=this._map;e.clamp();for(var r=[],i=0;i<n;i+=3)for(var s=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,a=0;a<4&&i+.75*a<n;a++)r.push(o.charAt(s>>>6*(3-a)&63));var c=o.charAt(64);if(c)for(;r.length%4;)r.push(c);return r.join("")},parse:function(e){var t=e.length,n=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<n.length;i++)o[n.charCodeAt(i)]=i}var s=n.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return function(e,t,n){for(var o=[],i=0,s=0;s<t;s++)if(s%4){var a=n[e.charCodeAt(s-1)]<<s%4*2,c=n[e.charCodeAt(s)]>>>6-s%4*2;o[i>>>2]|=(a|c)<<24-i%4*8,i++}return r.create(o,i)}(e,t,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const nd="FUNCTION",od="pending",rd="rejected";function id(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function sd(e){return"object"===id(e)}function ad(e){return"function"==typeof e}function cd(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const ld="REJECTED",ud="NOT_PENDING";class fd{constructor({createPromise:e,retryRule:t=ld}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case ld:return this.status===rd;case ud:return this.status!==od}}exec(){return this.needRetry?(this.status=od,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=rd,Promise.reject(e)))),this.promise):this.promise}}function pd(e){return e&&"string"==typeof e?JSON.parse(e):e}const dd=pd([]),hd="mp-weixin",gd=pd(""),md=pd("[]")||[];let yd="";try{yd="__UNI__0820E41"}catch(ah){}let vd={};function bd(e,t={}){var n,o;return n=vd,o=e,Object.prototype.hasOwnProperty.call(n,o)||(vd[e]=t),vd[e]}const wd=["invoke","success","fail","complete"],xd=bd("_globalUniCloudInterceptor");function _d(e,t){xd[e]||(xd[e]={}),sd(t)&&Object.keys(t).forEach((n=>{wd.indexOf(n)>-1&&function(e,t,n){let o=xd[e][t];o||(o=xd[e][t]=[]),-1===o.indexOf(n)&&ad(n)&&o.push(n)}(e,n,t[n])}))}function Ad(e,t){xd[e]||(xd[e]={}),sd(t)?Object.keys(t).forEach((n=>{wd.indexOf(n)>-1&&function(e,t,n){const o=xd[e][t];if(!o)return;const r=o.indexOf(n);r>-1&&o.splice(r,1)}(e,n,t[n])})):delete xd[e]}function Sd(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function kd(e,t){return xd[e]&&xd[e][t]||[]}function Ed(e){_d("callObject",e)}const Pd=bd("_globalUniCloudListener"),Id="response",Cd="needLogin",Td="refreshToken",Bd="clientdb",Od="cloudfunction",Ld="cloudobject";function Nd(e){return Pd[e]||(Pd[e]=[]),Pd[e]}function Rd(e,t){const n=Nd(e);n.includes(t)||n.push(t)}function Md(e,t){const n=Nd(e),o=n.indexOf(t);-1!==o&&n.splice(o,1)}function Fd(e,t){const n=Nd(e);for(let o=0;o<n.length;o++)(0,n[o])(t)}let Ud,Dd=!1;function qd(){return Ud||(Ud=new Promise((e=>{Dd&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(Dd=!0,e())}Dd||setTimeout((()=>{t()}),30)}()})),Ud)}function Hd(e){const t={};for(const n in e){const o=e[n];ad(o)&&(t[n]=cd(o))}return t}class zd extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var Qd={request:e=>ac.request(e),uploadFile:e=>ac.uploadFile(e),setStorageSync:(e,t)=>ac.setStorageSync(e,t),getStorageSync:e=>ac.getStorageSync(e),removeStorageSync:e=>ac.removeStorageSync(e),clearStorageSync:()=>ac.clearStorageSync(),connectSocket:e=>ac.connectSocket(e)};function $d(e){return e&&$d(e.__v_raw)||e}function Vd(){return{token:Qd.getStorageSync("uni_id_token")||Qd.getStorageSync("uniIdToken"),tokenExpired:Qd.getStorageSync("uni_id_token_expired")}}function Wd({token:e,tokenExpired:t}={}){e&&Qd.setStorageSync("uni_id_token",e),t&&Qd.setStorageSync("uni_id_token_expired",t)}let Kd,Jd;function Xd(){return Kd||(Kd=ac.getSystemInfoSync()),Kd}function Gd(){let e,t;try{if(ac.getLaunchOptionsSync){if(ac.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:o}=ac.getLaunchOptionsSync();e=o,t=n}}catch(n){}return{channel:e,scene:t}}let Yd={};function Zd(){const e=ac.getLocale&&ac.getLocale()||"en";if(Jd)return{...Yd,...Jd,locale:e,LOCALE:e};const t=Xd(),{deviceId:n,osName:o,uniPlatform:r,appId:i}=t,s=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const a in t)Object.hasOwnProperty.call(t,a)&&-1===s.indexOf(a)&&delete t[a];return Jd={PLATFORM:r,OS:o,APPID:i,DEVICEID:n,...Gd(),...t},{...Yd,...Jd,locale:e,LOCALE:e}}var eh=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),Zp(n,t).toString()},th=function(e,t){return new Promise(((n,o)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",r=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return o(new zd({code:n,message:r,requestId:t}))}const r=e.data;if(r.error)return o(new zd({code:r.error.code,message:r.error.message,requestId:t}));r.result=r.data,r.requestId=t,delete r.data,n(r)}}))}))},nh=function(e){return td.stringify(ed.parse(e))},oh=class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=Qd,this._getAccessTokenPromiseHub=new fd({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new zd({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:ud})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return th(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=eh(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),o={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,o["x-basement-token"]=this.accessToken),o["x-serverless-sign"]=eh(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:o}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:o,fileType:r,onUploadProgress:i}){return new Promise(((s,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:o,fileType:r,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?s(e):a(new zd({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new zd({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:o=!1,onUploadProgress:r,config:i}){if("string"!==id(t))throw new zd({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new zd({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new zd({code:"INVALID_PARAM",message:"cloudPath不合法"});const s=i&&i.envType||this.config.envType;if(o&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new zd({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:s,filename:o?t.split("/").pop():t,fileId:o?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:l,accessKeyId:u,signature:f,host:p,ossPath:d,id:h,policy:g,ossCallbackUrl:m}=a,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:f,host:p,id:h,key:d,policy:g,success_action_status:200};if(l&&(y["x-oss-security-token"]=l),m){const e=JSON.stringify({callbackUrl:m,callbackBody:JSON.stringify({fileId:h,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=nh(e)}const v={url:"https://"+a.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},v,{onUploadProgress:r})),m)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:h})).success)return{success:!0,filePath:e,fileID:c};throw new zd({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new zd({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new zd({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}},rh={init(e){const t=new oh(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const ih="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var sh,ah;(ah=sh||(sh={})).local="local",ah.none="none",ah.session="session";var ch=function(){},lh=Xp((function(e,t){var n;e.exports=(n=Yp,function(e){var t=n,o=t.lib,r=o.WordArray,i=o.Hasher,s=t.algo,a=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),o=2;o<=n;o++)if(!(t%o))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var o=2,r=0;r<64;)t(o)&&(r<8&&(a[r]=n(e.pow(o,.5))),c[r]=n(e.pow(o,1/3)),r++),o++}();var l=[],u=s.SHA256=i.extend({_doReset:function(){this._hash=new r.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],r=n[1],i=n[2],s=n[3],a=n[4],u=n[5],f=n[6],p=n[7],d=0;d<64;d++){if(d<16)l[d]=0|e[t+d];else{var h=l[d-15],g=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,m=l[d-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[d]=g+l[d-7]+y+l[d-16]}var v=o&r^o&i^r&i,b=(o<<30|o>>>2)^(o<<19|o>>>13)^(o<<10|o>>>22),w=p+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&f)+c[d]+l[d];p=f,f=u,u=a,a=s+w|0,s=i,i=r,r=o,o=w+(b+v)|0}n[0]=n[0]+o|0,n[1]=n[1]+r|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+f|0,n[7]=n[7]+p|0},_doFinalize:function(){var t=this._data,n=t.words,o=8*this._nDataBytes,r=8*t.sigBytes;return n[r>>>5]|=128<<24-r%32,n[14+(r+64>>>9<<4)]=e.floor(o/4294967296),n[15+(r+64>>>9<<4)]=o,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(u),t.HmacSHA256=i._createHmacHelper(u)}(Math),n.SHA256)})),uh=lh,fh=Xp((function(e,t){e.exports=Yp.HmacSHA256}));const ph=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new zd({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,o)=>e?n(e):t(o)}));return e.promise=t,e};function dh(e){return void 0===e}function hh(e){return"[object Null]"===Object.prototype.toString.call(e)}function gh(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function mh(e=32){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let n="";for(let o=0;o<e;o++)n+=t.charAt(Math.floor(62*Math.random()));return n}var yh;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(yh||(yh={}));const vh={adapter:null,runtime:void 0},bh=["anonymousUuidKey"];class wh extends ch{constructor(){super(),vh.adapter.root.tcbObject||(vh.adapter.root.tcbObject={})}setItem(e,t){vh.adapter.root.tcbObject[e]=t}getItem(e){return vh.adapter.root.tcbObject[e]}removeItem(e){delete vh.adapter.root.tcbObject[e]}clear(){delete vh.adapter.root.tcbObject}}function xh(e,t){switch(e){case"local":return t.localStorage||new wh;case"none":return new wh;default:return t.sessionStorage||new wh}}class _h{constructor(e){if(!this._storage){this._persistence=vh.adapter.primaryStorage||e.persistence,this._storage=xh(this._persistence,vh.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,o=`refresh_token_${e.env}`,r=`anonymous_uuid_${e.env}`,i=`login_type_${e.env}`,s="device_id",a=`token_type_${e.env}`,c=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o,anonymousUuidKey:r,loginTypeKey:i,userInfoKey:c,deviceIdKey:s,tokenTypeKey:a}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=xh(e,vh.adapter);for(const o in this.keys){const e=this.keys[o];if(t&&bh.includes(o))continue;const r=this._storage.getItem(e);dh(r)||hh(r)||(n.setItem(e,r),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const o={version:n||"localCachev1",content:t},r=JSON.stringify(o);try{this._storage.setItem(e,r)}catch(i){throw i}}getStore(e,t){try{if(!this._storage)return}catch(o){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const Ah={},Sh={};function kh(e){return Ah[e]}class Eh{constructor(e,t){this.data=t||null,this.name=e}}class Ph extends Eh{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Ih=new class{constructor(){this._listeners={}}on(e,t){return n=e,o=t,(r=this._listeners)[n]=r[n]||[],r[n].push(o),this;var n,o,r}off(e,t){return function(e,t,n){if(n&&n[e]){const o=n[e].indexOf(t);-1!==o&&n[e].splice(o,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Ph)return console.error(e.error),this;const n="string"==typeof e?new Eh(e,t||{}):e,o=n.name;if(this._listens(o)){n.target=this;const e=this._listeners[o]?[...this._listeners[o]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Ch(e,t){Ih.on(e,t)}function Th(e,t={}){Ih.fire(e,t)}function Bh(e,t){Ih.off(e,t)}const Oh="loginStateChanged",jh="loginStateExpire",Lh="loginTypeChanged",Nh="anonymousConverted",Rh="refreshAccessToken";var Mh;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(Mh||(Mh={}));class Fh{constructor(){this._fnPromiseMap=new Map}async run(e,t){let n=this._fnPromiseMap.get(e);return n||(n=new Promise((async(n,o)=>{try{await this._runIdlePromise();const e=t();n(await e)}catch(r){o(r)}finally{this._fnPromiseMap.delete(e)}})),this._fnPromiseMap.set(e,n)),n}_runIdlePromise(){return Promise.resolve()}}class Uh{constructor(e){this._singlePromise=new Fh,this._cache=kh(e.env),this._baseURL=`https://${e.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new vh.adapter.reqClass({timeout:e.timeout,timeoutMsg:`请求在${e.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:e}=this._cache.keys;let t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=mh(),this._cache.setStore(e,t)),this._deviceID=t,t}async _request(e,t,n={}){const o={"x-request-id":mh(),"x-device-id":this._getDeviceId()};if(n.withAccessToken){const{tokenTypeKey:e}=this._cache.keys,t=await this.getAccessToken(),n=this._cache.getStore(e);o.authorization=`${n} ${t}`}return this._reqClass["get"===n.method?"get":"post"]({url:`${this._baseURL}${e}`,data:t,headers:o})}async _fetchAccessToken(){const{loginTypeKey:e,accessTokenKey:t,accessTokenExpireKey:n,tokenTypeKey:o}=this._cache.keys,r=this._cache.getStore(e);if(r&&r!==Mh.ANONYMOUS)throw new zd({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const i=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:s,expires_in:a,token_type:c}=i;return this._cache.setStore(o,c),this._cache.setStore(t,s),this._cache.setStore(n,Date.now()+1e3*a),s}isAccessTokenExpired(e,t){let n=!0;return e&&t&&(n=t<Date.now()),n}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),o=this._cache.getStore(t);return this.isAccessTokenExpired(n,o)?this._fetchAccessToken():n}async refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,loginTypeKey:n}=this._cache.keys;return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.setStore(n,Mh.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const Dh=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],qh={"X-SDK-Version":"1.3.5"};function Hh(e,t,n){const o=e[t];e[t]=function(t){const r={},i={};n.forEach((n=>{const{data:o,headers:s}=n.call(e,t);Object.assign(r,o),Object.assign(i,s)}));const s=t.data;return s&&(()=>{var e;if(e=s,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...s,...r};else for(const t in r)s.append(t,r[t])})(),t.headers={...t.headers||{},...i},o.call(e,t)}}function zh(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...qh,"x-seqid":e}}}class Qh{constructor(e={}){var t;this.config=e,this._reqClass=new vh.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=kh(this.config.env),this._localCache=(t=this.config.env,Sh[t]),this.oauth=new Uh(this.config),Hh(this._reqClass,"post",[zh]),Hh(this._reqClass,"upload",[zh]),Hh(this._reqClass,"download",[zh])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:o,anonymousUuidKey:r}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let i=this._cache.getStore(n);if(!i)throw new zd({message:"未登录CloudBase"});const s={refresh_token:i},a=await this.request("auth.fetchAccessTokenWithRefreshToken",s);if(a.data.code){const{code:e}=a.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(o)===Mh.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(r),t=this._cache.getStore(n),o=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(o.refresh_token),this._refreshAccessToken()}Th(jh),this._cache.removeStore(n)}throw new zd({code:a.data.code,message:`刷新access token失败：${a.data.code}`})}if(a.data.access_token)return Th(Rh),this._cache.setStore(e,a.data.access_token),this._cache.setStore(t,a.data.access_token_expire+Date.now()),{accessToken:a.data.access_token,accessTokenExpire:a.data.access_token_expire};a.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,a.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new zd({message:"refresh token不存在，登录状态异常"});let o=this._cache.getStore(e),r=this._cache.getStore(t),i=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(o,r))&&(i=!1),(!o||!r||r<Date.now())&&i?this.refreshAccessToken():{accessToken:o,accessTokenExpire:r}}async request(e,t,n){const o=`x-tcb-trace_${this.config.env}`;let r="application/x-www-form-urlencoded";const i={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};let s;if(-1===Dh.indexOf(e)&&(this._cache.keys,i.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===e){s=new FormData;for(let e in s)s.hasOwnProperty(e)&&void 0!==s[e]&&s.append(e,i[e]);r="multipart/form-data"}else{r="application/json",s={};for(let e in i)void 0!==i[e]&&(s[e]=i[e])}let a={headers:{"content-type":r}};n&&n.timeout&&(a.timeout=n.timeout),n&&n.onUploadProgress&&(a.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(o);c&&(a.headers["X-TCB-Trace"]=c);const{parse:l,inQuery:u,search:f}=t;let p={env:this.config.env};l&&(p.parse=!0),u&&(p={...u,...p});let d=function(e,t,n={}){const o=/\?/.test(t);let r="";for(let i in n)""===r?!o&&(t+="?"):r+="&",r+=`${i}=${encodeURIComponent(n[i])}`;return/^http(s)?\:\/\//.test(t+=r)?t:`${e}${t}`}(ih,"//tcb-api.tencentcloudapi.com/web",p);f&&(d+=f);const h=await this.post({url:d,data:s,...a}),g=h.header&&h.header["x-tcb-trace"];if(g&&this._localCache.setStore(o,g),200!==Number(h.status)&&200!==Number(h.statusCode)||!h.data)throw new zd({code:"NETWORK_ERROR",message:"network request error"});return h}async send(e,t={},n={}){const o=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===o.data.code||"ACCESS_TOKEN_EXPIRED"===o.data.code)&&-1===Dh.indexOf(e)){await this.oauth.refreshAccessToken();const o=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(o.data.code)throw new zd({code:o.data.code,message:gh(o.data.message)});return o.data}if(o.data.code)throw new zd({code:o.data.code,message:gh(o.data.message)});return o.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(o,e)}}const $h={};function Vh(e){return $h[e]}class Wh{constructor(e){this.config=e,this._cache=kh(e.env),this._request=Vh(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(o,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:o}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(o,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class Kh{constructor(e){if(!e)throw new zd({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=kh(this._envId),this._request=Vh(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new zd({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new zd({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:o,province:r,country:i,city:s}=e,{data:a}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:o,province:r,country:i,city:s});this.setLocalUserInfo(a)}async refresh(){const e=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class Jh{constructor(e){if(!e)throw new zd({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=kh(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:o}=this._cache.keys,r=this._cache.getStore(t),i=this._cache.getStore(n),s=this._cache.getStore(o);this.credential={refreshToken:r,accessToken:i,accessTokenExpire:s},this.user=new Kh(e)}get isAnonymousAuth(){return this.loginType===Mh.ANONYMOUS}get isCustomAuth(){return this.loginType===Mh.CUSTOM}get isWeixinAuth(){return this.loginType===Mh.WECHAT||this.loginType===Mh.WECHAT_OPEN||this.loginType===Mh.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class Xh extends Wh{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),Th(Oh),Th(Lh,{env:this.config.env,loginType:Mh.ANONYMOUS,persistence:"local"});const e=new Jh(this.config.env);return await e.user.refresh(),e}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,o=this._cache.getStore(t),r=this._cache.getStore(n),i=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:o,refresh_token:r,ticket:e});if(i.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),Th(Nh,{env:this.config.env}),Th(Lh,{loginType:Mh.CUSTOM,persistence:"local"}),{credential:{refreshToken:i.refresh_token}};throw new zd({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,Mh.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class Gh extends Wh{async signIn(e){if("string"!=typeof e)throw new zd({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Th(Oh),Th(Lh,{env:this.config.env,loginType:Mh.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new Jh(this.config.env);throw new zd({message:"自定义登录失败"})}}class Yh extends Wh{async signIn(e,t){if("string"!=typeof e)throw new zd({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,o=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:r,access_token:i,access_token_expire:s}=o;if(r)return this.setRefreshToken(r),i&&s?this.setAccessToken(i,s):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Th(Oh),Th(Lh,{env:this.config.env,loginType:Mh.EMAIL,persistence:this.config.persistence}),new Jh(this.config.env);throw o.code?new zd({code:o.code,message:`邮箱登录失败: ${o.message}`}):new zd({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class Zh extends Wh{async signIn(e,t){if("string"!=typeof e)throw new zd({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,o=await this._request.send("auth.signIn",{loginType:Mh.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:r,access_token_expire:i,access_token:s}=o;if(r)return this.setRefreshToken(r),s&&i?this.setAccessToken(s,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Th(Oh),Th(Lh,{env:this.config.env,loginType:Mh.USERNAME,persistence:this.config.persistence}),new Jh(this.config.env);throw o.code?new zd({code:o.code,message:`用户名密码登录失败: ${o.message}`}):new zd({message:"用户名密码登录失败"})}}class eg{constructor(e){this.config=e,this._cache=kh(e.env),this._request=Vh(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Ch(Lh,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new Xh(this.config)}customAuthProvider(){return new Gh(this.config)}emailAuthProvider(){return new Yh(this.config)}usernameAuthProvider(){return new Zh(this.config)}async signInAnonymously(){return new Xh(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new Yh(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new Zh(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new Xh(this.config)),Ch(Nh,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===Mh.ANONYMOUS)throw new zd({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,o=this._cache.getStore(e);if(!o)return;const r=await this._request.send("auth.logout",{refresh_token:o});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),Th(Oh),Th(Lh,{env:this.config.env,loginType:Mh.NULL,persistence:this.config.persistence}),r}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){Ch(Oh,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){Ch(jh,e.bind(this))}onAccessTokenRefreshed(e){Ch(Rh,e.bind(this))}onAnonymousConverted(e){Ch(Nh,e.bind(this))}onLoginTypeChanged(e){Ch(Lh,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),o=this._cache.getStore(t);return this._request.oauth.isAccessTokenExpired(n,o)?null:new Jh(this.config.env)}async isUsernameRegistered(e){if("string"!=typeof e)throw new zd({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new Gh(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:o}=e.data;o===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const tg=function(e,t){t=t||ph();const n=Vh(this.config.env),{cloudPath:o,filePath:r,onUploadProgress:i,fileType:s="image"}=e;return n.send("storage.getUploadMetadata",{path:o}).then((e=>{const{data:{url:a,authorization:c,token:l,fileId:u,cosFileId:f},requestId:p}=e,d={key:o,signature:c,"x-cos-meta-fileid":f,success_action_status:"201","x-cos-security-token":l};n.upload({url:a,data:d,file:r,name:o,fileType:s,onUploadProgress:i}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:p}):t(new zd({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},ng=function(e,t){t=t||ph();const n=Vh(this.config.env),{cloudPath:o}=e;return n.send("storage.getUploadMetadata",{path:o}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},og=function({fileList:e},t){if(t=t||ph(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let o of e)if(!o||"string"!=typeof o)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return Vh(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},rg=function({fileList:e},t){t=t||ph(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let r of e)"object"==typeof r?(r.hasOwnProperty("fileID")&&r.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:r.fileID,max_age:r.maxAge})):"string"==typeof r?n.push({fileid:r}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const o={file_list:n};return Vh(this.config.env).send("storage.batchGetDownloadUrl",o).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},ig=async function({fileID:e},t){const n=(await rg.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const o=Vh(this.config.env);let r=n.download_url;if(r=encodeURI(r),!t)return o.download({url:r});t(await o.download({url:r}))},sg=function({name:e,data:t,query:n,parse:o,search:r,timeout:i},s){const a=s||ph();let c;try{c=t?JSON.stringify(t):""}catch(u){return Promise.reject(u)}if(!e)return Promise.reject(new zd({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:o,search:r,function_name:e,request_data:c};return Vh(this.config.env).send("functions.invokeFunction",l,{timeout:i}).then((e=>{if(e.code)a(null,e);else{let n=e.data.response_data;if(o)a(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),a(null,{result:n,requestId:e.requestId})}catch(t){a(new zd({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},ag={timeout:15e3,persistence:"session"},cg={};class lg{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(vh.adapter||(this.requestClient=new vh.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...ag,...e},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new lg(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||vh.adapter.primaryStorage||ag.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Ah[t]=new _h(e),Sh[t]=new _h({...e,persistence:"local"})}(this.config),n=this.config,$h[n.env]=new Qh(n),this.authObj=new eg(this.config),this.authObj}on(e,t){return Ch.apply(this,[e,t])}off(e,t){return Bh.apply(this,[e,t])}callFunction(e,t){return sg.apply(this,[e,t])}deleteFile(e,t){return og.apply(this,[e,t])}getTempFileURL(e,t){return rg.apply(this,[e,t])}downloadFile(e,t){return ig.apply(this,[e,t])}uploadFile(e,t){return tg.apply(this,[e,t])}getUploadMetadata(e,t){return ng.apply(this,[e,t])}registerExtension(e){cg[e.name]=e}async invokeExtension(e,t){const n=cg[e];if(!n)throw new zd({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const o of t){const{isMatch:e,genAdapter:t,runtime:n}=o;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&(vh.adapter=t),n&&(vh.runtime=n)}}var ug=new lg;function fg(e,t,n){void 0===n&&(n={});var o=/\?/.test(t),r="";for(var i in n)""===r?!o&&(t+="?"):r+="&",r+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=r)?t:""+e+t}class pg{get(e){const{url:t,data:n,headers:o,timeout:r}=e;return new Promise(((e,i)=>{Qd.request({url:fg("https:",t),data:n,method:"GET",header:o,timeout:r,success(t){e(t)},fail(e){i(e)}})}))}post(e){const{url:t,data:n,headers:o,timeout:r}=e;return new Promise(((e,i)=>{Qd.request({url:fg("https:",t),data:n,method:"POST",header:o,timeout:r,success(t){e(t)},fail(e){i(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:o,file:r,data:i,headers:s,fileType:a}=e,c=Qd.uploadFile({url:fg("https:",o),name:"file",formData:Object.assign({},i),filePath:r,fileType:a,header:s,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const dg={setItem(e,t){Qd.setStorageSync(e,t)},getItem:e=>Qd.getStorageSync(e),removeItem(e){Qd.removeStorageSync(e)},clear(){Qd.clearStorageSync()}};var hg={genAdapter:function(){return{root:{},reqClass:pg,localStorage:dg,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};ug.useAdapters(hg);const gg=ug,mg=gg.init;gg.init=function(e){e.env=e.spaceId;const t=mg.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:o,complete:r}=Hd(e);if(!(t||o||r))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),r&&r(e)}),(e=>{o&&o(e),r&&r(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var yg=gg;async function vg(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(o={url:n,timeout:500},new Promise(((e,t)=>{Qd.request({...o,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(r){return!1}var o}async function bg(e,t){let n;for(let o=0;o<e.length;o++){const r=e[o];if(await vg(r,t)){n=r;break}}return{address:n,port:t}}const wg={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var xg=class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=Qd}async request(e,t=!0){const n=t;return e=n?await this.setupLocalRequest(e):this.setupRequest(e),Promise.resolve().then((()=>n?this.requestLocal(e):th(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",o=e.data&&e.data.message||"request:fail";return n(new zd({code:t,message:o}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=eh(t,this.config.clientSecret);const o=Zd();n["x-client-info"]=encodeURIComponent(JSON.stringify(o));const{token:r}=Vd();return n["x-client-token"]=r,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=Zd(),{token:n}=Vd(),o=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:r,servePort:i}=this.__dev__&&this.__dev__.debugInfo||{},{address:s}=await bg(r,i);return{url:`http://${s}:${i}/${wg[e.method]}`,method:"POST",data:o,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:o}){if(!t)throw new zd({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let r;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:i,formData:s,name:a}=t.result;return r=t.result.fileUrl,new Promise(((t,r)=>{const c=this.adapter.uploadFile({url:i,formData:s,name:a,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):r(new zd({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){r(new zd({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,o)=>{t.success?n({success:!0,filePath:e,fileID:r}):o(new zd({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new zd({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new zd({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new zd({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}},_g={init(e){const t=new xg(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Ag=Xp((function(e,t){e.exports=Yp.enc.Hex}));function Sg(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function kg(e="",t={}){const{data:n,functionName:o,method:r,headers:i,signHeaderKeys:s=[],config:a}=t,c=String(Date.now()),l=Sg(),u=Object.assign({},i,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":o,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":l,"x-alipay-callid":l,"x-trace-id":l}),f=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(s),[p="",d=""]=e.split("?")||[],h=function(e){const t=e.signedHeaders.join(";"),n=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),o=uh(e.body).toString(Ag),r=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${n}\n${t}\n${o}\n`,i=uh(r).toString(Ag),s=`HMAC-SHA256\n${e.timestamp}\n${i}\n`,a=fh(s,e.secretKey).toString(Ag);return`HMAC-SHA256 Credential=${e.secretId}, SignedHeaders=${t}, Signature=${a}`}({path:p,query:d,method:r,headers:u,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:f.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},u,{Authorization:h})}}function Eg({url:e,data:t,method:n="POST",headers:o={},timeout:r}){return new Promise(((i,s)=>{Qd.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:o,dataType:"json",timeout:r,complete:(e={})=>{const t=o["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:o,trace_id:r}=e.data||{};return s(new zd({code:"SYS_ERR",message:n||o||"request:fail",requestId:r||t}))}i({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Pg(e,t){const{path:n,data:o,method:r="GET"}=e,{url:i,headers:s}=kg(n,{functionName:"",data:o,method:r,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Eg({url:i,data:o,method:r,headers:s}).then((e=>{const t=e.data||{};if(!t.success)throw new zd({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new zd({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Ig(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new zd({code:"INVALID_PARAM",message:"fileID不合法"});const o=t.substring(0,n),r=t.substring(n+1);return o!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),r}function Cg(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class Tg{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,o=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),r=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Sg(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return r[e]?"".concat(e,"=").concat(r[e]):null})).filter(Boolean).join("&"),`host:${o}`].join("\n"),s=["HMAC-SHA256",uh(i).toString(Ag)].join("\n"),a=fh(s,this.config.secretKey).toString(Ag),c=Object.keys(r).map((e=>`${e}=${encodeURIComponent(r[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${a}`}}var Bg=class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Tg(this.config)}callFunction(e){return function(e,t){const{name:n,data:o,async:r=!1,timeout:i}=e,s="POST",a={"x-to-function-name":n};r&&(a["x-function-invoke-type"]="async");const{url:c,headers:l}=kg("/functions/invokeFunction",{functionName:n,data:o,method:s,headers:a,signHeaderKeys:["x-to-function-name"],config:t});return Eg({url:c,data:o,method:s,headers:l,timeout:i}).then((e=>{let t=0;if(r){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new zd({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new zd({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:o,onUploadProgress:r}){return new Promise(((i,s)=>{const a=Qd.uploadFile({url:e,filePath:t,fileType:n,formData:o,name:"file",success(e){e&&e.statusCode<400?i(e):s(new zd({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){s(new zd({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:o}){if("string"!==id(t))throw new zd({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new zd({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new zd({code:"INVALID_PARAM",message:"cloudPath不合法"});const r=await Pg({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:i,upload_url:s,form_data:a}=r,c=a&&a.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:s,filePath:e,fileType:n,formData:c,onUploadProgress:o}).then((()=>({fileID:i})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const o=[];for(const i of e){let e;"string"!==id(i)&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{e=Ig.call(this,i)}catch(r){console.warn(r.errCode,r.errMsg),e=i}o.push({file_id:e,expire:600})}Pg({path:"/?download_url",data:{file_list:o},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Cg.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return Qd.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}},Og={init:e=>{e.provider="alipay";const t=new Bg(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function jg({data:e}){let t;t=Zd();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=Vd();e&&(n.uniIdToken=e)}return n}async function Lg(e={}){await this.__dev__.initLocalNetwork();const{localAddress:t,localPort:n}=this.__dev__,o={aliyun:"aliyun",tencent:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],r=this.config.spaceId,i=`http://${t}:${n}/system/check-function`,s=`http://${t}:${n}/cloudfunctions/${e.name}`;return new Promise(((t,n)=>{Qd.request({method:"POST",url:i,data:{name:e.name,platform:hd,provider:o,spaceId:r},timeout:3e3,success(e){t(e)},fail(){t({data:{code:"NETWORK_ERROR",message:"连接本地调试服务失败，请检查客户端是否和主机在同一局域网下，自动切换为已部署的云函数。"}})}})})).then((({data:e}={})=>{const{code:t,message:n}=e||{};return{code:0===t?0:t||"SYS_ERR",message:n||"SYS_ERR"}})).then((({code:t,message:n})=>{if(0!==t){switch(t){case"MODULE_ENCRYPTED":console.error(`此云函数（${e.name}）依赖加密公共模块不可本地调试，自动切换为云端已部署的云函数`);break;case"FUNCTION_ENCRYPTED":console.error(`此云函数（${e.name}）已加密不可本地调试，自动切换为云端已部署的云函数`);break;case"ACTION_ENCRYPTED":console.error(n||"需要访问加密的uni-clientDB-action，自动切换为云端环境");break;case"NETWORK_ERROR":console.error(n||"连接本地调试服务失败，请检查客户端是否和主机在同一局域网下");break;case"SWITCH_TO_CLOUD":break;default:{const e=`检测本地调试服务出现错误：${n}，请检查网络环境或重启客户端再试`;throw console.error(e),new Error(e)}}return this._callCloudFunction(e)}return new Promise(((t,n)=>{const r=jg.call(this,{data:e.data});Qd.request({method:"POST",url:s,data:{provider:o,platform:hd,param:r},timeout:e.timeout,success:({statusCode:e,data:o}={})=>!e||e>=400?n(new zd({code:o.code||"SYS_ERR",message:o.message||"request:fail"})):t({result:o}),fail(e){n(new zd({code:e.code||e.errCode||"SYS_ERR",message:e.message||e.errMsg||"request:fail"}))}})}))}))}const Ng=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var Rg=/[\\^$.*+?()[\]{}|]/g,Mg=RegExp(Rg.source);function Fg(e,t,n){return e.replace(new RegExp((o=t)&&Mg.test(o)?o.replace(Rg,"\\$&"):o,"g"),n);var o}const Ug=2e4,Dg={code:20101,message:"Invalid client"};function qg(e){const{errSubject:t,subject:n,errCode:o,errMsg:r,code:i,message:s,cause:a}=e||{};return new zd({subject:t||n||"uni-secure-network",code:o||i||Ug,message:r||s,cause:a})}let Hg;function zg({secretType:e}={}){return"request"===e||"response"===e||"both"===e}function Qg({name:e,data:t={}}={}){return"app"===hd}function $g({functionName:e,result:t,logPvd:n}){if(this.__dev__.debugLog&&t&&t.requestId){const o=JSON.stringify({spaceId:this.config.spaceId,functionName:e,requestId:t.requestId});console.log(`[${n}-request]${o}[/${n}-request]`)}}function Vg(e){const t=e.callFunction,n=function(n){const o=n.name;n.data=jg.call(e,{data:n.data});const r={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],i=zg(n),s=Qg(n),a=i||s;return t.call(this,n).then((e=>(e.errCode=0,!a&&$g.call(this,{functionName:o,result:e,logPvd:r}),Promise.resolve(e))),(e=>(!a&&$g.call(this,{functionName:o,result:e,logPvd:r}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let o=0;o<n.length;o++){const{rule:r,content:i,mode:s}=n[o],a=e.match(r);if(!a)continue;let c=i;for(let e=1;e<a.length;e++)c=Fg(c,`{$${e}}`,a[e]);for(const e in t)c=Fg(c,`{${e}}`,t[e]);return"replace"===s?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:Ng,extraInfo:{functionName:o}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:o,spaceId:r}=e.config,i=t.name;let s,a;return t.data=t.data||{},e.__dev__.debugInfo&&!e.__dev__.debugInfo.forceRemote&&md?(e._callCloudFunction||(e._callCloudFunction=n,e._callLocalFunction=Lg),s=Lg):s=n,s=s.bind(e),Qg(t)||(a=function({name:e,data:t={}}){return"uni-id-co"===e&&"secureNetworkHandshakeByWeixin"===t.method}(t)?s.call(e,t):zg(t)?new Hg({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:o,uniPlatform:r,osName:i}=Xd();let s=r;"app"===r&&(s=i);const a=function({provider:e,spaceId:t}={}){const n=dd;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const o=n.find((n=>n.provider===e&&n.spaceId===t));return o&&o.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},l=Object.keys(c);if(0===l.length)return!0;const u=function(e,t){let n,o,r;for(let i=0;i<e.length;i++){const s=e[i];s!==t?"*"!==s?s.split(",").map((e=>e.trim())).indexOf(t)>-1&&(o=s):r=s:n=s}return n||o||r}(l,n);if(!u)return!1;if((c[u]||[]).find(((e={})=>e.appId===o&&(e.platform||"").toLowerCase()===s.toLowerCase())))return!0;throw console.error(`此应用[appId: ${o}, platform: ${s}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),qg(Dg)}({provider:o,spaceId:r,functionName:i})?new Hg({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):s(t)),Object.defineProperty(a,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),a.then((e=>("undefined"!=typeof UTSJSONObject&&"undefined"!=typeof UTS&&(e.result=UTS.JSON.parse(JSON.stringify(e.result))),e)))}}Hg=class{constructor(){throw qg({message:`Platform ${hd} is not enabled, please check whether secure network module is enabled in your manifest.json`})}};const Wg=Symbol("CLIENT_DB_INTERNAL");function Kg(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=Wg,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,o){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,o)}})}function Jg(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const o=e[t].indexOf(n);-1!==o&&e[t].splice(o,1)}}}const Xg=["db.Geo","db.command","command.aggregate"];function Gg(e,t){return Xg.indexOf(`${e}.${t}`)>-1}function Yg(e){switch(id(e=$d(e))){case"array":return e.map((e=>Yg(e)));case"object":return e._internalType===Wg||Object.keys(e).forEach((t=>{e[t]=Yg(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function Zg(e){return e&&e.content&&e.content.$method}class em{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:Yg(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=Zg(e),n=Zg(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===Zg(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=Zg(e),n=Zg(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return tm({$method:e,$param:Yg(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),o=this.getCommand();o.$db.push({$method:e,$param:Yg(t)});{const e=o.$db.find((e=>"collection"===e.$method)),t=e&&e.$param;t&&1===t.length&&"string"==typeof e.$param[0]&&e.$param[0].indexOf(",")>-1&&console.warn("检测到使用JQL语法联表查询时，未使用getTemp先过滤主表数据，在主表数据量大的情况下可能会查询缓慢。\n- 如何优化请参考此文档：https://uniapp.dcloud.net.cn/uniCloud/jql?id=lookup-with-temp \n- 如果主表数据量很小请忽略此信息，项目发行时不会出现此提示。")}return this._database._callCloudFunction({action:n,command:o})}}function tm(e,t,n){return Kg(new em(e,t,n),{get(e,t){let o="db";return e&&e.content&&(o=e.content.$method),Gg(o,t)?tm({$method:t},e,n):function(){return tm({$method:t,$param:Yg(Array.from(arguments))},e,n)}}})}function nm({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function om(e,t={}){return Kg(new e(t),{get:(e,t)=>Gg("db",t)?tm({$method:t},null,e):function(){return tm({$method:t,$param:Yg(Array.from(arguments))},null,e)}})}class rm extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=bd("_globalUniCloudDatabaseCallback")),t||(this.auth=Jg(this._authCallBacks)),this._isJQL=t,Object.assign(this,Jg(this._dbCallBacks)),this.env=Kg({},{get:(e,t)=>({$env:t})}),this.Geo=Kg({},{get:(e,t)=>nm({path:["Geo"],method:t})}),this.serverDate=nm({path:[],method:"serverDate"}),this.RegExp=nm({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:o}){function r(e,t){if(n&&o)for(let n=0;n<o.length;n++){const r=o[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}const i=this,s=this._isJQL?"databaseForJQL":"database";function a(e){return i._callback("error",[e]),Sd(kd(s,"fail"),e).then((()=>Sd(kd(s,"complete"),e))).then((()=>(r(null,e),Fd(Id,{type:Bd,content:e}),Promise.reject(e))))}const c=Sd(kd(s,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:"CLIENT_DB",data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:o,tokenExpired:c,systemInfo:l=[]}=e.result;if(l)for(let r=0;r<l.length;r++){const{level:e,message:t,detail:n}=l[r];let o="[System Info]"+t;n&&(o=`${o}\n详细信息：${n}`),(console[e]||console.log)(o)}if(t)return a(new zd({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,o&&c&&(Wd({token:o,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:o,tokenExpired:c}]),this._callback("refreshToken",[{token:o,tokenExpired:c}]),Fd(Td,{token:o,tokenExpired:c}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let r=0;r<u.length;r++){const{prop:t,tips:n}=u[r];if(t in e.result){const o=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),o)})}}return f=e,Sd(kd(s,"success"),f).then((()=>Sd(kd(s,"complete"),f))).then((()=>{r(f,null);const e=i._parseResult(f);return Fd(Id,{type:Bd,content:e}),Promise.resolve(e)}));var f}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),a(new zd({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const im="token无效，跳转登录页面",sm="token过期，跳转登录页面",am={TOKEN_INVALID_TOKEN_EXPIRED:sm,TOKEN_INVALID_INVALID_CLIENTID:im,TOKEN_INVALID:im,TOKEN_INVALID_WRONG_TOKEN:im,TOKEN_INVALID_ANONYMOUS_USER:im},cm={"uni-id-token-expired":sm,"uni-id-check-token-failed":im,"uni-id-token-not-exist":im,"uni-id-check-device-feature-failed":im};function lm(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function um(e=[],t=""){const n=[],o=[];return e.forEach((e=>{!0===e.needLogin?n.push(lm(t,e.path)):!1===e.needLogin&&o.push(lm(t,e.path))})),{needLoginPage:n,notNeedLoginPage:o}}function fm(e){return e.split("?")[0].replace(/^\//,"")}function pm(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function dm(){return fm(pm())}function hm(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,o=fm(e);return n.some((e=>e.pagePath===o))}const gm=!!Cf.uniIdRouter,{loginPage:mm,routerNeedLogin:ym,resToLogin:vm,needLoginPage:bm,notNeedLoginPage:wm,loginPageInTabBar:xm}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:o={}}=Cf){const{loginPage:r,needLogin:i=[],resToLogin:s=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=um(e),{needLoginPage:l,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:o,pages:r=[]}=e,{needLoginPage:i,notNeedLoginPage:s}=um(r,o);t.push(...i),n.push(...s)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:r,routerNeedLogin:i,resToLogin:s,needLoginPage:[...a,...l],notNeedLoginPage:[...c,...u],loginPageInTabBar:hm(r,o)}}();if(bm.indexOf(mm)>-1)throw new Error(`Login page [${mm}] should not be "needLogin", please check your pages.json`);function _m(e){const t=dm();if("/"===e.charAt(0))return e;const[n,o]=e.split("?"),r=n.replace(/^\//,"").split("/"),i=t.split("/");i.pop();for(let s=0;s<r.length;s++){const e=r[s];".."===e?i.pop():"."!==e&&i.push(e)}return""===i[0]&&i.shift(),"/"+i.join("/")+(o?"?"+o:"")}function Am({redirect:e}){const t=fm(e),n=fm(mm);return dm()!==n&&t!==n}function Sm({api:e,redirect:t}={}){if(!t||!Am({redirect:t}))return;const n=(r=t,"/"!==(o=mm).charAt(0)&&(o="/"+o),r?o.indexOf("?")>-1?o+`&uniIdRedirectUrl=${encodeURIComponent(r)}`:o+`?uniIdRedirectUrl=${encodeURIComponent(r)}`:o);var o,r;xm?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const i={navigateTo:ac.navigateTo,redirectTo:ac.redirectTo,switchTab:ac.switchTab,reLaunch:ac.reLaunch};setTimeout((()=>{i[e]({url:n})}),0)}function km({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=Vd();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:cm[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:cm[e]}}return n}();if(function(e){const t=fm(_m(e));return!(wm.indexOf(t)>-1)&&(bm.indexOf(t)>-1||ym.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,Nd(Cd).length>0)return setTimeout((()=>{Fd(Cd,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Em(){!function(){const e=pm(),{abortLoginPageJump:t,autoToLoginPage:n}=km({url:e});t||n&&Sm({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];ac.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:o}=km({url:e.url});return t?e:o?(Sm({api:n,redirect:_m(e.url)}),!1):e}})}}function Pm(){this.onResponse((e=>{const{type:t,content:n}=e;let o=!1;switch(t){case"cloudobject":o=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in cm}(n);break;case"clientdb":o=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in am}(n)}o&&function(e={}){const t=Nd(Cd);qd().then((()=>{const n=pm();if(n&&Am({redirect:n}))return t.length>0?Fd(Cd,Object.assign({uniIdRedirectUrl:n},e)):void(mm&&Sm({api:"navigateTo",redirect:n}))}))}(n)}))}function Im(e){var t;(t=e).onResponse=function(e){Rd(Id,e)},t.offResponse=function(e){Md(Id,e)},function(e){e.onNeedLogin=function(e){Rd(Cd,e)},e.offNeedLogin=function(e){Md(Cd,e)},gm&&(bd("_globalUniCloudStatus").needLoginInit||(bd("_globalUniCloudStatus").needLoginInit=!0,qd().then((()=>{Em.call(e)})),vm&&Pm.call(e)))}(e),function(e){e.onRefreshToken=function(e){Rd(Td,e)},e.offRefreshToken=function(e){Md(Td,e)}}(e)}let Cm;const Tm="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Bm=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Om(){const e=Vd().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(Cm(o).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}Cm="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Bm.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",i=0;i<e.length;)t=Tm.indexOf(e.charAt(i++))<<18|Tm.indexOf(e.charAt(i++))<<12|(n=Tm.indexOf(e.charAt(i++)))<<6|(o=Tm.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;var jm=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(Xp((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",o="chooseAndUploadFile:fail";function r(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function i(e,t,{onChooseFile:o,onUploadProgress:r}){return t.then((e=>{if(o){const t=o(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,o=5,r){(t=Object.assign({},t)).errMsg=n;const i=t.tempFiles,s=i.length;let a=0;return new Promise((n=>{for(;a<o;)c();function c(){const o=a++;if(o>=s)return void(!i.find((e=>!e.url&&!e.errMsg))&&n(t));const l=i[o];e.uploadFile({provider:l.provider,filePath:l.path,cloudPath:l.cloudPath,fileType:l.fileType,cloudPathAsRealPath:l.cloudPathAsRealPath,onUploadProgress(e){e.index=o,e.tempFile=l,e.tempFilePath=l.path,r&&r(e)}}).then((e=>{l.url=e.fileID,o<s&&c()})).catch((e=>{l.errMsg=e.errMsg||e.message,o<s&&c()}))}}))}(e,t,5,r)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?i(e,function(e){const{count:t,sizeType:n,sourceType:i=["album","camera"],extension:s}=e;return new Promise(((e,a)=>{ac.chooseImage({count:t,sizeType:n,sourceType:i,extension:s,success(t){e(r(t,"image"))},fail(e){a({errMsg:e.errMsg.replace("chooseImage:fail",o)})}})}))}(t),t):"video"===t.type?i(e,function(e){const{camera:t,compressed:n,maxDuration:i,sourceType:s=["album","camera"],extension:a}=e;return new Promise(((e,c)=>{ac.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:s,extension:a,success(t){const{tempFilePath:n,duration:o,size:i,height:s,width:a}=t;e(r({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:a,height:s,duration:o,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",o)})}})}))}(t),t):i(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,i)=>{let s=ac.chooseFile;if(void 0!==sc&&"function"==typeof sc.chooseMessageFile&&(s=sc.chooseMessageFile),"function"!=typeof s)return i({errMsg:o+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});s({type:"all",count:t,extension:n,success(t){e(r(t))},fail(e){i({errMsg:e.errMsg.replace("chooseFile:fail",o)})}})}))}(t),t)}}})));function Lm(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if("manual"===this.loadtime)return;let n=!1;const o=[];for(let r=2;r<e.length;r++)e[r]!==t[r]&&(o.push(e[r]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,o)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:o,count:r}=n.result;this.getcount&&(this.mixinDatacomPage.count=r),this.mixinDatacomHasMore=o.length<this.pageSize;const i=e?o.length?o[0]:void 0:o;this.mixinDatacomResData=i,t&&t(i)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const o=t.action||this.action;o&&(n=n.action(o));const r=t.collection||this.collection;n=Array.isArray(r)?n.collection(...r):n.collection(r);const i=t.where||this.where;i&&Object.keys(i).length&&(n=n.where(i));const s=t.field||this.field;s&&(n=n.field(s));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const l=t.groupField||this.groupField;l&&(n=n.groupField(l)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const f=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,p=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,d=void 0!==t.getcount?t.getcount:this.getcount,h=void 0!==t.gettree?t.gettree:this.gettree,g=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:d},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return h&&(m.getTree=y),g&&(m.getTreePath=y),n=n.skip(p*(f-1)).limit(p).get(m),n}}}}function Nm(e){return bd("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function Rm({openid:e,callLoginByWeixin:t=!1}={}){const n=Nm(this);if(e&&t)throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");if(e)return n.mpWeixinOpenid=e,{};const o=await new Promise(((e,t)=>{ac.login({success(t){e(t.code)},fail(e){t(new Error(e.errMsg))}})})),r=this.importObject("uni-id-co",{customUI:!0});return await r.secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:t}),n.mpWeixinCode=o,{code:o}}async function Mm(e){const t=Nm(this);return t.initPromise||(t.initPromise=Rm.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function Fm(e){Yd=e}function Um(e){const t={getSystemInfo:ac.getSystemInfo,getPushClientId:ac.getPushClientId};return function(n){return new Promise(((o,r)=>{t[e]({...n,success(e){o(e)},fail(e){r(e)}})}))}}class Dm extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const o=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(o,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let o=0;o<n.length;o++)n[o](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([Um("getSystemInfo")(),Um("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:o,message:r}=t;this._payloadQueue.push({action:n,messageId:o,message:r}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:o}=e;"end"===t?this._end({messageId:n,message:o}):"message"===t&&this._appendMessage({messageId:n,message:o})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){ac.onPushMessage(this._uniPushMessageCallback)}_destroy(){ac.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const qm={tcb:yg,tencent:yg,aliyun:rh,private:_g,dcloud:_g,alipay:Og};let Hm=new class{init(e){let t={};const n=qm[e.provider];if(!n)throw new Error("未提供正确的provider参数");var o;return t=n.init(e),function(e){const t={};e.__dev__=t,t.debugLog="mp-harmony"===hd;const n=gd;n&&!n.code&&(t.debugInfo=n);const o=new fd({createPromise:function(){return async function(e){const t=e.__dev__;if(!t.debugInfo)return;const{address:n,servePort:o}=t.debugInfo,{address:r}=await bg(n,o);if(r)return t.localAddress=r,void(t.localPort=o);const i=console.warn;let s="";if("remote"===t.debugInfo.initialLaunchType?(t.debugInfo.forceRemote=!0,s="当前客户端和HBuilderX不在同一局域网下（或其他网络原因无法连接HBuilderX），uniCloud本地调试服务不对当前客户端生效。\n- 如果不使用uniCloud本地调试服务，请直接忽略此信息。\n- 如需使用uniCloud本地调试服务，请将客户端与主机连接到同一局域网下并重新运行到客户端。"):s="无法连接uniCloud本地调试服务，请检查当前客户端是否与主机在同一局域网下。\n- 如需使用uniCloud本地调试服务，请将客户端与主机连接到同一局域网下并重新运行到客户端。",s+="\n- 如果在HBuilderX开启的状态下切换过网络环境，请重启HBuilderX后再试\n- 检查系统防火墙是否拦截了HBuilderX自带的nodejs\n- 检查是否错误的使用拦截器修改uni.request方法的参数",0===hd.indexOf("mp-")&&(s+="\n- 小程序中如何使用uniCloud，请参考：https://uniapp.dcloud.net.cn/uniCloud/publish.html#useinmp"),!t.debugInfo.forceRemote)throw new Error(s);i(s)}(e)}});t.initLocalNetwork=function(){return o.exec()}}(t),function(e){e._initPromiseHub||(e._initPromiseHub=new fd({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),Vg(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(o=t).database=function(e){if(e&&Object.keys(e).length>0)return o.init(e).database();if(this._database)return this._database;const t=om(rm,{uniClient:o});return this._database=t,t},o.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return o.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=om(rm,{uniClient:o,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=Om,e.chooseAndUploadFile=jm.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return Lm(e)}}),e.SSEChannel=Dm,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return Mm.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=Fm,e.importObject=function(t){return function(n,o={}){o=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},o);const{customUI:r,loadingOptions:i,errorOptions:s,parseSystemError:a}=o,c=!r;return new Proxy({},{get(r,l){switch(l){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...o){const r=n?n({params:o}):{};let i,s;try{return await Sd(kd(t,"invoke"),{...r}),i=await e(...o),await Sd(kd(t,"success"),{...r,result:i}),i}catch(a){throw s=a,await Sd(kd(t,"fail"),{...r,error:s}),s}finally{await Sd(kd(t,"complete"),s?{...r,error:s}:{...r,result:i})}}}({fn:async function r(...u){let f;c&&ac.showLoading({title:i.title,mask:i.mask});const p={name:n,type:"OBJECT",data:{method:l,params:u}};"object"==typeof o.secretMethods&&function(e,t){const n=t.data.method,o=e.secretMethods||{},r=o[n]||o["*"];r&&(t.secretType=r)}(o,p);let d=!1;try{f=await t.callFunction(p)}catch(e){d=!0,f={result:new zd(e)}}const{errSubject:h,errCode:g,errMsg:m,newToken:y}=f.result||{};if(c&&ac.hideLoading(),y&&y.token&&y.tokenExpired&&(Wd(y),Fd(Td,{...y})),g){let e=m;if(d&&a&&(e=(await a({objectName:n,methodName:l,params:u,errSubject:h,errCode:g,errMsg:m})).errMsg||m),c)if("toast"===s.type)ac.showToast({title:e,icon:"none"});else{if("modal"!==s.type)throw new Error(`Invalid errorOptions.type: ${s.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:o,confirmText:r}={}){return new Promise(((i,s)=>{ac.showModal({title:e,content:t,showCancel:n,cancelText:o,confirmText:r,success(e){i(e)},fail(){i({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:s.retry,cancelText:"取消",confirmText:s.retry?"重试":"确定"});if(s.retry&&t)return r(...u)}}const t=new zd({subject:h,code:g,message:m,requestId:f.requestId});throw t.detail=f.result,Fd(Id,{type:Ld,content:t}),t}return Fd(Id,{type:Ld,content:f.result}),f.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:l,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let o=!1;if("callFunction"===t){const e=n&&n.type||nd;o=e!==nd}const r="callFunction"===t&&!o,i=this._initPromiseHub.exec();n=n||{};const{success:s,fail:a,complete:c}=Hd(n),l=i.then((()=>o?Promise.resolve():Sd(kd(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>o?Promise.resolve(e):Sd(kd(t,"success"),e).then((()=>Sd(kd(t,"complete"),e))).then((()=>(r&&Fd(Id,{type:Od,content:e}),Promise.resolve(e))))),(e=>o?Promise.reject(e):Sd(kd(t,"fail"),e).then((()=>Sd(kd(t,"complete"),e))).then((()=>(Fd(Id,{type:Od,content:e}),Promise.reject(e))))));if(!(s||a||c))return l;l.then((e=>{s&&s(e),c&&c(e),r&&Fd(Id,{type:Od,content:e})}),(e=>{a&&a(e),c&&c(e),r&&Fd(Id,{type:Od,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=md;let t={};if(e&&1===e.length)t=e[0],Hm=Hm.init(t),Hm._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{Hm[e]=function(){return console.error(n),Promise.reject(new zd({code:"SYS_ERR",message:n}))}}))}Object.assign(Hm,{get mixinDatacom(){return Lm(Hm)}}),Im(Hm),Hm.addInterceptor=_d,Hm.removeInterceptor=Ad,Hm.interceptObject=Ed})();var zm=Hm;const Qm={props:{modelValue:{type:[String,Number],default:()=>Dp.input.value},type:{type:String,default:()=>Dp.input.type},fixed:{type:Boolean,default:()=>Dp.input.fixed},disabled:{type:Boolean,default:()=>Dp.input.disabled},disabledColor:{type:String,default:()=>Dp.input.disabledColor},clearable:{type:Boolean,default:()=>Dp.input.clearable},password:{type:Boolean,default:()=>Dp.input.password},maxlength:{type:[String,Number],default:()=>Dp.input.maxlength},placeholder:{type:String,default:()=>Dp.input.placeholder},placeholderClass:{type:String,default:()=>Dp.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>Dp.input.placeholderStyle},showWordLimit:{type:Boolean,default:()=>Dp.input.showWordLimit},confirmType:{type:String,default:()=>Dp.input.confirmType},confirmHold:{type:Boolean,default:()=>Dp.input.confirmHold},holdKeyboard:{type:Boolean,default:()=>Dp.input.holdKeyboard},focus:{type:Boolean,default:()=>Dp.input.focus},autoBlur:{type:Boolean,default:()=>Dp.input.autoBlur},disableDefaultPadding:{type:Boolean,default:()=>Dp.input.disableDefaultPadding},cursor:{type:[String,Number],default:()=>Dp.input.cursor},cursorSpacing:{type:[String,Number],default:()=>Dp.input.cursorSpacing},selectionStart:{type:[String,Number],default:()=>Dp.input.selectionStart},selectionEnd:{type:[String,Number],default:()=>Dp.input.selectionEnd},adjustPosition:{type:Boolean,default:()=>Dp.input.adjustPosition},inputAlign:{type:String,default:()=>Dp.input.inputAlign},fontSize:{type:[String,Number],default:()=>Dp.input.fontSize},color:{type:String,default:()=>Dp.input.color},prefixIcon:{type:String,default:()=>Dp.input.prefixIcon},prefixIconStyle:{type:[String,Object],default:()=>Dp.input.prefixIconStyle},suffixIcon:{type:String,default:()=>Dp.input.suffixIcon},suffixIconStyle:{type:[String,Object],default:()=>Dp.input.suffixIconStyle},border:{type:String,default:()=>Dp.input.border},readonly:{type:Boolean,default:()=>Dp.input.readonly},shape:{type:String,default:()=>Dp.input.shape},formatter:{type:[Function,null],default:()=>Dp.input.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}},$m={props:{name:{type:String,default:()=>Dp.icon.name},color:{type:String,default:()=>Dp.icon.color},size:{type:[String,Number],default:()=>Dp.icon.size},bold:{type:Boolean,default:()=>Dp.icon.bold},index:{type:[String,Number],default:()=>Dp.icon.index},hoverClass:{type:String,default:()=>Dp.icon.hoverClass},customPrefix:{type:String,default:()=>Dp.icon.customPrefix},label:{type:[String,Number],default:()=>Dp.icon.label},labelPos:{type:String,default:()=>Dp.icon.labelPos},labelSize:{type:[String,Number],default:()=>Dp.icon.labelSize},labelColor:{type:String,default:()=>Dp.icon.labelColor},space:{type:[String,Number],default:()=>Dp.icon.space},imgMode:{type:String,default:()=>Dp.icon.imgMode},width:{type:[String,Number],default:()=>Dp.icon.width},height:{type:[String,Number],default:()=>Dp.icon.height},top:{type:[String,Number],default:()=>Dp.icon.top},stop:{type:Boolean,default:()=>Dp.icon.stop}}},Vm={props:{label:{type:String,default:()=>Dp.formItem.label},prop:{type:String,default:()=>Dp.formItem.prop},rules:{type:Array,default:()=>Dp.formItem.rules},borderBottom:{type:[String,Boolean],default:()=>Dp.formItem.borderBottom},labelPosition:{type:String,default:()=>Dp.formItem.labelPosition},labelWidth:{type:[String,Number],default:()=>Dp.formItem.labelWidth},rightIcon:{type:String,default:()=>Dp.formItem.rightIcon},leftIcon:{type:String,default:()=>Dp.formItem.leftIcon},required:{type:Boolean,default:()=>Dp.formItem.required},leftIconStyle:{type:[String,Object],default:()=>Dp.formItem.leftIconStyle}}},Wm={props:{model:{type:Object,default:()=>Dp.form.model},rules:{type:[Object,Function,Array],default:()=>Dp.form.rules},errorType:{type:String,default:()=>Dp.form.errorType},borderBottom:{type:Boolean,default:()=>Dp.form.borderBottom},labelPosition:{type:String,default:()=>Dp.form.labelPosition},labelWidth:{type:[String,Number],default:()=>Dp.form.labelWidth},labelAlign:{type:String,default:()=>Dp.form.labelAlign},labelStyle:{type:Object,default:()=>Dp.form.labelStyle}}},Km=/%[sdj%]/g;let Jm=function(){};function Xm(e){if(!e||!e.length)return null;const t={};return e.forEach((e=>{const{field:n}=e;t[n]=t[n]||[],t[n].push(e)})),t}function Gm(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let o=1;const r=t[0],i=t.length;if("function"==typeof r)return r.apply(null,t.slice(1));if("string"==typeof r){let e=String(r).replace(Km,(e=>{if("%%"===e)return"%";if(o>=i)return e;switch(e){case"%s":return String(t[o++]);case"%d":return Number(t[o++]);case"%j":try{return JSON.stringify(t[o++])}catch(n){return"[Circular]"}break;default:return e}}));for(let n=t[o];o<i;n=t[++o])e+=` ${n}`;return e}return r}function Ym(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}(t)||"string"!=typeof e||e))}function Zm(e,t,n){let o=0;const r=e.length;!function i(s){if(s&&s.length)return void n(s);const a=o;o+=1,a<r?t(e[a],i):n([])}([])}function ey(e,t,n,o){if(t.first){const t=new Promise(((t,r)=>{const i=function(e){const t=[];return Object.keys(e).forEach((n=>{t.push.apply(t,e[n])})),t}(e);Zm(i,n,(function(e){return o(e),e.length?r({errors:e,fields:Xm(e)}):t()}))}));return t.catch((e=>e)),t}let r=t.firstFields||[];!0===r&&(r=Object.keys(e));const i=Object.keys(e),s=i.length;let a=0;const c=[],l=new Promise(((t,l)=>{const u=function(e){if(c.push.apply(c,e),a++,a===s)return o(c),c.length?l({errors:c,fields:Xm(c)}):t()};i.length||(o(c),t()),i.forEach((t=>{const o=e[t];-1!==r.indexOf(t)?Zm(o,n,u):function(e,t,n){const o=[];let r=0;const i=e.length;function s(e){o.push.apply(o,e),r++,r===i&&n(o)}e.forEach((e=>{t(e,s)}))}(o,n,u)}))}));return l.catch((e=>e)),l}function ty(e){return function(t){return t&&t.message?(t.field=t.field||e.fullField,t):{message:"function"==typeof t?t():t,field:t.field||e.fullField}}}function ny(e,t){if(t)for(const n in t)if(t.hasOwnProperty(n)){const o=t[n];"object"==typeof o&&"object"==typeof e[n]?e[n]={...e[n],...o}:e[n]=o}return e}function oy(e,t,n,o,r,i){!e.required||n.hasOwnProperty(e.field)&&!Ym(t,i||e.type)||o.push(Gm(r.messages.required,e.fullField))}"undefined"!=typeof process&&process.env&&"undefined"!=typeof window&&"undefined"!=typeof document&&(Jm=function(e,t){"undefined"!=typeof console&&console.warn&&t.every((e=>"string"==typeof e))&&ac.__f__("warn","at node_modules/uview-plus/libs/util/async-validator.js:28",e,t)});const ry={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i};var iy={integer:function(e){return/^(-)?\d+$/.test(e)},float:function(e){return/^(-)?\d+(\.\d+)?$/.test(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(ah){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear},number:function(e){return!isNaN(e)&&"number"==typeof+e},object:function(e){return"object"==typeof e&&!iy.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&!!e.match(ry.email)&&e.length<255},url:function(e){return"string"==typeof e&&!!e.match(ry.url)},hex:function(e){return"string"==typeof e&&!!e.match(ry.hex)}};const sy="enum";const ay={required:oy,whitespace:function(e,t,n,o,r){(/^\s+$/.test(t)||""===t)&&o.push(Gm(r.messages.whitespace,e.fullField))},type:function(e,t,n,o,r){if(e.required&&void 0===t)return void oy(e,t,n,o,r);const i=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(i)>-1?iy[i](t)||o.push(Gm(r.messages.types[i],e.fullField,e.type)):i&&typeof t!==e.type&&o.push(Gm(r.messages.types[i],e.fullField,e.type))},range:function(e,t,n,o,r){const i="number"==typeof e.len,s="number"==typeof e.min,a="number"==typeof e.max,c=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;let l=t,u=null;const f="number"==typeof t,p="string"==typeof t,d=Array.isArray(t);if(f?u="number":p?u="string":d&&(u="array"),!u)return!1;d&&(l=t.length),p&&(l=t.replace(c,"_").length),i?l!==e.len&&o.push(Gm(r.messages[u].len,e.fullField,e.len)):s&&!a&&l<e.min?o.push(Gm(r.messages[u].min,e.fullField,e.min)):a&&!s&&l>e.max?o.push(Gm(r.messages[u].max,e.fullField,e.max)):s&&a&&(l<e.min||l>e.max)&&o.push(Gm(r.messages[u].range,e.fullField,e.min,e.max))},enum:function(e,t,n,o,r){e[sy]=Array.isArray(e[sy])?e[sy]:[],-1===e[sy].indexOf(t)&&o.push(Gm(r.messages[sy],e.fullField,e[sy].join(", ")))},pattern:function(e,t,n,o,r){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||o.push(Gm(r.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||o.push(Gm(r.messages.pattern.mismatch,e.fullField,t,e.pattern))}}};function cy(e,t,n,o,r){const i=e.type,s=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t,i)&&!e.required)return n();ay.required(e,t,o,s,r,i),Ym(t,i)||ay.type(e,t,o,s,r)}n(s)}const ly={string:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t,"string")&&!e.required)return n();ay.required(e,t,o,i,r,"string"),Ym(t,"string")||(ay.type(e,t,o,i,r),ay.range(e,t,o,i,r),ay.pattern(e,t,o,i,r),!0===e.whitespace&&ay.whitespace(e,t,o,i,r))}n(i)},method:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&ay.type(e,t,o,i,r)}n(i)},number:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(""===t&&(t=void 0),Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&(ay.type(e,t,o,i,r),ay.range(e,t,o,i,r))}n(i)},boolean:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&ay.type(e,t,o,i,r)}n(i)},regexp:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),Ym(t)||ay.type(e,t,o,i,r)}n(i)},integer:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&(ay.type(e,t,o,i,r),ay.range(e,t,o,i,r))}n(i)},float:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&(ay.type(e,t,o,i,r),ay.range(e,t,o,i,r))}n(i)},array:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t,"array")&&!e.required)return n();ay.required(e,t,o,i,r,"array"),Ym(t,"array")||(ay.type(e,t,o,i,r),ay.range(e,t,o,i,r))}n(i)},object:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&ay.type(e,t,o,i,r)}n(i)},enum:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r),void 0!==t&&ay.enum(e,t,o,i,r)}n(i)},pattern:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t,"string")&&!e.required)return n();ay.required(e,t,o,i,r),Ym(t,"string")||ay.pattern(e,t,o,i,r)}n(i)},date:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();if(ay.required(e,t,o,i,r),!Ym(t)){let n;n="number"==typeof t?new Date(t):t,ay.type(e,n,o,i,r),n&&ay.range(e,n.getTime(),o,i,r)}}n(i)},url:cy,hex:cy,email:cy,required:function(e,t,n,o,r){const i=[],s=Array.isArray(t)?"array":typeof t;ay.required(e,t,o,i,r,s),n(i)},any:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Ym(t)&&!e.required)return n();ay.required(e,t,o,i,r)}n(i)}};function uy(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){const e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}const fy=uy();function py(e){this.rules=null,this._messages=fy,this.define(e)}py.prototype={messages:function(e){return e&&(this._messages=ny(uy(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");let t,n;for(t in this.rules={},e)e.hasOwnProperty(t)&&(n=e[t],this.rules[t]=Array.isArray(n)?n:[n])},validate:function(e,t,n){const o=this;void 0===t&&(t={}),void 0===n&&(n=function(){});let r,i,s=e,a=t,c=n;if("function"==typeof a&&(c=a,a={}),!this.rules||0===Object.keys(this.rules).length)return c&&c(),Promise.resolve();if(a.messages){let e=this.messages();e===fy&&(e=uy()),ny(e,a.messages),a.messages=e}else a.messages=this.messages();const l={};(a.keys||Object.keys(this.rules)).forEach((t=>{r=o.rules[t],i=s[t],r.forEach((n=>{let r=n;"function"==typeof r.transform&&(s===e&&(s={...s}),i=s[t]=r.transform(i)),r="function"==typeof r?{validator:r}:{...r},r.validator=o.getValidationMethod(r),r.field=t,r.fullField=r.fullField||t,r.type=o.getType(r),r.validator&&(l[t]=l[t]||[],l[t].push({rule:r,value:i,source:s,field:t}))}))}));const u={};return ey(l,a,((e,t)=>{const{rule:n}=e;let o,r=!("object"!==n.type&&"array"!==n.type||"object"!=typeof n.fields&&"object"!=typeof n.defaultField);function i(e,t){return{...t,fullField:`${n.fullField}.${e}`}}function s(o){void 0===o&&(o=[]);let s=o;if(Array.isArray(s)||(s=[s]),!a.suppressWarning&&s.length&&py.warning("async-validator:",s),s.length&&n.message&&(s=[].concat(n.message)),s=s.map(ty(n)),a.first&&s.length)return u[n.field]=1,t(s);if(r){if(n.required&&!e.value)return s=n.message?[].concat(n.message).map(ty(n)):a.error?[a.error(n,Gm(a.messages.required,n.field))]:[],t(s);let o={};if(n.defaultField)for(const t in e.value)e.value.hasOwnProperty(t)&&(o[t]=n.defaultField);o={...o,...e.rule.fields};for(const e in o)if(o.hasOwnProperty(e)){const t=Array.isArray(o[e])?o[e]:[o[e]];o[e]=t.map(i.bind(null,e))}const r=new py(o);r.messages(a.messages),e.rule.options&&(e.rule.options.messages=a.messages,e.rule.options.error=a.error),r.validate(e.value,e.rule.options||a,(e=>{const n=[];s&&s.length&&n.push.apply(n,s),e&&e.length&&n.push.apply(n,e),t(n.length?n:null)}))}else t(s)}r=r&&(n.required||!n.required&&e.value),n.field=e.field,n.asyncValidator?o=n.asyncValidator(n,e.value,s,e.source,a):n.validator&&(o=n.validator(n,e.value,s,e.source,a),!0===o?s():!1===o?s(n.message||`${n.field} fails`):o instanceof Array?s(o):o instanceof Error&&s(o.message)),o&&o.then&&o.then((()=>s()),(e=>s(e)))}),(e=>{!function(e){let t,n=[],o={};function r(e){if(Array.isArray(e)){let t;n=(t=n).concat.apply(t,e)}else n.push(e)}for(t=0;t<e.length;t++)r(e[t]);n.length?o=Xm(n):(n=null,o=null),c(n,o)}(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!ly.hasOwnProperty(e.type))throw new Error(Gm("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"==typeof e.validator)return e.validator;const t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?ly.required:ly[this.getType(e)]||!1}},py.register=function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");ly[e]=t},py.warning=Jm,py.messages=fy;const dy={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}},hy={props:{openType:String},methods:{onGetUserInfo(e){this.$emit("getuserinfo",e.detail)},onContact(e){this.$emit("contact",e.detail)},onGetPhoneNumber(e){this.$emit("getphonenumber",e.detail)},onError(e){this.$emit("error",e.detail)},onLaunchApp(e){this.$emit("launchapp",e.detail)},onOpenSetting(e){this.$emit("opensetting",e.detail)}}},gy={props:{hairline:{type:Boolean,default:()=>Dp.button.hairline},type:{type:String,default:()=>Dp.button.type},size:{type:String,default:()=>Dp.button.size},shape:{type:String,default:()=>Dp.button.shape},plain:{type:Boolean,default:()=>Dp.button.plain},disabled:{type:Boolean,default:()=>Dp.button.disabled},loading:{type:Boolean,default:()=>Dp.button.loading},loadingText:{type:[String,Number],default:()=>Dp.button.loadingText},loadingMode:{type:String,default:()=>Dp.button.loadingMode},loadingSize:{type:[String,Number],default:()=>Dp.button.loadingSize},openType:{type:String,default:()=>Dp.button.openType},formType:{type:String,default:()=>Dp.button.formType},appParameter:{type:String,default:()=>Dp.button.appParameter},hoverStopPropagation:{type:Boolean,default:()=>Dp.button.hoverStopPropagation},lang:{type:String,default:()=>Dp.button.lang},sessionFrom:{type:String,default:()=>Dp.button.sessionFrom},sendMessageTitle:{type:String,default:()=>Dp.button.sendMessageTitle},sendMessagePath:{type:String,default:()=>Dp.button.sendMessagePath},sendMessageImg:{type:String,default:()=>Dp.button.sendMessageImg},showMessageCard:{type:Boolean,default:()=>Dp.button.showMessageCard},dataName:{type:String,default:()=>Dp.button.dataName},throttleTime:{type:[String,Number],default:()=>Dp.button.throttleTime},hoverStartTime:{type:[String,Number],default:()=>Dp.button.hoverStartTime},hoverStayTime:{type:[String,Number],default:()=>Dp.button.hoverStayTime},text:{type:[String,Number],default:()=>Dp.button.text},icon:{type:String,default:()=>Dp.button.icon},iconColor:{type:String,default:()=>Dp.button.icon},color:{type:String,default:()=>Dp.button.color},stop:{type:Boolean,default:()=>Dp.button.stop}}},yy={props:{color:{type:String,default:()=>Dp.line.color},length:{type:[String,Number],default:()=>Dp.line.length},direction:{type:String,default:()=>Dp.line.direction},hairline:{type:Boolean,default:()=>Dp.line.hairline},margin:{type:[String,Number],default:()=>Dp.line.margin},dashed:{type:Boolean,default:()=>Dp.line.dashed}}},vy={props:{show:{type:Boolean,default:()=>Dp.loadingIcon.show},color:{type:String,default:()=>Dp.loadingIcon.color},textColor:{type:String,default:()=>Dp.loadingIcon.textColor},vertical:{type:Boolean,default:()=>Dp.loadingIcon.vertical},mode:{type:String,default:()=>Dp.loadingIcon.mode},size:{type:[String,Number],default:()=>Dp.loadingIcon.size},textSize:{type:[String,Number],default:()=>Dp.loadingIcon.textSize},text:{type:[String,Number],default:()=>Dp.loadingIcon.text},timingFunction:{type:String,default:()=>Dp.loadingIcon.timingFunction},duration:{type:[String,Number],default:()=>Dp.loadingIcon.duration},inactiveColor:{type:String,default:()=>Dp.loadingIcon.inactiveColor}}};exports.CryptoJS=If,exports.Schema=py,exports._export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},exports.addStyle=$f,exports.addUnit=Vf,exports.buttonMixin=dy,exports.color=vp,exports.colorGradient=lp,exports.computed=ti,exports.config=Hf,exports.createSSRApp=es,exports.createStore=function(e){return new Ll(e)},exports.deepClone=Wf,exports.deepMerge=Kf,exports.e=Cf,exports.e$1=(e,...t)=>a(e,...t),exports.error=Xf,exports.f=(e,t)=>function(e,t){let n;if(f(e)||g(e)){n=new Array(e.length);for(let o=0,r=e.length;o<r;o++)n[o]=t(e[o],o,o)}else if("number"==typeof e){if(!Number.isInteger(e))return oi(`The v-for range expect an integer value but got ${e}.`),[];n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(y(e))if(e[Symbol.iterator])n=Array.from(e,((e,n)=>t(e,n,n)));else{const o=Object.keys(e);n=new Array(o.length);for(let r=0,i=o.length;r<i;r++){const i=o[r];n[r]=t(e[i],i,r)}}else n=[];return n}(e,t),exports.formValidate=tp,exports.gei=Zi,exports.getCurrentInstance=Fr,exports.getProperty=np,exports.icons={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""},exports.index=ac,exports.initVueI18n=function(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e=void 0!==ac&&ac.getLocale?ac.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():J),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||J);const r=new ee({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{if("function"!=typeof getApp)i=function(e,t){return r.t(e,t)};else{let e=!1;i=function(t,n){const o=getApp().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}},exports.mixin=ap,exports.mpMixin=cp,exports.n=e=>q(e),exports.nextTick$1=Qn,exports.nr=zm,exports.o=(e,t)=>Ki(e,t),exports.onBeforeUnmount=Do,exports.onLoad=Kp,exports.onMounted=Mo,exports.onShow=Wp,exports.onUnload=Jp,exports.onUnmounted=qo,exports.openType=hy,exports.p=e=>zi(e),exports.props=Qm,exports.props$1=$m,exports.props$2=Vm,exports.props$3=Dp,exports.props$4=Wm,exports.props$5=gy,exports.props$6=yy,exports.props$7=vy,exports.r=(e,t,n)=>Xi(e,t,n),exports.reactive=en,exports.ref=yn,exports.resolveComponent=function(e,t){return function(e,t,n=!0,o=!1){const r=go||Mr;if(r){const i=r.type;if(e===yo){const e=Zr(i,!1);if(e&&(e===t||e===I(t)||e===B(I(t))))return i}const s=vo(r[e]||i[e],t)||vo(r.appContext[e],t);if(!s&&o)return i;if(n&&!s){const n=e===yo?"\nIf this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.":"";Pn(`Failed to resolve ${e.slice(0,-1)}: ${t}${n}`)}return s}Pn(`resolve${B(e.slice(0,-1))} can only be used in render() or setup().`)}(yo,e,!0,t)||e},exports.s=e=>Wi(e),exports.setProperty=op,exports.sleep=zf,exports.sr=(e,t,n)=>function(e,t,n={}){const{$templateRefs:o}=Fr();o.push({i:t,r:e,k:n.k,f:n.f})}(e,t,n),exports.t=e=>(e=>g(e)?e:null==e?"":f(e)||y(e)&&(e.toString===b||!h(e.toString))?JSON.stringify(e,H,2):String(e))(e),exports.test=Lf,exports.throttle=gp,exports.toRefs=function(e){cn(e)||Qe("toRefs() expects a reactive object but received a plain one.");const t=f(e)?new Array(e.length):{};for(const n in e)t[n]=An(e,n);return t},exports.toast=ep,exports.unref=bn,exports.useStore=function(e){return void 0===e&&(e=null),Co(null!==e?e:vl)},exports.uviewPlus=$p,exports.watch=wo;
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/vendor.js.map
