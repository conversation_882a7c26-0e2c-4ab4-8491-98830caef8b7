<template>
  <div class="step-operation-form">
    <!-- 统一的步骤表单 -->
    <UnifiedStepForm ref="stepFormRef" :progress-data="progressData" :step-name="step.name" :step-code="step.code"
      :readonly="readonly" @dataChange="handleDataChange" />

    <!-- 通用操作按钮 - 只读模式或流程已完成时隐藏 -->
    <div v-if="!readonly && !isProcessCompleted" class="operation-buttons">
      <!-- 完成步骤按钮 -->
      <el-button class="custom-btn" type="primary" @click="handleComplete" :disabled="isStepCompleted">
        完成步骤
      </el-button>

      <!-- 跳过按钮区域 - 使用template避免DOM结构问题 -->
      <template v-if="canCurrentStepSkip">
        <el-button class="custom-btn" type="warning" @click="handleSkip" :disabled="isStepCompleted">
          跳过步骤
        </el-button>
      </template>
      <template v-else-if="!canCurrentStepSkip && skipDisabledReason">
        <el-tooltip :content="skipDisabledReason" placement="top">
          <el-button class="custom-btn" type="warning" disabled>
            <span style="text-decoration: line-through;">跳过步骤</span>
          </el-button>
        </el-tooltip>
      </template>

      <!-- 关键步骤标识 -->
      <el-tag v-if="isCriticalStep" type="danger" size="small" style="margin-left: 10px;">
        关键步骤
      </el-tag>

      <el-button class="custom-btn" type="danger" @click="handleEndProcess">
        结束流程
      </el-button>
    </div>

    <!-- 步骤说明 -->
    <div v-if="stepDescription && !readonly" class="step-description">
      <el-alert :title="stepDescription" :type="isCriticalStep ? 'error' : 'info'" :closable="false" show-icon>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 导入统一的步骤表单组件
import UnifiedStepForm from './UnifiedStepForm.vue'

const props = defineProps({
  step: {
    type: Object,
    required: true
  },
  progressData: {
    type: Object,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'skip', 'endProcess', 'clearForm'])

const stepFormRef = ref(null)

// 存储当前步骤的表单数据
const currentStepData = ref({})

// 步骤跳过配置映射 - 新的申请跟踪流程
const STEP_SKIP_CONFIG = {
  // 第一阶段：申请准备阶段
  'WRITING_PROCESS': {
    canSkip: true,
    description: '可以跳过，如果已完成文书环节或不需要文书约谈'
  },
  'WRITING_FINALIZATION': {
    canSkip: false,
    reason: '文书定稿步骤不能跳过，必须确认文书完成状态',
    criticalStep: true
  },
  'APPLICATION_FILLING': {
    canSkip: false,
    reason: '学校申请填写步骤不能跳过，必须完成申请填写',
    criticalStep: true
  },
  'APPLICATION_SUBMISSION': {
    canSkip: false,
    reason: '申请递交确认步骤不能跳过，必须确认递交状态',
    criticalStep: true
  },

  // 第二阶段：申请后环节
  'INTERVIEW_INVITATION': {
    canSkip: true,
    description: '可以跳过，如果学校不安排面试'
  },
  'INTERVIEW_TRAINING': {
    canSkip: true,
    description: '可以跳过，如果不需要面试培训或学校不安排面试'
  },
  'ATTEND_INTERVIEW': {
    canSkip: true,
    description: '可以跳过，如果未安排面试'
  },
  'ADMISSION_NOTICE': {
    canSkip: false,
    reason: '录取通知步骤不能跳过，必须选择是否收到录取通知',
    requiredField: 'receiveAdmission',
    criticalStep: true,
    validation: {
      required: true,
      message: '请选择是否收到录取通知',
      onNoAdmission: 'endProcess' // 未收到录取时结束流程
    }
  },

  // 第三阶段：录取后流程
  'ACCEPT_OFFER_DEPOSIT': {
    canSkip: true,
    description: '可以跳过，如果不需要缴纳留位费'
  },
  'VISA_PREPARATION': {
    canSkip: true,
    description: '可以跳过，如果不需要签证申请'
  },
  'SCHOOL_VISA_UPLOAD': {
    canSkip: true,
    description: '可以跳过，如果学校不要求上传资料至签证系统'
  },

  // 第四阶段：最终确认阶段
  'CONDITIONAL_MATERIALS': {
    canSkip: true,
    description: '可以跳过，如果获得的是正式录取而非条件录取'
  },
  'VISA_FORMAL_OFFER': {
    canSkip: false,
    reason: '签证审批&正式录取步骤不能跳过，必须选择最终录取状态',
    criticalStep: true
  },
  'ACCOMMODATION_REGISTRATION': {
    canSkip: false,
    reason: '住宿申请&注册步骤不能跳过，注册为所有学生的必办流程',
    requiredField: 'confirmEnrollment',
    criticalStep: true,
    validation: {
      required: true,
      message: '请选择最终入学状态',
      finalStep: true // 最终步骤
    }
  }
}

// 判断步骤是否已完成 - 新的14步骤流程
const isStepCompleted = computed(() => {
  const fieldMap = {
    // 第一阶段：申请准备阶段
    'WRITING_PROCESS': 'stepWritingProcessStatus',
    'WRITING_FINALIZATION': 'stepWritingFinalizationStatus',
    'APPLICATION_FILLING': 'stepApplicationFillingStatus',
    'APPLICATION_SUBMISSION': 'stepApplicationSubmissionStatus',

    // 第二阶段：申请后环节
    'INTERVIEW_INVITATION': 'stepInterviewInvitationStatus',
    'INTERVIEW_TRAINING': 'stepInterviewTrainingStatus',
    'ATTEND_INTERVIEW': 'stepAttendInterviewStatus',
    'ADMISSION_NOTICE': 'stepAdmissionNoticeStatus',

    // 第三阶段：录取后流程
    'ACCEPT_OFFER_DEPOSIT': 'stepAcceptOfferDepositStatus',
    'VISA_PREPARATION': 'stepVisaPreparationStatus',
    'SCHOOL_VISA_UPLOAD': 'stepSchoolVisaUploadStatus',

    // 第四阶段：最终确认阶段
    'CONDITIONAL_MATERIALS': 'stepConditionalMaterialsStatus',
    'VISA_FORMAL_OFFER': 'stepVisaFormalOfferStatus',
    'ACCOMMODATION_REGISTRATION': 'stepAccommodationRegistrationStatus'
  }

  const fieldName = fieldMap[props.step.code]
  return props.progressData[fieldName] === 'COMPLETED'
})

// 判断整个流程是否已完成
const isProcessCompleted = computed(() => {
  return props.progressData.progressStatus === 'COMPLETED'
})

// 判断当前步骤是否可以跳过
const canCurrentStepSkip = computed(() => {
  const stepConfig = STEP_SKIP_CONFIG[props.step.code]
  return stepConfig ? stepConfig.canSkip : true // 默认可以跳过
})

// 获取当前步骤不能跳过的原因
const skipDisabledReason = computed(() => {
  const stepConfig = STEP_SKIP_CONFIG[props.step.code]
  return stepConfig && !stepConfig.canSkip ? stepConfig.reason : ''
})

// 获取当前步骤的描述信息
const stepDescription = computed(() => {
  const stepConfig = STEP_SKIP_CONFIG[props.step.code]
  return stepConfig ? stepConfig.description || stepConfig.reason : ''
})

// 判断是否为关键步骤
const isCriticalStep = computed(() => {
  const stepConfig = STEP_SKIP_CONFIG[props.step.code]
  return stepConfig ? stepConfig.criticalStep : false
})

// 处理表单数据变化
const handleDataChange = (data) => {
  currentStepData.value = data
}

// 处理完成步骤
const handleComplete = async () => {
  try {
    // 验证表单
    if (stepFormRef.value) {
      const isValid = await stepFormRef.value.validateForm()
      if (!isValid) {
        ElMessage.error('请完善表单信息后再提交')
        return
      }
    }

    // 确认弹窗
    await ElMessageBox.confirm(
      `确认完成步骤"${props.step.name}"吗？完成后将自动进入下一步骤。`,
      '确认完成步骤',
      {
        confirmButtonText: '确认完成',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    // 获取表单数据
    const formData = stepFormRef.value ? stepFormRef.value.getFormData() : {}

    // 录取通知步骤特殊验证：必须选择是否收到录取通知
    if (props.step.code === 'ADMISSION_NOTICE') {
      const stepConfig = STEP_SKIP_CONFIG[props.step.code]

      if (formData.receiveAdmission === null || formData.receiveAdmission === undefined) {
        ElMessage.error(stepConfig.validation.message || '请选择是否收到录取通知')
        return
      }

      // 如果选择未收到录取通知，给出详细警告并结束流程
      if (!formData.receiveAdmission) {
        await ElMessageBox.confirm(
          '您选择了"未收到录取通知"，系统将自动结束整个申请流程。\n\n' +
          '确认信息：\n' +
          '• 申请学校：' + (props.progressData.applySchool || '未知') + '\n' +
          '• 申请专业：' + (props.progressData.applyMajorZh || '未知') + '\n' +
          '• 当前状态：未收到录取通知\n\n' +
          '注意事项：\n' +
          '• 流程将标记为"已完成"\n' +
          '• 后续步骤将无法继续\n' +
          '• 此操作不可撤销\n' +
          '• 如需重新申请，请联系相关老师',
          '确认结束申请流程',
          {
            confirmButtonText: '确认结束流程',
            cancelButtonText: '重新选择',
            type: 'error',
            dangerouslyUseHTMLString: false,
            distinguishCancelAndClose: true
          }
        )

        // 选择未收到录取通知时，直接结束流程，并更新数据库字段
        emit('endProcess', {
          updateType: 'progressStatus', // 设置更新类型
          stepRemark: formData.remark || '学生未收到录取通知，申请流程结束',
          stepSelectTime: formData.selectTime || '',
          isReceiveAdmission: 'N', // 使用枚举值
          receiveAdmissionTime: null,
          admissionType: null
        })
        return
      } else {
        // 收到录取通知时，需要更新相关字段
        const admissionData = {
          isReceiveAdmission: 'Y', // 使用枚举值
          receiveAdmissionTime: formData.selectTime || new Date().toISOString().slice(0, 19).replace('T', ' '),
          admissionType: formData.admissionType || 'CONDITIONAL' // 默认条件录取
        }

        // 将录取信息合并到表单数据中
        Object.assign(formData, admissionData)
      }
    }

    // 住宿申请&注册步骤特殊验证：必须选择最终入学状态
    if (props.step.code === 'ACCOMMODATION_REGISTRATION') {
      if (formData.confirmEnrollment === null || formData.confirmEnrollment === undefined) {
        ElMessage.error('请选择最终入学状态')
        return
      }

      // 如果选择未入学，给出警告
      if (!formData.confirmEnrollment) {
        await ElMessageBox.confirm(
          '您选择了"未入学"，确认要完成此步骤吗？\n注意：这将标志着学生最终未入学。',
          '确认最终入学状态',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      }
    }

    emit('submit', {
      updateType: 'stepCode', // 设置更新类型
      stepCode: props.step.code,
      stepStatus: 'COMPLETED',
      stepRemark: formData.remark || '',
      stepSelectTime: formData.selectTime || '',
      operationData: formData.operationData || '',

      // 申请信息字段
      applicationNumber: formData.applicationNumber,
      applicationEmail: formData.applicationEmail,
      applicationPassword: formData.applicationPassword,
      applicationSubmissionTime: formData.applicationSubmissionTime || '',

      // 面试相关字段
      interviewLink: formData.interviewLink,
      interviewDateTime: formData.interviewDateTime,

      // 录取通知相关字段
      receiveAdmission: formData.receiveAdmission,
      isReceiveAdmission: formData.isReceiveAdmission,
      receiveAdmissionTime: formData.receiveAdmissionTime,
      admissionType: formData.admissionType,
      admissionConditions: formData.admissionConditions,

      // 留位费相关字段
      depositAmount: formData.depositAmount,
      depositDeadline: formData.depositDeadline,
      isPayDeposit: formData.isPayDeposit,
      payDepositTime: formData.payDepositTime,

      // 签证相关字段
      visaAdminFee: formData.visaAdminFee,
      isPayVisaFee: formData.isPayVisaFee,
      payVisaFeeTime: formData.payVisaFeeTime,
      visaApprovalStatus: formData.visaApprovalStatus,
      visaApprovalTime: formData.visaApprovalTime,

      // 正式录取相关字段
      isReceiveFormalOffer: formData.isReceiveFormalOffer,
      receiveFormalOfferTime: formData.receiveFormalOfferTime,

      // 住宿与注册相关字段
      accommodationApplication: formData.accommodationApplication,
      accommodationApplicationTime: formData.accommodationApplicationTime,
      confirmEnrollment: formData.confirmEnrollment,
      isConfirmEnrollment: formData.isConfirmEnrollment,
      confirmEnrollmentTime: formData.confirmEnrollmentTime
    })
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    ElMessage.error('请完善表单信息')
  }
}

// 处理跳过步骤
const handleSkip = async () => {
  try {
    // 验证备注必填
    if (stepFormRef.value) {
      const formData = stepFormRef.value.getFormData()
      if (!formData.remark || !formData.remark.trim()) {
        ElMessage.error('跳过步骤必须填写备注说明')
        return
      }

      // 确认弹窗
      await ElMessageBox.confirm(
        `确认跳过步骤"${props.step.name}"吗？跳过后将自动进入下一步骤。`,
        '确认跳过步骤',
        {
          confirmButtonText: '确认跳过',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 跳过步骤：统一处理数据提交
      emit('submit', {
        updateType: 'stepCode', // 设置更新类型
        stepCode: props.step.code,
        stepStatus: 'SKIPPED',
        stepRemark: formData.remark,
        stepSelectTime: formData.selectTime || '',
        operationData: formData.operationData || '',

        // 面试相关字段
        interviewLink: formData.interviewLink,
        interviewDateTime: formData.interviewDateTime,

        // 录取通知相关字段
        receiveAdmission: formData.receiveAdmission,
        isReceiveAdmission: formData.isReceiveAdmission
      })
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    ElMessage.error('请完善表单信息')
  }
}

// 处理结束整个流程
const handleEndProcess = async () => {
  try {
    // 验证备注必填
    if (stepFormRef.value) {
      const formData = stepFormRef.value.getFormData()
      if (!formData.remark || !formData.remark.trim()) {
        ElMessage.error('结束流程必须填写备注说明')
        return
      }

      // 增强确认弹窗，提供更详细的警告信息
      await ElMessageBox.confirm(
        '确认结束整个申请流程吗？\n\n注意：\n• 流程状态将变更为"已完成"\n• 当前步骤位置保持不变\n• 后续将无法继续操作\n• 此操作不可撤销',
        '确认结束流程',
        {
          confirmButtonText: '确认结束',
          cancelButtonText: '取消',
          type: 'error',
          dangerouslyUseHTMLString: false,
          distinguishCancelAndClose: true
        }
      )

      emit('endProcess', {
        updateType: 'progressStatus', // 设置更新类型
        stepRemark: formData.remark,
        stepSelectTime: formData.selectTime || ''
      })
    }
  } catch (error) {
    if (error === 'cancel' || error === 'close') {
      // 用户取消操作
      return
    }
    ElMessage.error('请完善表单信息')
  }
}

// 清空表单数据
const clearForm = () => {
  if (stepFormRef.value) {
    stepFormRef.value.resetForm()
  }
  currentStepData.value = {}
  emit('clearForm')
}

// 组件销毁前的清理
onBeforeUnmount(() => {
  // 清理表单引用，防止内存泄漏
  if (stepFormRef.value) {
    stepFormRef.value = null
  }
  // 清理数据
  currentStepData.value = {}
})
</script>

<style scoped lang="scss">
.step-operation-form {
  .operation-buttons {
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;

    .el-button {
      margin: 0 5px;
    }

    .el-tag {
      margin-left: 10px;
      font-weight: 600;
      animation: pulse 2s infinite;
    }
  }

  .step-description {
    margin-top: 15px;

    :deep(.el-alert) {
      border-radius: 8px;

      &.el-alert--error {
        background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
        border: 1px solid #f56565;

        .el-alert__icon {
          color: #e53e3e;
        }

        .el-alert__title {
          color: #c53030;
          font-weight: 600;
        }
      }

      &.el-alert--info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #0ea5e9;

        .el-alert__icon {
          color: #0284c7;
        }

        .el-alert__title {
          color: #0369a1;
          font-weight: 500;
        }
      }
    }
  }

  .readonly-tip {
    margin-top: 20px;
  }

  .process-completed-tip {
    margin-top: 20px;
  }
}

// 关键步骤标签动画
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }

  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
</style>
