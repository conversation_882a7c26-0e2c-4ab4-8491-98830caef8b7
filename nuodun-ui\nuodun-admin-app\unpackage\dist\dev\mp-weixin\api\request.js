"use strict";const e=require("../common/vendor.js"),t=require("../common/toast.js"),r=require("../common/common.js"),o=require("../config/environment.js"),n=require("../config/token.js"),s=require("../store/store.js");let a=!1;const i={401:"请先登录",403:"当前操作没有权限",404:"访问资源不存在",default:"系统未知错误，请反馈给管理员"},d=o.environment.aesKey,c=t=>{if(t&&!0===t.encrypted&&t.data)try{const o=(t=>{if(!t)return null;try{const r=e.CryptoJS.SHA256(d),o=e.CryptoJS.enc.Hex.parse(r.toString().substring(0,32));return e.CryptoJS.AES.decrypt(t,o,{mode:e.CryptoJS.mode.ECB,padding:e.CryptoJS.pad.Pkcs7}).toString(e.CryptoJS.enc.Utf8)}catch(r){return e.index.__f__("error","at api/request.js:83","解密失败:",r),null}})(t.data);if(!o)throw e.index.__f__("error","at api/request.js:108","数据解密失败"),new Error("数据解密失败");try{return JSON.parse(o)}catch(r){throw e.index.__f__("error","at api/request.js:104","数据解析失败:",r),new Error("数据解析失败")}}catch(o){throw e.index.__f__("error","at api/request.js:112","数据解密失败:",o),new Error("数据解密失败")}return t},p=()=>({"APP-Authorization":"Bearer "+n.getToken()}),u=n=>{const{url:u,method:l="GET",data:h={},params:m=null,header:y={},noToken:f=!1,isLoading:g=!1,loadingTitle:S="加载中...",baseUrl:C=o.environment.baseUrl,encrypt:_=o.environment.enableRequestEncrypt}=n,j={...y};f||Object.assign(j,p()),g&&e.index.showLoading(S);let x=u;if(m){x=u+"?";for(const e of Object.keys(m)){const t=m[e],r=encodeURIComponent(e)+"=";if(null!=t)if("object"==typeof t){for(const o of Object.keys(t))if(null!==t[o]&&void 0!==t[o]){const r=encodeURIComponent(e+"["+o+"]")+"=";x+=r+encodeURIComponent(t[o])+"&"}}else x+=r+encodeURIComponent(t)+"&"}x=x.slice(0,-1)}let q=h;if(_&&("POST"===l.toUpperCase()||"PUT"===l.toUpperCase())&&Object.keys(h).length>0){const t=(t=>{if(!t)return null;try{const r="object"==typeof t?JSON.stringify(t):t,o=e.CryptoJS.SHA256(d),n=e.CryptoJS.enc.Hex.parse(o.toString().substring(0,32));return e.CryptoJS.AES.encrypt(r,n,{mode:e.CryptoJS.mode.ECB,padding:e.CryptoJS.pad.Pkcs7}).toString()}catch(r){return e.index.__f__("error","at api/request.js:52","加密失败:",r),null}})(h);t&&(j["X-Encrypted"]="true",q={encryptedData:t},j["Content-Type"]="application/json;charset=UTF-8")}return new Promise(((o,n)=>{e.index.request({method:l.toUpperCase(),url:C+x,data:q,header:j,dataType:"json",success:d=>{e.index.stopPullDownRefresh(),g&&e.index.hideLoading();try{d.data=c(d.data)}catch(l){return t.toast.show(l.message||"数据解密失败"),void n(l.message||"数据解密失败")}const p=d.data.code||200,u=i[p]||d.data.msg||i.default;401!==p||a?500===p||200!==p?(t.toast.show(u),n(d.data)):o(d.data):(a=!0,r.showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then((t=>{t.confirm&&s.store.dispatch("LogOut").then((()=>{e.index.reLaunch({url:"/pages/login/login"})}))})).catch((()=>{a=!1})),n("无效的会话，或者会话已过期，请重新登录。"))},fail:r=>{e.index.stopPullDownRefresh(),g&&e.index.hideLoading();let o=r.errMsg||"网络请求失败";o.includes("timeout")?o="请求超时，请检查网络":o.includes("fail")&&(o="网络连接失败，请检查网络设置"),t.toast.show(o),n(r)}})}))};exports.get=(e,t={},r={})=>u({url:e,method:"GET",params:t,...r}),exports.getAuthHeader=p,exports.handleResponseDecrypt=c,exports.post=(e,t={},r={})=>u({url:e,method:"POST",data:t,...r}),exports.request=u;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/request.js.map
