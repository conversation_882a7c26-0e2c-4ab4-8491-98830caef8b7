<template>
  <div class="application-filling-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>学校申请填写详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>申请编号：</label>
            <span>{{ stepData.applicationNumber || '暂未填写' }}</span>
          </div>
          <div class="info-item">
            <label>申请邮箱：</label>
            <span>{{ stepData.applicationEmail || '暂未填写' }}</span>
          </div>
          <div class="info-item">
            <label>申请密码：</label>
            <span>{{ stepData.applicationPassword ? '已设置' : '暂未设置' }}</span>
          </div>
          <div v-if="stepData.submissionTime" class="info-item">
            <label>申请提交时间：</label>
            <span>{{ stepData.submissionTime }}</span>
          </div>
          <div v-if="stepData.remark" class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="申请编号" prop="applicationNumber">
          <el-input 
            v-model="stepData.applicationNumber" 
            placeholder="请输入学校申请系统生成的申请编号"
          />
        </el-form-item>

        <el-form-item label="申请邮箱" prop="applicationEmail">
          <el-input 
            v-model="stepData.applicationEmail" 
            type="email"
            placeholder="请输入申请时使用的邮箱地址"
          />
        </el-form-item>

        <el-form-item label="申请密码" prop="applicationPassword">
          <el-input 
            v-model="stepData.applicationPassword" 
            type="password"
            show-password
            placeholder="请输入申请系统的登录密码"
          />
        </el-form-item>

        <el-form-item label="申请提交时间" prop="submissionTime">
          <el-date-picker
            v-model="stepData.submissionTime"
            type="datetime"
            placeholder="请选择申请提交时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="申请过程中的其他重要信息"
          />
        </el-form-item>
      </el-form>

      <!-- 流程分叉选择（在完成步骤时显示） -->
      <div v-if="showJumpOptions" class="jump-options">
        <el-divider content-position="left">
          <span class="jump-title">后续流程选择</span>
        </el-divider>
        <el-alert
          title="请根据学校要求选择后续流程"
          description="不同学校的申请流程可能不同，请根据实际情况选择"
          type="info"
          :closable="false"
          show-icon
        />
        <el-radio-group v-model="selectedJumpOption" class="jump-radio-group">
          <el-radio label="normal" class="jump-radio">
            <div class="jump-option-content">
              <div class="jump-option-title">正常流程（包含面试）</div>
              <div class="jump-option-desc">学校会安排面试环节，需要进行面试准备</div>
            </div>
          </el-radio>
          <el-radio label="direct_admission" class="jump-radio">
            <div class="jump-option-content">
              <div class="jump-option-title">直接录取（跳过面试）</div>
              <div class="jump-option-desc">学校直接给出录取结果，无需面试环节</div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  },
  showJumpOptions: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'jumpOptionSelect'])

const formRef = ref(null)
const selectedJumpOption = ref('normal')

// 步骤数据 - 按照新的JSON字段配置
const stepData = reactive({
  applicationNumber: '',           // 申请编号
  applicationEmail: '',            // 申请邮箱
  applicationPassword: '',         // 申请密码
  submissionTime: '',              // 申请提交时间
  remark: ''                       // 备注说明
})

// 验证规则
const rules = {
  applicationNumber: [
    { required: true, message: '请输入申请编号', trigger: 'blur' }
  ],
  applicationEmail: [
    { required: true, message: '请输入申请邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  applicationPassword: [
    { required: true, message: '请输入申请密码', trigger: 'blur' }
  ],
  submissionTime: [
    { required: true, message: '请选择申请提交时间', trigger: 'change' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    applicationNumber: '',
    applicationEmail: '',
    applicationPassword: '',
    submissionTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听跳转选项变化
watch(selectedJumpOption, (newOption) => {
  const jumpOptions = {
    normal: { 
      key: 'normal', 
      label: '正常流程（包含面试）', 
      nextStep: 'INTERVIEW_INVITATION',
      description: '学校会安排面试环节'
    },
    direct_admission: { 
      key: 'direct_admission', 
      label: '直接录取（跳过面试）', 
      nextStep: 'ADMISSION_NOTICE',
      description: '学校直接给出录取结果'
    }
  }
  emit('jumpOptionSelect', jumpOptions[newOption])
})

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    applicationNumber: '',
    applicationEmail: '',
    applicationPassword: '',
    submissionTime: '',
    remark: ''
  })
  selectedJumpOption.value = 'normal'
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 获取当前跳转选项
const getCurrentJumpOption = () => {
  const jumpOptions = {
    normal: { 
      key: 'normal', 
      label: '正常流程（包含面试）', 
      nextStep: 'INTERVIEW_INVITATION' 
    },
    direct_admission: { 
      key: 'direct_admission', 
      label: '直接录取（跳过面试）', 
      nextStep: 'ADMISSION_NOTICE' 
    }
  }
  return jumpOptions[selectedJumpOption.value]
}

defineExpose({
  validate,
  resetForm,
  getCurrentJumpOption
})
</script>

<style scoped lang="scss">
.application-filling-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .jump-options {
      margin-top: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;

      .jump-title {
        font-weight: 600;
        color: #303133;
      }

      .el-alert {
        margin: 16px 0;
      }

      .jump-radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 16px;

        .jump-radio {
          margin: 0;
          padding: 12px;
          border: 1px solid #dcdfe6;
          border-radius: 6px;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
            background: #ecf5ff;
          }

          &.is-checked {
            border-color: #409eff;
            background: #ecf5ff;
          }

          .jump-option-content {
            margin-left: 8px;

            .jump-option-title {
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
            }

            .jump-option-desc {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
  }
}
</style> 