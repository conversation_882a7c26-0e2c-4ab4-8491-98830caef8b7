"use strict";const e=require("../../common/vendor.js");if(!Array){e.resolveComponent("custom-nav")()}Math||((()=>"../../components/custom-nav/custom-nav.js")+i+a)();const i=()=>"../../components/file-office/index.js",a=()=>"../../components/file-view/index.js",n={__name:"filePreview",setup(i){const{proxy:a}=e.getCurrentInstance(),n=a.toast,o=e.ref(""),t=e.ref(""),l=e.ref(!1),s=e.ref(!1),u=e.ref(""),d=()=>{e.index.showLoading({title:"准备预览...",mask:!0}),e.index.downloadFile({url:o.value,success:i=>{200===i.statusCode?e.index.openDocument({filePath:i.tempFilePath,showMenu:!0,success:()=>{e.index.hideLoading(),setTimeout((()=>{e.index.showToast({title:"可点击右上角保存到手机",icon:"none",duration:3e3})}),1e3)},fail:i=>{e.index.hideLoading(),e.index.__f__("error","at pages/transition/filePreview.vue:131","文档预览失败:",i),n.show("原生预览失败，使用组件预览"),r()}}):(e.index.hideLoading(),n.show("文件下载失败"),e.index.navigateBack())},fail:i=>{e.index.hideLoading(),e.index.__f__("error","at pages/transition/filePreview.vue:145","文件下载失败:",i),n.show("文件下载失败"),e.index.navigateBack()}})},r=()=>{var e;const i=(null==(e=o.value.split(".").pop())?void 0:e.toLowerCase())||"";l.value=!0,s.value=!1,"pdf"===i?u.value="PDF":["doc","docx","rtf"].includes(i)?u.value="WORD":["xls","xlsx","csv"].includes(i)?u.value="EXCEL":["jpg","jpeg","png","gif","bmp"].includes(i)&&(u.value="IMAGE")},v=()=>{},p=i=>{e.index.__f__("error","at pages/transition/filePreview.vue:173","文件预览失败:",i),n.show("文件预览失败")};return e.onLoad((i=>{i.filePath&&(o.value=decodeURIComponent(i.filePath),(()=>{var i;if(!o.value)return n.show("没有可预览的文件"),void setTimeout((()=>{e.index.navigateBack()}),1500);const a=(null==(i=o.value.split(".").pop())?void 0:i.toLowerCase())||"";["jpg","jpeg","png","gif","bmp","webp"].includes(a)?e.index.previewImage({urls:[o.value],current:o.value,fail:i=>{e.index.__f__("error","at pages/transition/filePreview.vue:75","图片预览失败:",i),n.show("图片预览失败"),l.value=!0,u.value="IMAGE"}}):["pdf","doc","docx","xls","xlsx","ppt","pptx"].includes(a)?d():(s.value=!0,l.value=!1)})()),i.fileName&&(t.value=decodeURIComponent(i.fileName))})),(i,a)=>e.e$1({a:e.p({title:"文件预览",showLeft:!0}),b:l.value},l.value?{c:e.o(v),d:e.o(p),e:e.p({"file-path":o.value,"file-type":u.value})}:s.value?{g:e.p({"file-path":o.value,"file-name":t.value})}:{},{f:s.value,h:e.gei(i,"")})}};wx.createPage(n);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/transition/filePreview.js.map
