<template>
  <div class="visa-formal-offer-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>签证审批&正式录取详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否收到正式录取：</label>
            <span>{{ stepData.isReceiveFormalOffer ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.isReceiveFormalOffer" class="info-item">
            <label>正式录取时间：</label>
            <span>{{ parseTime(stepData.receiveFormalOfferTime) || '暂未填写' }}</span>
          </div>
          <div v-if="stepData.isReceiveFormalOffer" class="info-item">
            <label>正式录取详情：</label>
            <span>{{ stepData.formalOfferDetails || '暂无详情' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否收到正式录取" prop="isReceiveFormalOffer">
          <el-radio-group v-model="stepData.isReceiveFormalOffer">
            <el-radio :label="true">已收到</el-radio>
            <el-radio :label="false">未收到</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="stepData.isReceiveFormalOffer">
          <el-form-item label="正式录取时间" prop="receiveFormalOfferTime">
            <el-date-picker
              v-model="stepData.receiveFormalOfferTime"
              type="datetime"
              placeholder="选择正式录取时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="正式录取详情" prop="formalOfferDetails">
            <el-input
              v-model="stepData.formalOfferDetails"
              type="textarea"
              :rows="3"
              placeholder="请详细描述正式录取的相关信息"
            />
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="签证审批通过且所有条件满足后，将获得正式录取或注册通知"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  isReceiveFormalOffer: false,
  receiveFormalOfferTime: '',
  formalOfferDetails: '',
  remark: ''
})

// 验证规则
const rules = {
  isReceiveFormalOffer: [
    { required: true, message: '请选择是否收到正式录取', trigger: 'change' }
  ],
  receiveFormalOfferTime: [
    { required: true, message: '请选择正式录取时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.isReceiveFormalOffer && !value) {
        callback(new Error('请选择正式录取时间'))
      } else {
        callback()
      }
    }}
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    isReceiveFormalOffer: false,
    receiveFormalOfferTime: '',
    formalOfferDetails: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    isReceiveFormalOffer: false,
    receiveFormalOfferTime: '',
    formalOfferDetails: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.visa-formal-offer-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 
 