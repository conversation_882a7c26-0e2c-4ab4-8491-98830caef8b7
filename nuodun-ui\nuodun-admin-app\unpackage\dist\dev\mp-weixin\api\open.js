"use strict";const e=require("./request.js");exports.bindWxLogin=function(n){return e.post("/quzr-open/wx/bindWxLogin",{},{params:n,isLoading:!0,loadingTitle:"加载中..."})},exports.bindWxMiniProgram=function(n){return e.post("/quzr-open/wx/bindWxMiniProgram",{},{params:n,isLoading:!0,loadingTitle:"绑定中..."})},exports.determineWxMaUser=function(n){return e.get("/quzr-open/wx/determineWxMaUser",n)},exports.sendVerCode=function(n){return e.get("/quzr-open/aly/getVerCode",n)};
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/open.js.map
