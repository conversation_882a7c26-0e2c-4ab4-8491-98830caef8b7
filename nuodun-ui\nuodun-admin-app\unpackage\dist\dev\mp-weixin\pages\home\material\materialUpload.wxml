<view class="{{['container', 'data-v-44845096', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}" id="{{n}}"><custom-nav wx:if="{{a}}" class="data-v-44845096" virtualHostClass="data-v-44845096" u-i="44845096-0" bind:__l="__l" u-p="{{a}}"></custom-nav><view wx:if="{{b}}" class="loading-wrapper data-v-44845096"><uni-load-more wx:if="{{c}}" class="data-v-44845096" virtualHostClass="data-v-44845096" u-i="44845096-1" bind:__l="__l" u-p="{{c}}"></uni-load-more></view><view wx:else class="material-upload data-v-44845096"><view wx:if="{{d}}" class="select-section data-v-44845096"><view class="section-title data-v-44845096">请选择学生在读类型</view><text class="select-tip data-v-44845096">请先选择该学生的在读类型，以便为您展示对应的材料配置：</text><view class="picker-wrapper data-v-44845096"><uni-data-picker wx:if="{{f}}" class="data-v-44845096" virtualHostClass="data-v-44845096" u-i="44845096-2" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"></uni-data-picker></view><view class="select-actions data-v-44845096"><button class="action-btn confirm-btn data-v-44845096" bindtap="{{g}}"> 确认 </button></view></view><view wx:elif="{{h}}" class="empty-section data-v-44845096"><view class="empty-icon data-v-44845096">📋</view><text class="empty-text data-v-44845096">暂无可上传的材料配置</text></view><view wx:else class="materials-container data-v-44845096"><view wx:for="{{i}}" wx:for-item="item" wx:key="p" class="{{['material-item', 'data-v-44845096', item.q && 'required']}}"><view class="material-header data-v-44845096"><view class="header-left data-v-44845096"><text class="material-title data-v-44845096">{{item.a}}</text><view class="material-tags data-v-44845096"><text class="{{['type-tag', 'data-v-44845096', item.c]}}">{{item.b}}</text><text wx:if="{{item.d}}" class="required-tag data-v-44845096">必填</text></view></view></view><text class="material-desc data-v-44845096">{{item.e}}</text><view class="material-content data-v-44845096"><view wx:if="{{item.f}}" class="file-container data-v-44845096"><view wx:if="{{item.g}}" class="file-list data-v-44845096"><document-preview wx:for="{{item.h}}" wx:for-item="file" wx:key="a" binddelete="{{file.b}}" class="material-file-item data-v-44845096" virtualHostClass="material-file-item data-v-44845096" u-i="{{file.c}}" bind:__l="__l" u-p="{{file.d}}"/></view><view class="upload-btn data-v-44845096" bindtap="{{item.k}}"><text class="upload-icon data-v-44845096">+</text><view class="upload-content data-v-44845096"><text class="upload-text data-v-44845096">{{item.i}}</text><text class="upload-tip data-v-44845096"> 支持文档、图片等格式（可选择手机相册或聊天记录） </text><text wx:if="{{item.j}}" class="debug-info data-v-44845096"> 微信浏览器环境 - 可能无法正确上传 </text></view></view></view><view wx:elif="{{item.l}}" class="text-container data-v-44845096"><view class="text-header data-v-44845096"><text class="text-title data-v-44845096">账号信息</text><text class="add-btn data-v-44845096" bindtap="{{item.m}}"> 添加 </text></view><view class="text-rows data-v-44845096"><view wx:for="{{item.n}}" wx:for-item="row" wx:key="f" class="text-row data-v-44845096"><input bindinput="{{row.a}}" placeholder="例：账号" class="text-input key-input data-v-44845096" value="{{row.b}}"/><input bindinput="{{row.c}}" placeholder="例：12345678" class="text-input value-input data-v-44845096" value="{{row.d}}"/><text bindtap="{{row.e}}" class="{{['delete-row-btn', 'data-v-44845096', item.o && 'disabled']}}"> 删除 </text></view></view></view></view></view></view><view class="bottom-spacing data-v-44845096"></view><view wx:if="{{j}}" class="action-section data-v-44845096"><button class="action-btn save-btn data-v-44845096" bindtap="{{l}}" disabled="{{m}}"><text wx:if="{{k}}" class="data-v-44845096">保存中...</text><text wx:else class="data-v-44845096">保存材料</text></button></view></view></view>