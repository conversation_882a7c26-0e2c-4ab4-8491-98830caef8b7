"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/open.js"),s=require("../../../api/user.js"),a=require("../../../common/validate.js");if(!Array){(e.resolveComponent("custom-nav")+e.resolveComponent("up-input")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../../components/custom-nav/custom-nav.js")+(()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const n={__name:"resetPwdByCode",setup(n){const{proxy:r}=e.getCurrentInstance(),t=r.toast,u=e.useStore(),l=e.ref(!0),p=e.ref(!0),d=e.ref(0);let c=null;const v=e.ref(""),i=e.reactive({verCode:"",newPassword:"",confirmPassword:"",verType:"app_resetps"}),m=e.computed((()=>v.value?11===v.value.length?v.value.slice(0,3)+"****"+v.value.slice(7):v.value:"加载中...")),h=()=>{d.value>0||(v.value?o.sendVerCode({phone:v.value,verType:"app_resetps"}).then((()=>{t.show("验证码已发送"),w()})).catch((o=>{e.index.__f__("error","at pages/mine/resetPwd/resetPwdByCode.vue:120",o),t.show("验证码发送失败")})):t.show("未获取到手机号，请返回重试"))},w=()=>{d.value=60,c=setInterval((()=>{d.value--,d.value<=0&&clearInterval(c)}),1e3)},f=()=>{v.value?i.verCode?a.checkPwd(i.newPassword)&&(i.confirmPassword==i.newPassword?s.resetUserPwdByCode(v.value,i.verCode,i.newPassword,i.verType).then((o=>{t.show("修改成功"),setTimeout((function(){e.index.redirectTo({url:"/pages/tabbar/tabbar"})}),300)})).catch((o=>{o&&o.msg&&o.msg.includes("手机号已修改")?(t.show("手机号已修改，请重新登录"),setTimeout((()=>{u.dispatch("LogOut").then((()=>{e.index.reLaunch({url:"/pages/login/login"})}))}),1e3)):t.show((null==o?void 0:o.msg)||"修改失败")})):t.show("两次输入的密码不一致")):t.show("请填写验证码"):t.show("未获取到手机号，请返回重试")};return e.onLoad((()=>{(()=>{const e=u.state.userInfo;e&&e.phonenumber?v.value=e.phonenumber:u.dispatch("GetInfo",{isJump:!1}).then((()=>{const e=u.state.userInfo;e&&e.phonenumber?v.value=e.phonenumber:t.show("获取手机号失败，请返回重试")})).catch((()=>{t.show("获取用户信息失败")}))})()})),e.onUnload((()=>{c&&clearInterval(c)})),(o,s)=>({a:e.p({title:"修改密码",showLeft:!0,path:"/pages/tabbar/tabbar?activeTab=mine"}),b:e.t(m.value),c:e.t(d.value>0?`${d.value}秒后重试`:"获取验证码"),d:e.o(h),e:d.value>0?1:"",f:e.o((e=>i.verCode=e)),g:e.p({type:"number",placeholder:"请输入验证码",maxlength:"6",border:"none",modelValue:i.verCode}),h:e.p({type:l.value?"eye-slash":"eye",size:"20",color:"#666"}),i:e.o((e=>l.value=!l.value)),j:e.o((e=>i.newPassword=e)),k:e.p({type:l.value?"password":"text",placeholder:"请输入新密码",maxlength:"20",border:"none",modelValue:i.newPassword}),l:e.p({type:p.value?"eye-slash":"eye",size:"20",color:"#666"}),m:e.o((e=>p.value=!p.value)),n:e.o((e=>i.confirmPassword=e)),o:e.p({type:p.value?"password":"text",placeholder:"请确认密码",maxlength:"20",border:"none",modelValue:i.confirmPassword}),p:e.o(f),q:e.gei(o,"")})}},r=e._export_sfc(n,[["__scopeId","data-v-9ffda5e0"]]);wx.createPage(r);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mine/resetPwd/resetPwdByCode.js.map
