<template>
  <div class="interview-invitation-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>面试邀请通知详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否收到面试邀请：</label>
            <span>{{ stepData.interviewInvited ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.interviewInvited" class="info-item">
            <label>面试时间：</label>
            <span>{{ parseTime(stepData.interviewTime) || '暂未安排' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否收到面试邀请" prop="interviewInvited">
          <el-radio-group v-model="stepData.interviewInvited">
            <el-radio :label="true">收到邀请</el-radio>
            <el-radio :label="false">未收到邀请</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="stepData.interviewInvited">
          <el-form-item label="面试时间" prop="interviewTime">
            <el-date-picker
              v-model="stepData.interviewTime"
              type="datetime"
              placeholder="选择面试时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="部分学校会安排面试，升学老师沟通并安排面培"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  interviewInvited: false,
  interviewTime: '',
  remark: ''
})

// 验证规则
const rules = {
  interviewInvited: [
    { required: true, message: '请选择是否收到面试邀请', trigger: 'change' }
  ],
  interviewTime: [
    { required: true, message: '请选择面试时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.interviewInvited && !value) {
        callback(new Error('请选择面试时间'))
      } else {
        callback()
      }
    }}
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    interviewInvited: false,
    interviewTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    interviewInvited: false,
    interviewTime: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.interview-invitation-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 