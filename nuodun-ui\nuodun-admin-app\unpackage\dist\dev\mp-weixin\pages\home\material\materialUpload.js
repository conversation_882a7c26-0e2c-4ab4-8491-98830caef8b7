"use strict";const e=require("../../../common/vendor.js"),a=require("../../../config/environment.js"),t=require("../../../api/home/<USER>"),i=require("../../../api/home/<USER>"),l=require("../../../api/home/<USER>"),s=require("../../../common/fileUtils.js"),n=require("../../../common/modal.js"),d=require("./materialValidation.js");if(!Array){(e.resolveComponent("uni-load-more")+e.resolveComponent("uni-data-picker"))()}Math||(o+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.js")+u)();const o=()=>"../../../components/custom-nav/custom-nav.js",u=()=>"../../../components/document-preview/index.js",r={__name:"materialUpload",setup(o){const u=a.environment.fileUrl||"",{proxy:r}=e.getCurrentInstance(),p=r.toast,{t_st_study_status:c}=r.useDict("t_st_study_status"),f=e.ref(!1),m=e.ref(!1),v={contentdown:"正在加载...",contentrefresh:"加载中...",contentnomore:"没有更多数据了"},h=e.ref({stId:null,stDecaType:"",studentStatus:"",materialPlanId:null}),y=e.ref(!1),L=e.ref({studentStatus:""}),_=e.ref([]),x=e.ref([]),g=e.ref({}),T=e.computed((()=>!1)),w=e=>{if(!e||!Array.isArray(e))return[];return e.filter((e=>e&&(e.sourceFileName||e.fileName||e.name)))},I=async()=>{if(L.value.studentStatus)try{e.index.showLoading("提交中...");const a={stId:h.value.stId,stDecaType:h.value.stDecaType,studentStatus:L.value.studentStatus,stFileCreStatus:"1"};await i.saveOrUpdateStMaterial(a),h.value.studentStatus=L.value.studentStatus,y.value=!1,await N(),e.index.hideLoading(),p.show("学生在读类型设置成功")}catch(a){e.index.__f__("error","at pages/home/<USER>/materialUpload.vue:278","保存学生在读类型失败:",a),e.index.hideLoading()}else p.show("请选择学生在读类型")},D=e=>{const a=x.value.find((a=>a.id===e));a&&(g.value[e]||(g.value[e]={id:a.planFileId||null,materialOptId:e,materialType:a.materialType,description:a.description,planType:a.dataType,fileList:[],textDataList:[{key:"",value:""}],isModified:!1}),g.value[e].textDataList=[...a.textDataList||[]],g.value[e].isModified=!0)},S=async a=>{try{T.value;const t=await new Promise((a=>{e.index.showActionSheet({itemList:["从手机相册选择（仅图片）","从微信聊天记录选择（所有文件）"],success:e=>{0===e.tapIndex?a("album"):1===e.tapIndex&&a("chat")},fail:()=>{a(null)}})}));if(!t)return;const i=await s.chooseAndUploadFile({count:10,maxSize:10,allowedTypes:["image","document"],acceptTypes:"application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,.csv,image/*",fileSourceType:t,uploadData:{fileBizType:"material_upload",materialOptId:a},onProgress:e=>{},onSuccess:e=>{}});if(i.uploaded&&i.uploadResults){const e=x.value.find((e=>e.id===a));if(e){e.fileList||(e.fileList=[]);const t=[],l=[];i.uploadResults.forEach((e=>{const a=e.sourceFileName||e.name,i=e.filePath;s.validateMaterialFileType(a,i)?t.push({...e,displayName:a||e.fileName||e.name||"未知文件"}):l.push(a||i||"未知文件")})),t.length>0&&(e.fileList.push(...t),P(a,"fileList",e.fileList)),l.length>0?p.show(`${l.length}个文件格式不支持`):p.show("文件上传成功")}}}catch(t){e.index.__f__("error","at pages/home/<USER>/materialUpload.vue:410","文件选择或上传失败:",t)}},F=(e,a)=>{const t=x.value.find((a=>a.id===e));t&&t.fileList&&(t.fileList.splice(a,1),P(e,"fileList",t.fileList),p.show("文件已删除"))},P=(e,a,t)=>{const i=x.value.find((a=>a.id===e));i&&(g.value[e]||(g.value[e]={id:i.planFileId||null,materialOptId:e,materialType:i.materialType,description:i.description,planType:i.dataType,fileList:[],textDataList:[{key:"",value:""}],isModified:!1}),"fileList"===a?g.value[e].fileList=[...t||[]]:"textDataList"===a&&(g.value[e].textDataList=[...t||[]]),g.value[e].isModified=!0)},k=async()=>{try{e.index.showLoading("保存中..."),m.value=!0;const a=d.buildSubmitData(g.value);if(0===a.length)return void p.show("没有检测到文件变更");const t={stId:h.value.stId,stDecaType:h.value.stDecaType,materialPlanId:h.value.materialPlanId,fileOptType:"1",materialPlanFileList:a};await l.uploadMaterialList(t),p.show("保存成功"),setTimeout((()=>{e.index.navigateBack()}),500)}catch(a){e.index.__f__("error","at pages/home/<USER>/materialUpload.vue:491","保存失败:",a)}finally{e.index.hideLoading(),m.value=!1}},M=async()=>{if(d.validateRequiredMaterials(x.value,g.value,p))try{(await n.createModal({title:"确认提交",content:"确定要提交该协议吗？提交后将不可修改",confirmText:"确定",cancelText:"取消"})).confirm&&await k()}catch(a){e.index.__f__("error","at pages/home/<USER>/materialUpload.vue:518","确认对话框错误:",a),await k()}},N=async()=>{if(h.value.stId){f.value=!0;try{const[a,i]=await Promise.all([t.getMaterialByTypeAndStatus({stDecaType:h.value.stDecaType,studentStatus:h.value.studentStatus}),l.selectStMaterialStAndType({stId:h.value.stId,fileOptType:"1",materialPlanId:h.value.materialPlanId})]),s=a.data||[],n=i.data||[],d={};n.forEach((a=>{d[a.materialOptId]||(d[a.materialOptId]=[]);let t=[];if(a.dataJson)try{t="string"==typeof a.dataJson?JSON.parse(a.dataJson):a.dataJson}catch(i){e.index.__f__("warn","at pages/home/<USER>/materialUpload.vue:566","解析文件JSON失败:",i),t=[]}Array.isArray(t)&&t.length>0&&d[a.materialOptId].push({planFileId:a.id,fileList:t})})),x.value=s.map((a=>{const t=d[a.id];let i=[],l=[{key:"",value:""}],s=null,o=a.dataType||"1";if(t&&t.length>0){const a=t[0];s=a.planFileId;const d=n.find((e=>e.id===a.planFileId));if(d)if("2"===o)try{if(d.dataJson){const e="string"==typeof d.dataJson?JSON.parse(d.dataJson):d.dataJson;l=Array.isArray(e)?e.length>0?e:[{key:"",value:""}]:e&&"object"==typeof e?[e]:[{key:"",value:""}]}}catch(u){e.index.__f__("warn","at pages/home/<USER>/materialUpload.vue:611","解析文本数据失败:",u),l=[{key:"",value:""}]}else t.forEach((e=>{i=i.concat(e.fileList||[])}))}return{...a,fileList:i,textDataList:l,dataType:o,planFileId:s}})),x.value.forEach((e=>{g.value[e.id]={id:e.planFileId||null,materialOptId:e.id,materialType:e.materialType,description:e.description,planType:e.dataType||"1",fileList:e.fileList||[],textDataList:e.textDataList||[{key:"",value:""}],isModified:!1}}))}catch(a){e.index.__f__("error","at pages/home/<USER>/materialUpload.vue:646","加载数据失败:",a),p.show("加载数据失败")}finally{f.value=!1}}},U=async(a={})=>{if(h.value={stId:parseInt(a.stId)||null},_.value=c.value.map((e=>({text:e.label,value:e.value}))),h.value.studentStatus)N();else try{const e=await i.getMaterialByStId(h.value.stId);e.data&&e.data.studentStatus?(h.value.studentStatus=e.data.studentStatus,h.value.stDecaType=e.data.stDecaType,N()):y.value=!0}catch(t){e.index.__f__("error","at pages/home/<USER>/materialUpload.vue:692","获取学生资料信息失败:",t),y.value=!0}};return e.onLoad((e=>{U(e)})),(a,t)=>e.e$1({a:e.p({title:"材料上传",showLeft:!0}),b:f.value},f.value?{c:e.p({status:"loading","content-text":v})}:e.e$1({d:y.value},y.value?{e:e.o((e=>L.value.studentStatus=e)),f:e.p({placeholder:"请选择学生在读类型","popup-title":"请选择学生在读类型",localdata:_.value,modelValue:L.value.studentStatus}),g:e.o(I)}:x.value.length?{i:e.f(x.value,((a,t,i)=>e.e$1({a:e.t(a.materialType),b:e.t("1"===a.dataType?"文件":"文本"),c:e.n("1"===a.dataType?"file-tag":"text-tag"),d:"Y"===a.isRequired},(a.isRequired,{}),{e:e.t(a.description),f:"1"===a.dataType},"1"===a.dataType?e.e$1({g:w(a.fileList).length>0},w(a.fileList).length>0?{h:e.f(w(a.fileList),((t,l,s)=>({a:l,b:e.o((t=>((a,t,i)=>{const l=x.value.find((e=>e.id===a));if(!l||!l.fileList)return;const s=w(l.fileList)[t],n=l.fileList.findIndex((e=>e===s||e.sourceFileName===s.sourceFileName&&e.fileFullPath===s.fileFullPath||e.fileName===s.fileName&&e.fileUrl===s.fileUrl||e.sourceFileName===i.fileName&&e.fileFullPath===i.filePath));-1!==n?F(a,n):e.index.__f__("warn","at pages/home/<USER>/materialUpload.vue:244","未找到要删除的文件")})(a.id,l,t)),l),c:"44845096-3-"+i+"-"+s,d:e.p({"file-path":e.unref(u)+t.filePath||t.fileFullPath||"","file-name":t.sourceFileName||t.fileName||t.name||"未知文件","allow-view":!!(e.unref(u)+t.filePath||t.fileFullPath),"allow-download":!!(e.unref(u)+t.filePath||t.fileFullPath),"allow-delete":!0})})))}:{},{i:e.t(a.fileList&&a.fileList.length>0?"继续添加":"上传文件"),j:T.value},(T.value,{}),{k:e.o((e=>S(a.id)),a.id)}):"2"===a.dataType?{m:e.o((e=>(e=>{const a=x.value.find((a=>a.id===e));a&&(a.textDataList||(a.textDataList=[]),a.textDataList.push({key:"",value:""}),D(e))})(a.id)),a.id),n:e.f(a.textDataList,((t,i,l)=>({a:e.o([e=>t.key=e.detail.value,i,e=>D(a.id),i],i),b:t.key,c:e.o([e=>t.value=e.detail.value,i,e=>D(a.id),i],i),d:t.value,e:e.o((e=>((e,a)=>{const t=x.value.find((a=>a.id===e));t&&t.textDataList&&(t.textDataList.length<=1||(t.textDataList.splice(a,1),D(e)))})(a.id,i)),i),f:i}))),o:a.textDataList.length<=1?1:""}:{},{l:"2"===a.dataType,p:a.id,q:"Y"===a.isRequired?1:""})))}:{},{h:!x.value.length,j:x.value.length>0},x.value.length>0?e.e$1({k:m.value},(m.value,{}),{l:e.o(M),m:m.value}):{}),{n:e.gei(a,"")})}},p=e._export_sfc(r,[["__scopeId","data-v-44845096"]]);wx.createPage(p);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/home/<USER>/materialUpload.js.map
