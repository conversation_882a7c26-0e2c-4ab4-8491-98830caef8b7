package com.nuodun.tran.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nuodun.common.enums.MaterialPlanProgressEnum;
import com.nuodun.common.utils.DateUtils;
import com.nuodun.common.utils.JposAssert;
import com.nuodun.tran.domain.TStMaterialPlanProgressLog;
import com.nuodun.tran.domain.dto.MaterialPlanProgressLogDto;
import com.nuodun.tran.domain.entity.TStMaterialPlanProgressLogEntity;
import com.nuodun.tran.mapper.TStMaterialPlanProgressLogMapper;
import com.nuodun.tran.service.TStMaterialPlanProgressLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 专业申请流程操作日志Service实现类
 * 提供操作日志的增删改查功能，使用MyBatis-Plus进行数据库操作
 *
 * <AUTHOR>
 * @description 针对表【t_st_material_plan_progress_log(专业申请流程操作日志表)】的数据库操作Service实现
 * @createDate 2025-07-21
 */
@Service
public class TStMaterialPlanProgressLogServiceImpl extends ServiceImpl<TStMaterialPlanProgressLogMapper, TStMaterialPlanProgressLog>
        implements TStMaterialPlanProgressLogService {

    @Autowired
    private TStMaterialPlanProgressLogMapper stMaterialPlanProgressLogMapper;

    @Override
    public List<TStMaterialPlanProgressLogEntity> getByProgressId(Long progressId) {
        return stMaterialPlanProgressLogMapper.getByProgressId(progressId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TStMaterialPlanProgressLog addLog(MaterialPlanProgressLogDto logDto) {
        // 参数校验
        JposAssert.notNull(logDto.getProgressId(), "进度ID不能为空");
        JposAssert.notNull(logDto.getStepCode(), "步骤编码不能为空");
        JposAssert.notNull(logDto.getOperationType(), "操作类型不能为空");
        JposAssert.hasText(logDto.getOperationDesc(), "操作描述不能为空");

        // 创建日志记录
        TStMaterialPlanProgressLog log = new TStMaterialPlanProgressLog();
        log.setPlanId(logDto.getPlanId());
        log.setProgressId(logDto.getProgressId());
        log.setStId(logDto.getStId());
        log.setContractId(logDto.getContractId());
        log.setOrderId(logDto.getOrderId());
        log.setApplySchool(logDto.getApplySchool());
        log.setApplyMajorZh(logDto.getApplyMajorZh());

        // 设置步骤相关信息
        log.setStepCode(logDto.getStepCode().getCode());
        log.setStepName(logDto.getStepCode().getName());

        // 设置操作相关信息
        log.setOperationType(logDto.getOperationType().getCode());
        log.setOperationDesc(logDto.getOperationDesc());

        // 设置步骤变更信息
        log.setBeforeCurrentStep(logDto.getBeforeCurrentStep() != null ? logDto.getBeforeCurrentStep().getCode() : null);
        log.setAfterCurrentStep(logDto.getAfterCurrentStep() != null ? logDto.getAfterCurrentStep().getCode() : null);

        // 设置其他信息
        log.setOperationData(logDto.getOperationData());
        log.setAttachments(logDto.getAttachments());
        log.setStepRemark(logDto.getStepRemark());
        log.setOperatorId(logDto.getOperatorId());
        log.setOperatorType(logDto.getOperatorType() != null ? logDto.getOperatorType().getCode() : MaterialPlanProgressEnum.OperatorType.MANUAL.getCode());
        log.setOperationTime(DateUtils.getNowDate());
        log.setBusinessData(logDto.getBusinessData());
        log.setRemark(logDto.getRemark());

        // 保存日志记录
        save(log);
        return log;
    }
}




