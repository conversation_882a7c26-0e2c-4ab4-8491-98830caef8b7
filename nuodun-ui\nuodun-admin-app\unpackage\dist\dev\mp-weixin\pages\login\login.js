"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),n=require("../../config/environment.js"),r=require("../../common/auth.js"),a=require("../../api/open.js"),i=require("../../common/validate.js");if(!Array){(e.resolveComponent("up-input")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const t={__name:"login",setup(t){const{proxy:s}=e.getCurrentInstance(),l=s.toast,p=e.useStore(),u={service:"服务协议",privacy:"隐私政策"},d=e.ref(!0),m=e.ref(!1);e.ref("获取验证码");const c=e.ref(0);let v=null;const g=e.reactive({username:"",password:"",phoneNumber:"",verificationCode:"",verType:"app_login",rememberMe:!1,agreedToTerms:!1,code:"",uuid:"",loginType:"PASSWORD",platType:"USER",appId:n.environment.currentAppId}),y=e.computed((()=>!0));e.onMounted((()=>{_(),T()})),e.onUnmounted((()=>{v&&clearInterval(v)}));const _=()=>{g.loginType="WX_OPEN"},T=()=>{const e=r.getAndDecrypt("savedCredentials");if(e){const{username:o,password:n,rememberMe:r}=e;g.username=o,g.password=n,g.rememberMe=r}else g.rememberMe=!1;d.value=!0},f=e=>{l.show(e)},h=()=>!!g.agreedToTerms||(f("请阅读并同意服务协议和隐私政策"),!1),b=async()=>{try{await p.dispatch("Login",{userInfo:g}),await p.dispatch("GetInfo",{isJump:!0})}catch(o){throw e.index.__f__("error","at pages/login/login.vue:277","登录失败:",o),o}},w=()=>{g.rememberMe=!g.rememberMe},O=()=>{d.value=!d.value},P=()=>{const e=document.querySelector('input[type="text"][password]');e&&e.focus()},S=()=>{g.agreedToTerms=!g.agreedToTerms},x=e=>{g.loginType=e},N=()=>{c.value>0||(g.phoneNumber?i.checkPhone(g.phoneNumber)?a.sendVerCode({phone:g.phoneNumber,verType:g.verType}).then((()=>{f("验证码已发送"),C()})).catch((o=>{e.index.__f__("error","at pages/login/login.vue:326",o),f("验证码发送失败")})):f("请输入正确的手机号"):f("请输入手机号"))},C=()=>{c.value=60,v=setInterval((()=>{c.value--,c.value<=0&&clearInterval(v)}),1e3)},E=async()=>{if(!m.value)if(g.phoneNumber)if(/^1[3-9]\d{9}$/.test(g.phoneNumber))if(g.verificationCode){if(h()){m.value=!0;try{await b(),m.value=!1}catch(e){m.value=!1}}}else f("请输入验证码");else f("请输入正确的手机号");else f("请输入手机号")},D=async()=>{if(!m.value)if(g.code="",g.username)if(g.password){if(h()){m.value=!0;try{await b(),g.rememberMe?r.encryptAndStore("savedCredentials",{username:g.username,password:g.password,rememberMe:!0}):e.index.removeStorageSync("savedCredentials"),m.value=!1}catch(o){m.value=!1}}}else f("请填写密码");else f("请填写账号")},W=()=>!m.value&&h(),I=async o=>{if(h()&&!m.value)if(o.detail.code){m.value=!0;try{const r=await new Promise(((o,n)=>{e.index.login({provider:"weixin",success:o,fail:n})}));if(!r.code)return f("获取登录信息失败"),void(m.value=!1);const i=await(async e=>await a.determineWxMaUser({code:e,appId:n.environment.currentAppId}))(r.code);if(g.code=i.openId,"Y"===i.bindStatus)return f("登录中..."),await b(),void(m.value=!1);f("绑定中...");const t=await a.bindWxMiniProgram({phoneCode:o.detail.code,loginCode:g.code,appId:n.environment.wxAppId});"Y"===t.data.bindStatus?(f("绑定成功，登录中..."),await b(),e.index.reLaunch({url:"/"})):f(t.message||"绑定失败，请联系管理员"),m.value=!1}catch(r){e.index.__f__("error","at pages/login/login.vue:540","一键登录失败:",r),f("登录失败，请重试"),m.value=!1}}else f("获取手机号失败，请重试")},A=o=>{const r=n.environment.agreementUrls;e.index.navigateTo({url:`/pages/transition/webView?url=${encodeURIComponent(r[o])}&title=${u[o]}`})};return(n,r)=>e.e$1({a:o._imports_0,b:"PASSWORD"===g.loginType},"PASSWORD"===g.loginType?{c:e.o(P),d:e.o((e=>g.username=e)),e:e.p({placeholder:"请输入账号",type:"text",maxlength:"20","confirm-type":"next",border:"none",modelValue:g.username}),f:e.p({type:d.value?"eye-slash":"eye",size:"20",color:"#666"}),g:e.o(O),h:e.o(D),i:e.o((e=>g.password=e)),j:e.p({type:d.value?"password":"text",placeholder:"请输入密码",maxlength:"20","confirm-type":"done",border:"none",modelValue:g.password}),k:g.rememberMe,l:e.o(w)}:"PHONE_CODE"===g.loginType?{n:e.o((e=>g.phoneNumber=e)),o:e.p({placeholder:"请输入手机号",type:"number",maxlength:"11","confirm-type":"next",border:"none",modelValue:g.phoneNumber}),p:e.t(c.value>0?`${c.value}秒后重试`:"获取验证码"),q:e.o(N),r:c.value>0?1:"",s:e.o((e=>g.verificationCode=e)),t:e.p({placeholder:"请输入验证码",type:"number",maxlength:"6","confirm-type":"done",border:"none",modelValue:g.verificationCode})}:{v:o._imports_1},{m:"PHONE_CODE"===g.loginType,w:"PASSWORD"===g.loginType},"PASSWORD"===g.loginType?{x:e.t(m.value?"登录中...":"登录"),y:m.value?1:"",z:e.o(D)}:{},{A:"PHONE_CODE"===g.loginType},"PHONE_CODE"===g.loginType?{B:e.t(m.value?"登录中...":"验证并登录"),C:m.value?1:"",D:e.o(E)}:{},{E:"WX_OPEN"===g.loginType},"WX_OPEN"===g.loginType?{F:e.t(m.value?"登录中...":"一键登录"),G:!g.agreedToTerms||m.value?1:"",H:g.agreedToTerms&&!m.value?"getPhoneNumber":"",I:e.o(I),J:e.o(W)}:{},{K:g.agreedToTerms,L:e.o(S),M:e.o((e=>A("service"))),N:e.o((e=>A("privacy"))),O:"PASSWORD"!==g.loginType},"PASSWORD"!==g.loginType?{P:o._imports_2,Q:e.o((e=>x("PASSWORD")))}:{},{R:"PHONE_CODE"!==g.loginType},"PHONE_CODE"!==g.loginType?{S:o._imports_3,T:e.o((e=>x("PHONE_CODE")))}:{},{U:"WX_OPEN"!==g.loginType},"WX_OPEN"!==g.loginType?{V:o._imports_1,W:e.n(y.value?"":"disabled"),X:e.o((e=>y.value?x("WX_OPEN"):null))}:{},{Y:e.gei(n,"")})}},s=e._export_sfc(t,[["__scopeId","data-v-e4e4508d"]]);wx.createPage(s);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
