<template>
  <div class="interview-training-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>面试培训安排详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否安排面试培训：</label>
            <span>{{ stepData.trainingArranged ? '是' : '否' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否面试培训" prop="trainingArranged">
          <el-radio-group v-model="stepData.trainingArranged">
            <el-radio :label="true">安排培训</el-radio>
            <el-radio :label="false">不需要培训</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="升学老师沟通并安排面培，模拟面试练习"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  trainingArranged: false,
  remark: ''
})

// 验证规则
const rules = {
  trainingArranged: [
    { required: true, message: '请选择是否安排面试培训', trigger: 'change' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    trainingArranged: false,
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    trainingArranged: false,
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.interview-training-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 