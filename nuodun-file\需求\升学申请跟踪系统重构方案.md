# 诺盾教育申请跟踪系统重构方案 v3.0

## 概述

基于《升学2025-07-30步骤.md》规范，对现有申请跟踪系统进行全面重构，实现12个核心步骤的完整流程管理。

## 重构目标

1. **统一步骤管理**：从14步骤简化为12步骤，优化流程逻辑
2. **数据库驱动**：前端步骤配置完全从数据库JSON字段读取
3. **灵活跳转机制**：支持根据学校要求自动跳过非必须步骤
4. **关键步骤控制**：确保关键步骤不可跳过，保证流程完整性
5. **JSON数据统一**：所有步骤数据使用JSON格式存储在step_data字段

## 技术架构

### 前端重构方案

#### 1. 步骤表单组件重构 (stepForms/)

根据12步骤流程，重构以下组件：

1. **WritingProcessStep.vue** - 文书环节
2. **WritingFinalizationStep.vue** - 文书定稿
3. **ApplicationFillingStep.vue** - 学校申请填写
4. **InterviewInvitationStep.vue** - 面试邀请通知
5. **InterviewTrainingStep.vue** - 面试培训安排
6. **AttendInterviewStep.vue** - 参加面试
7. **AdmissionNoticeStep.vue** - 录取通知
8. **AcceptOfferDepositStep.vue** - 接受录取&留位费
9. **VisaPreparationStep.vue** - 签证申请准备
10. **ConditionalMaterialsStep.vue** - 条件录取材料递交
11. **VisaFormalOfferStep.vue** - 签证审批&正式录取
12. **AccommodationRegistrationStep.vue** - 住宿申请&注册

**删除的步骤**：
- APPLICATION_SUBMISSION（申请递交）- 合并到APPLICATION_FILLING
- SCHOOL_VISA_UPLOAD（学校签证系统上传）- 合并到VISA_PREPARATION

#### 2. 核心组件重构

**ApplicationProgressDialog.vue** 重构要点：
- 步骤配置完全从数据库读取
- 支持动态步骤跳转逻辑
- 优化步骤状态管理
- 实现关键步骤控制

**StepOperationForm.vue** 重构要点：
- 动态加载对应步骤组件
- 统一JSON数据处理
- 支持步骤完成/跳过操作
- **动态跳转选择**：在有跳转选项的步骤中显示选择界面
- 关键步骤验证机制

**跳转选择实现示例**：
```vue
<template>
  <div class="step-operation-form">
    <!-- 常规步骤表单内容 -->
    <component :is="currentStepComponent" v-model="stepData" />
    
    <!-- 跳转选择区域（如果该步骤有跳转选项） -->
    <div v-if="hasJumpOptions" class="jump-options">
      <h4>请选择后续流程：</h4>
      <el-radio-group v-model="selectedJumpOption">
        <el-radio 
          v-for="option in jumpOptions" 
          :key="option.key" 
          :label="option.key"
        >
          {{ option.label }}
        </el-radio>
      </el-radio-group>
    </div>
    
    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleComplete">完成当前步骤</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      stepData: {},
      selectedJumpOption: null
    }
  },
  computed: {
    hasJumpOptions() {
      return this.currentStepConfig?.jumpOptions?.length > 0
    },
    jumpOptions() {
      return this.currentStepConfig?.jumpOptions || []
    }
  },
  methods: {
    handleComplete() {
      // 添加跳转选择到步骤数据
      if (this.selectedJumpOption) {
        this.stepData.jumpOption = this.jumpOptions.find(opt => opt.key === this.selectedJumpOption)
      }
      
      // 提交步骤完成
      this.$emit('complete', this.stepData)
    }
  }
}
</script>
```

### 后端重构方案

#### 1. 数据库结构调整

**主表字段更新**：
```sql
-- 材料计划进度表 (t_st_material_plan_progress)
ALTER TABLE t_st_material_plan_progress 
ADD COLUMN steps_config TEXT COMMENT '步骤配置JSON';

-- 步骤数据表结构保持不变，但step_data字段存储内容按新规范
```

**步骤状态字段映射**：
```sql
-- 12个步骤对应的状态字段
step_writing_process_status              -- 文书环节状态
step_writing_finalization_status         -- 文书定稿状态
step_application_filling_status          -- 学校申请填写状态
step_interview_invitation_status         -- 面试邀请通知状态
step_interview_training_status           -- 面试培训安排状态
step_attend_interview_status             -- 参加面试状态
step_admission_notice_status             -- 录取通知状态
step_accept_offer_deposit_status         -- 接受录取&留位费状态
step_visa_preparation_status             -- 签证申请准备状态
step_conditional_materials_status        -- 条件录取材料递交状态
step_visa_formal_offer_status            -- 签证审批&正式录取状态
step_accommodation_registration_status   -- 住宿申请&注册状态
```

#### 2. 业务逻辑重构

**初始化逻辑**：
- 创建进度记录时初始化12个步骤状态为PENDING
- 设置默认当前步骤为WRITING_PROCESS
- 初始化steps_config配置

**动态步骤配置逻辑**：
- 实现用户选择驱动的跳转机制
- 步骤完成时根据用户选择动态调整后续流程
- 支持实时更新steps_config JSON配置
- 关键步骤控制验证
- 步骤完成后智能推进到下一步

**核心业务方法**：
```java
// 初始化步骤配置
public void initializeStepsConfig(Long progressId) {
    StepsConfig config = createDefaultStepsConfig();
    materialPlanProgressMapper.updateStepsConfig(progressId, JSON.toJSONString(config));
}

// 处理步骤完成并动态调整配置
public void completeStepWithJump(Long progressId, String stepKey, StepData stepData) {
    StepsConfig config = getStepsConfig(progressId);
    
    // 标记当前步骤完成
    config.getSteps().get(stepKey).setStatus("COMPLETED");
    config.getCompletedSteps().add(stepKey);
    
    // 处理跳转选择
    if (stepData.getJumpOption() != null) {
        processJumpOption(config, stepKey, stepData.getJumpOption());
    }
    
    // 推进到下一步
    String nextStep = determineNextStep(config, stepKey);
    config.setCurrentStep(nextStep);
    
    // 保存配置
    materialPlanProgressMapper.updateStepsConfig(progressId, JSON.toJSONString(config));
}

// 根据用户选择处理跳转逻辑
private void processJumpOption(StepsConfig config, String stepKey, JumpOption jumpOption) {
    Step step = config.getSteps().get(stepKey);
    step.setSelectedOption(jumpOption);
    
    // 根据不同的跳转选择，标记相应步骤为跳过
    switch (jumpOption.getKey()) {
        case "direct_admission":
            skipSteps(config, Arrays.asList("INTERVIEW_INVITATION", "INTERVIEW_TRAINING", "ATTEND_INTERVIEW"));
            break;
        case "unconditional":
            skipSteps(config, Arrays.asList("CONDITIONAL_MATERIALS"));
            break;
        case "no_visa_required":
            skipSteps(config, Arrays.asList("CONDITIONAL_MATERIALS"));
            break;
    }
}
```

## 实施计划

### Phase 1: 前端组件重构

1. **步骤表单组件** (1-2天)
   - 重构12个步骤表单组件
   - 统一JSON数据结构
   - 实现表单验证逻辑

2. **核心对话框组件** (1天)
   - 重构ApplicationProgressDialog.vue
   - 优化步骤显示和交互逻辑
   - 实现动态步骤加载

### Phase 2: 后端逻辑重构

1. **数据库调整** (0.5天)
   - 更新表结构
   - 数据迁移脚本

2. **业务逻辑重构** (1-2天)
   - 重构初始化逻辑
   - 实现跳转机制
   - 优化状态管理

### Phase 3: 集成测试

1. **功能测试** (1天)
   - 完整流程测试
   - 跳转逻辑验证
   - 边界情况测试

2. **性能优化** (0.5天)
   - 前端渲染优化
   - 后端查询优化

## 关键技术要点

### 1. 动态步骤跳转机制

```javascript
// 步骤操作时的跳转选择
const stepJumpOptions = {
  'APPLICATION_FILLING': {
    label: '学校申请填写',
    options: [
      { key: 'normal', label: '正常流程（包含面试）', nextStep: 'INTERVIEW_INVITATION' },
      { key: 'direct_admission', label: '直接录取（跳过面试）', nextStep: 'ADMISSION_NOTICE' }
    ]
  },
  'ADMISSION_NOTICE': {
    label: '录取通知',
    options: [
      { key: 'conditional', label: '条件录取', nextStep: 'ACCEPT_OFFER_DEPOSIT' },
      { key: 'unconditional', label: '无条件录取（跳过条件材料）', nextStep: 'VISA_PREPARATION' }
    ]
  },
  'VISA_PREPARATION': {
    label: '签证申请准备',
    options: [
      { key: 'normal', label: '正常签证流程', nextStep: 'CONDITIONAL_MATERIALS' },
      { key: 'no_visa_required', label: '无需签证（本地学生）', nextStep: 'VISA_FORMAL_OFFER' }
    ]
  }
}

// 用户选择跳转选项后，动态更新步骤配置
const updateStepsConfig = (progressId, currentStep, selectedOption) => {
  const config = getProgressStepsConfig(progressId)
  
  // 根据选择更新步骤配置
  if (selectedOption.key === 'direct_admission') {
    // 跳过面试相关步骤
    config.steps.INTERVIEW_INVITATION.status = 'SKIPPED'
    config.steps.INTERVIEW_TRAINING.status = 'SKIPPED'
    config.steps.ATTEND_INTERVIEW.status = 'SKIPPED'
  }
  
  // 更新数据库中的steps_config
  updateProgressStepsConfig(progressId, config)
}
```

### 2. steps_config JSON结构设计

```javascript
// 步骤配置JSON结构
const stepsConfigSchema = {
  currentStep: 'WRITING_PROCESS',  // 当前步骤
  steps: {
    'WRITING_PROCESS': {
      status: 'PENDING',           // PENDING/IN_PROGRESS/COMPLETED/SKIPPED
      order: 1,                    // 步骤顺序
      required: true,              // 是否必须
      nextStep: 'WRITING_FINALIZATION'
    },
    'APPLICATION_FILLING': {
      status: 'PENDING',
      order: 3,
      required: true,
      // 可选择的跳转选项
      jumpOptions: [
        { key: 'normal', label: '正常流程（包含面试）', nextStep: 'INTERVIEW_INVITATION' },
        { key: 'direct_admission', label: '直接录取（跳过面试）', nextStep: 'ADMISSION_NOTICE' }
      ],
      selectedOption: null,        // 用户选择的选项
      nextStep: 'INTERVIEW_INVITATION'  // 默认下一步，可根据选择动态调整
    }
    // ... 其他步骤
  },
  completedSteps: [],              // 已完成步骤列表
  skippedSteps: []                 // 已跳过步骤列表
}
```

### 3. 统一的步骤数据结构

```javascript
// 各步骤的step_data JSON结构
const stepDataSchema = {
  remark: '',                      // 备注说明 - 所有步骤通用
  jumpOption: null,                // 跳转选择（如果该步骤有跳转选项）
  // 各步骤特有字段按文档规范定义
}
```

### 4. 关键步骤验证

```javascript
// 关键步骤列表（不可跳过，但可以有不同的处理方式）
const CRITICAL_STEPS = [
  'WRITING_FINALIZATION',
  'APPLICATION_FILLING',
  'ADMISSION_NOTICE',
  'ACCEPT_OFFER_DEPOSIT',
  'VISA_FORMAL_OFFER',
  'ACCOMMODATION_REGISTRATION'
]

// 步骤完成时的处理逻辑
const completeStep = (progressId, stepKey, stepData) => {
  const config = getProgressStepsConfig(progressId)
  
  // 更新当前步骤状态
  config.steps[stepKey].status = 'COMPLETED'
  config.completedSteps.push(stepKey)
  
  // 如果用户选择了跳转选项，动态调整后续步骤
  if (stepData.jumpOption) {
    const option = config.steps[stepKey].jumpOptions.find(opt => opt.key === stepData.jumpOption.key)
    if (option) {
      // 记录用户选择
      config.steps[stepKey].selectedOption = stepData.jumpOption
      
      // 根据选择跳过相应步骤
      updateSkippedSteps(config, stepKey, option)
      
      // 更新下一步
      config.currentStep = option.nextStep
    }
  } else {
    // 正常流程，进入下一步
    config.currentStep = config.steps[stepKey].nextStep
  }
  
  // 保存配置
  updateProgressStepsConfig(progressId, config)
}
```

## 风险评估

### 高风险点
1. **数据迁移**：现有14步骤数据需要安全迁移到12步骤
2. **业务逻辑变更**：动态跳转机制比静态规则更复杂，可能影响现有流程
3. **JSON配置完整性**：steps_config的动态更新需要确保数据一致性
4. **用户操作复杂度**：增加了跳转选择可能让用户操作变复杂

### 缓解措施
1. **数据备份**：重构前完整备份现有数据
2. **灰度发布**：先在测试环境验证完整流程
3. **回滚方案**：保留原有代码分支，支持快速回滚
4. **JSON配置验证**：添加steps_config的完整性检查和自动修复机制
5. **用户体验优化**：在跳转选择界面添加详细说明和帮助提示
6. **测试用例覆盖**：针对所有跳转场景编写完整的自动化测试

## 验收标准

### 功能验收
- [ ] 12个步骤表单组件完整实现
- [ ] 动态步骤跳转选择功能正常
- [ ] 用户选择跳转后steps_config正确更新
- [ ] 跳过的步骤状态正确标记为SKIPPED
- [ ] 关键步骤控制有效
- [ ] JSON数据存储格式正确
- [ ] 流程完成状态管理准确
- [ ] 各种跳转场景（直接录取、无条件录取、无需签证）测试通过

### 性能验收
- [ ] 前端组件加载时间 < 500ms
- [ ] 步骤数据查询响应时间 < 200ms
- [ ] 大批量数据处理性能稳定

### 兼容性验收
- [ ] 现有数据完整迁移
- [ ] API接口向后兼容
- [ ] 用户操作习惯保持一致

## 后续优化方向

1. **智能推荐**：基于历史数据智能推荐步骤操作
2. **流程模板**：支持不同学校/专业的流程模板配置
3. **进度预测**：基于当前进度预测完成时间
4. **自动化集成**：与学校系统API对接，自动更新状态 