<template>
  <div class="conditional-materials-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>条件录取材料递交详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>条件材料清单：</label>
            <span>{{ stepData.materialChecklist || '暂未填写' }}</span>
          </div>
          <div class="info-item">
            <label>递交时间：</label>
            <span>{{ parseTime(stepData.submissionTime) || '暂未递交' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="条件材料清单" prop="materialChecklist">
          <el-input
            v-model="stepData.materialChecklist"
            type="textarea"
            :rows="3"
            placeholder="请详细列出需要递交的条件材料清单"
          />
        </el-form-item>

        <el-form-item label="递交时间" prop="submissionTime">
          <el-date-picker
            v-model="stepData.submissionTime"
            type="datetime"
            placeholder="选择递交时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="获有条件录取的学生，需递交满足条件的学术材料等，以换取正式录取"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  materialChecklist: '',
  submissionTime: '',
  remark: ''
})

// 验证规则
const rules = {
  materialChecklist: [
    { required: true, message: '请填写条件材料清单', trigger: 'blur' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    materialChecklist: '',
    submissionTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    materialChecklist: '',
    submissionTime: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.conditional-materials-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 