<template>
  <div class="accept-offer-deposit-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>接受录取&留位费详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否接受录取：</label>
            <span>{{ stepData.offerAccepted ? '是' : '否' }}</span>
          </div>
          <div class="info-item">
            <label>是否需要缴纳留位费：</label>
            <span>{{ stepData.depositRequired ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.depositRequired" class="info-item">
            <label>留位费金额：</label>
            <span>{{ stepData.depositAmount || '暂未填写' }}</span>
          </div>
          <div v-if="stepData.depositRequired" class="info-item">
            <label>留位费截止时间：</label>
            <span>{{ parseTime(stepData.depositDeadline) || '暂未设置' }}</span>
          </div>
          <div v-if="stepData.depositRequired" class="info-item">
            <label>缴费时间：</label>
            <span>{{ parseTime(stepData.payDepositTime) || '暂未缴费' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否接受录取" prop="offerAccepted">
          <el-radio-group v-model="stepData.offerAccepted">
            <el-radio :label="true">接受录取</el-radio>
            <el-radio :label="false">拒绝录取</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否缴纳留位费" prop="depositRequired">
          <el-radio-group v-model="stepData.depositRequired">
            <el-radio :label="true">需要缴费</el-radio>
            <el-radio :label="false">不需要缴费</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="stepData.depositRequired">
          <el-form-item label="留位费金额" prop="depositAmount">
            <el-input
              v-model="stepData.depositAmount"
              placeholder="请输入留位费金额"
            />
          </el-form-item>

          <el-form-item label="留位费截止时间" prop="depositDeadline">
            <el-date-picker
              v-model="stepData.depositDeadline"
              type="datetime"
              placeholder="选择截止时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="缴费时间" prop="payDepositTime">
            <el-date-picker
              v-model="stepData.payDepositTime"
              type="datetime"
              placeholder="选择缴费时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="录取后接受入读有截止日期，多数学校要求缴纳留位费"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  offerAccepted: false,
  depositRequired: false,
  depositAmount: '',
  depositDeadline: '',
  payDepositTime: '',
  remark: ''
})

// 验证规则
const rules = {
  offerAccepted: [
    { required: true, message: '请选择是否接受录取', trigger: 'change' }
  ],
  depositRequired: [
    { required: true, message: '请选择是否需要缴纳留位费', trigger: 'change' }
  ],
  depositAmount: [
    { required: true, message: '请输入留位费金额', trigger: 'blur', validator: (rule, value, callback) => {
      if (stepData.depositRequired && !value) {
        callback(new Error('请输入留位费金额'))
      } else {
        callback()
      }
    }}
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    offerAccepted: false,
    depositRequired: false,
    depositAmount: '',
    depositDeadline: '',
    payDepositTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    offerAccepted: false,
    depositRequired: false,
    depositAmount: '',
    depositDeadline: '',
    payDepositTime: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.accept-offer-deposit-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 