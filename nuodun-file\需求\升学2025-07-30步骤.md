# 诺盾教育申请跟踪流程规范文档 v3.0

## 概述

本文档定义了诺盾教育申请跟踪系统的完整流程结构，共12个核心步骤，分为4个阶段，支持灵活的流程跳转以适应不同学校的申请要求。

## 流程架构

### 总体结构
- **12个核心步骤**：覆盖从文书准备到最终注册的完整申请流程
- **4个业务阶段**：申请准备 → 申请后环节 → 录取后流程 → 最终确认
- **灵活跳转机制**：支持根据学校要求跳过非必须步骤（如面试环节）
- **关键步骤控制**：部分关键步骤不可跳过，确保流程完整性

### 数据存储说明
- 所有步骤数据统一使用JSON格式存储在`step_data`字段中
- 时间信息、备注说明等均包含在JSON结构内
- 每个步骤的状态独立管理，支持灵活的流程控制

---

## 第一阶段：申请准备阶段 (PREPARATION)

### 1. 文书环节 (WRITING_PROCESS)
**步骤说明**：定校结果确定后，进入文书环节
- **业务内容**：
  - 文书约谈安排和沟通
  - 文书老师分配和指导方向确定
  - 文书撰写要点和内容规划
  - 根据学校要求制定文书策略
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "needsConsultation": false,        // 是否需要文书约谈
    "writingTeacher": "",              // 文书老师姓名
    "writingDirection": "",            // 文书撰写方向和内容要点
    "consultationTime": "",            // 文书约谈时间
    "completedTime": "",               // 步骤完成时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：是
- **跳转说明**：如无需专门文书环节，可直接进入文书定稿

### 2. 文书定稿 (WRITING_FINALIZATION)
**步骤说明**：文书内容确认和版本管理
- **业务内容**：
  - 文书内容最终确认
  - 版本控制和文件管理
  - 客户确认和签字
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "finalFileList": [],               // 定稿文件列表(数组结构)
    "completedTime": "",               // 定稿完成时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（关键步骤）

### 3. 学校申请填写 (APPLICATION_FILLING)
**步骤说明**：进行学校申请填写
- **业务内容**：
  - 填写学校官方申请表格
  - 上传所需材料和文书
  - 申请信息核对和确认
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "applicationNumber": "",           // 申请编号
    "applicationEmail": "",            // 申请邮箱
    "applicationPassword": "",         // 申请密码
    "submissionTime": "",              // 申请提交时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（关键步骤）
- **流程分叉**：完成后可能直接跳转至第7步（录取通知），跳过面试环节

---

## 第二阶段：申请后环节 (POST_APPLICATION)

### 4. 面试邀请通知 (INTERVIEW_INVITATION)
**步骤说明**：部分学校会安排面试，升学老师沟通并安排面培
- **业务内容**：
  - 接收学校面试邀请通知
  - 部分学校直接跳过面试环节
  - 升学老师评估面试需求
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "interviewInvited": false,         // 是否收到面试邀请
    "interviewTime": "",               // 面试时间
    "interviewMethod": "",             // 面试方式（线上/线下）
    "interviewLink":  "",              //面试链接
    "invitationReceiveTime": "",       // 收到邀请时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：是（学校不安排面试时自动跳过）
- **流程说明**：如未收到面试邀请，直接跳转至第7步（录取通知）

### 5. 面试培训安排 (INTERVIEW_TRAINING)
**步骤说明**：升学老师沟通并安排面培
- **业务内容**：
  - 安排面试培训课程
  - 模拟面试练习
  - 面试技巧指导
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "trainingArranged": false,         // 是否安排面培
    "trainingTime": "",                // 面培时间
    "trainingTeacher": "",             // 面培老师
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（如不需要面试培训）

### 6. 参加面试 (ATTEND_INTERVIEW)
**步骤说明**：学生参加学校面试
- **业务内容**：
  - 学生按时参加面试
  - 面试结果跟踪
  - 成功→进入录取环节，失败→终止该学校申请
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "interviewAttended": false,        // 是否参加面试
    "attendTime": "",                  // 参加面试时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（如未安排面试）

### 7. 录取通知 (ADMISSION_NOTICE)
**步骤说明**：收到录取通知（有条件录取或正式录取）
- **业务内容**：
  - 接收学校录取通知
  - 确认录取类型（有条件/无条件）
  - 录取条件解读和说明
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "admissionReceived": false,        // 是否收到录取：true or false
    "admissionType": "CONDITIONAL",    // 录取类型：CONDITIONAL有条件录取 UNCONDITIONAL无条件录取
    "noticeTime": "",                  // 录取通知时间
    "admissionConditions": "",         // 录取条件（有条件录取时填写）
    "admissionDeadline": "",           // 录取的入读截止时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（关键步骤，未收到录取通知则结束流程）

---

## 第三阶段：录取后流程 (POST_ADMISSION)

### 8. 接受录取&留位费 (ACCEPT_OFFER_DEPOSIT)
**步骤说明**：录取后接受入读并缴纳留位费
- **业务内容**：
  - 录取后接受入读（有截止日期）
  - 多数学校要求缴纳留位费
  - 留位费金额和截止时间管理
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "offerAccepted": false,            // 是否接受录取
    "depositRequired": false,          // 是否需要缴纳留位费
    "depositAmount": "",               // 留位费金额
    "depositDeadline": "",             // 留位费截止时间
    "payDepositTime": "",              // 缴费时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（需要选择是否接受录取）

### 9. 签证申请准备 (VISA_PREPARATION)
**步骤说明**：缴纳留位费后，进入签证申请环节
- **业务内容**：
  - 需填写ID995A、同意书等表格
  - 收集存款证明等材料
  - 签证材料准备和整理
  - 缴纳签证行政费
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "visaRequired": false,             // 是否需要签证申请
    "formsFilled": "",                 // 表格填写状态（ID995A、同意书等）
    "materialsList": [],               // 材料文件列表
    "visaFeePaid": false,              // 是否缴纳签证行政费
    "visaFeeAmount": 0,                // 签证行政费金额
    "feePayTime": "",                  // 缴费时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：是（如不需要签证申请）

---

## 第四阶段：最终确认阶段 (FINAL_CONFIRMATION)

### 10. 条件录取材料递交 (CONDITIONAL_MATERIALS)
**步骤说明**：获有条件录取的学生，需递交满足条件的学术材料等
- **业务内容**：
  - 有条件录取学生递交满足条件的学术材料
  - 以换取正式录取
  - 材料审核和确认
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "conditionalMaterialsRequired": false,  // 是否需要递交条件材料
    "materialChecklist": "",                // 条件材料清单
    "submissionTime": "",                   // 递交时间
    "submissionStatus": "",                 // 递交状态
    "remark": ""                            // 备注说明
  }
  ```
- **可跳过**：是（如获得的是无条件录取）
- **流程说明**：仅对有条件录取的学生开启此步骤

### 11. 签证审批&正式录取 (VISA_FORMAL_OFFER)
**步骤说明**：签证审批通过且所有条件满足后，获得正式录取
- **业务内容**：
  - 签证审批通过且所有条件满足后
  - 将获得正式录取或注册通知
  - 最终录取确认
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "visaApprovalStatus": "",          // 签证审批状态：PENDING待审批 APPROVED已通过 REJECTED已拒绝
    "visaApprovalTime": "",            // 签证审批时间
    "isReceiveFormalOffer": false,     // 是否收到正式录取
    "receiveFormalOfferTime": "",      // 正式录取时间
    "formalOfferDetails": "",          // 正式录取详情
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（关键步骤）

### 12. 住宿申请&注册 (ACCOMMODATION_REGISTRATION)
**步骤说明**：住宿申请和学校注册
- **业务内容**：
  - 部分学校涉及住宿申请
  - 注册为所有学生的必办流程
  - 最终入学确认
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "accommodationApplied": false,     // 是否申请住宿
    "accommodationTime": "",           // 住宿申请时间
    "registrationCompleted": false,    // 注册状态
    "registrationTime": "",            // 注册时间
    "enrollmentConfirmed": null,       // 最终入学确认（true/false/null）
    "confirmTime": "",                 // 确认时间
    "remark": ""                       // 备注说明
  }
  ```
- **可跳过**：否（关键步骤，注册为必办流程）

---

## 流程跳转机制

### 主要跳转路径

1. **标准完整流程**：
   `1 → 2 → 3 → 4 → 5 → 6 → 7 → 8 → 9 → 10 → 11 → 12`

2. **跳过面试流程**：
   `1 → 2 → 3 → 7 → 8 → 9 → 10 → 11 → 12`

3. **无条件录取流程**：
   `1 → 2 → 3 → (4 → 5 → 6) → 7 → 8 → 9 → 11 → 12`

4. **无需签证流程**：
   `1 → 2 → 3 → (4 → 5 → 6) → 7 → 8 → 10 → 11 → 12`

### 关键控制点

- **步骤3完成后**：系统检查是否收到面试邀请，决定是否跳过步骤4-6
- **步骤7完成后**：根据录取类型（有条件/无条件）决定是否开启步骤10
- **步骤8完成后**：根据目标国家/地区决定是否需要步骤9

---

## 技术实现要点

### 步骤状态管理
- **PENDING**：待处理
- **IN_PROGRESS**：进行中  
- **COMPLETED**：已完成
- **SKIPPED**：已跳过
- **CANCELLED**：已取消

### 关键步骤控制
以下步骤为关键步骤，不可跳过：
- 文书定稿 (WRITING_FINALIZATION)
- 学校申请填写 (APPLICATION_FILLING)  
- 录取通知 (ADMISSION_NOTICE)
- 接受录取&留位费 (ACCEPT_OFFER_DEPOSIT)
- 签证审批&正式录取 (VISA_FORMAL_OFFER)
- 住宿申请&注册 (ACCOMMODATION_REGISTRATION)

### JSON字段通用结构
每个步骤的JSON数据都包含以下通用字段：
- `completedTime`：步骤完成时间
- `remark`：备注说明
- 其他业务相关的具体字段

### 数据库映射
| 业务概念 | 主表字段 | 说明 |
|---------|----------|------|
| 当前步骤 | current_step | 当前执行的步骤编码 |
| 步骤名称 | current_step_name | 当前步骤的中文名称 |
| 整体状态 | progress_status | 整个申请流程的状态 |
| 步骤配置 | steps_config | JSON格式的步骤配置信息 |
| 具体步骤数据 | step_data (步骤表) | 每个步骤的具体业务数据 |