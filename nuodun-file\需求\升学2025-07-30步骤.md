# 诺盾教育申请跟踪流程规范文档 v2.0

## 概述

本文档定义了诺盾教育申请跟踪系统的完整流程结构，将原有的11个步骤优化为14个步骤，分为4个阶段，更贴合实际业务流程。

## 流程架构

### 总体结构
- **14个核心步骤**：覆盖从文书准备到最终注册的完整申请流程
- **4个业务阶段**：申请准备 → 申请后环节 → 录取后流程 → 最终确认
- **关键步骤控制**：部分关键步骤不可跳过，确保流程完整性
- **灵活性设计**：支持根据不同学校要求调整流程
---

## 第一阶段：申请准备阶段

### 1. 文书环节 (WRITING_PROCESS)
**步骤说明**：定校结果确定后，进入文书环节
- **业务内容**：
- **JSON字段配置** (`step_data`)：
  ```json
  {
    "needsConsultation": false,      // 是否需要文书约谈
    "writingTeacher": "",            // 文书老师姓名
    "writingDirection": ""           // 文书撰写方向和内容要点
    //文书约谈时间
    //备注说明
  }
  ```
- **可跳过**：是

### 2. 文书定稿 (WRITING_FINALIZATION)
**步骤说明**：文书内容确认和版本管理
- **业务内容**：
- **数据库字段说明**：
  - `step_writing_finalization_remark` - 备注说明
  - `step_writing_finalization_select_time` - 定稿确认时间
  - `step_writing_finalization_data` - 业务数据(JSON)
- **JSON字段配置** (`step_writing_finalization_data`)：
  ```json
  {
    "finalFileList": "",            //定稿文件列表,数组结构
  }
  ```
- **可跳过**：否（关键步骤）

### 3. 学校申请填写 (APPLICATION_FILLING)
**步骤说明**：进行学校申请填写
- **业务内容**：
  - 填写学校官方申请表格
  - 上传所需材料和文书
  - 申请信息核对和确认
- **数据库字段说明**：
  - `step_application_filling_remark` - 备注说明
  - `step_application_filling_select_time` - 申请时间
  - `step_application_filling_data` - 业务数据(JSON)
- **JSON字段配置** (`step_application_filling_data`)：
  ```json
  {
    "applicationNumber": "",         // 申请编号
    "applicationEmail": "",          // 申请邮箱
    "applicationPassword": "",       // 申请密码
  }
  ```
- **可跳过**：否（关键步骤）

## 第二阶段：申请后环节

### 4. 面试邀请通知 (INTERVIEW_INVITATION)
**步骤说明**：部分学校会安排面试，升学老师沟通并安排面培
- **业务内容**：
  - 接收学校面试邀请通知
  - 部分学校直接跳过面试环节
  - 升学老师评估面试需求
- **数据库字段说明**：
  - `step_interview_invitation_remark` - 备注说明
  - `step_interview_invitation_select_time` - 收到通知时间
  - `step_interview_invitation_data` - 业务数据(JSON)
- **JSON字段配置** (`step_interview_invitation_data`)：
  ```json
  {
    "interviewInvited": false,       // 是否收到面试邀请
    "interviewTime": "",             // 面试时间
  }
  ```
- **可跳过**：是（如学校不安排面试）

### 5. 面试培训安排 (INTERVIEW_TRAINING)
**步骤说明**：升学老师沟通并安排面培
- **业务内容**：
  - 安排面试培训课程
  - 模拟面试练习
  - 面试技巧指导
- **数据库字段说明**：
  - `step_interview_training_remark` - 备注说明
  - `step_interview_training_select_time` - 面培时间
  - `step_interview_training_data` - 业务数据(JSON)
- **JSON字段配置** (`step_interview_training_data`)：
  ```json
  {
    "trainingArranged": false,       // 是否面培
  }
  ```
- **可跳过**：是（如不需要面试培训或学校不安排面试）

### 6. 参加面试 (ATTEND_INTERVIEW)
**步骤说明**：学生参加学校面试
- **业务内容**：
  - 面试成功则收到录取（有条件或正式）
  - 失败则终止该学校申请
  - 面试结果跟踪
- **数据库字段说明**：
  - `step_attend_interview_remark` - 备注说明
  - `step_attend_interview_select_time` - 面试日期时间
  - `step_attend_interview_data` - 业务数据(JSON)
- **JSON字段配置** (`step_attend_interview_data`)：
  ```json
  {
    "interviewAttended": false,      // 是否参加面试
    "interviewLink": "",             // 面试链接（如线上面试）
  }
  ```
- **可跳过**：是（如未安排面试）

### 7. 录取通知 (ADMISSION_NOTICE)
**步骤说明**：收到录取通知（有条件录取或正式录取）
- **业务内容**：
  - 接收学校录取通知
  - 确认录取类型（有条件/无条件）
  - 录取条件解读和说明
- **数据库字段说明**：
  - `step_admission_notice_remark` - 备注说明
  - `step_admission_notice_select_time` - 选择时间
  - `step_admission_notice_data` - 业务数据(JSON)
- **JSON字段配置** (`step_admission_notice_data`)：
  ```json
  {
    "admissionReceived": false,       // 录取状态（true/false）
    "admissionType": "CONDITIONAL",  // 录取类型（CONDITIONAL/UNCONDITIONAL）,录取状态时true才展示
    "noticeTime": "",                // 录取通知时间
    "admissionConditions": "",        // 录取条件（有条件录取时填写）
    "admissionDeadline": null,       //录取的入读截止时间
  }
  ```
- **可跳过**：否（关键步骤，未收到录取通知则结束流程）

---

## 第三阶段：录取后流程

### 8. 接受录取&留位费 (ACCEPT_OFFER_DEPOSIT)
**步骤说明**：录取后接受入读并缴纳留位费
- **业务内容**：
  - 录取后，接受入读有截止日期
  - 多数学校要求缴纳留位费
  - 留位费金额和截止时间管理
- **数据库字段说明**：
  - `step_accept_offer_deposit_remark` - 备注说明
  - `step_accept_offer_deposit_select_time` - 选择时间
  - `step_accept_offer_deposit_data` - 业务数据(JSON)
- **JSON字段配置** (`step_accept_offer_deposit_data`)：
  ```json
  {
    "offerAccepted": false,          // 是否接受录取
    "depositRequired": false,        // 是否缴纳留位费
    "depositAmount": "",             // 留位费金额
    "depositDeadline": "",           // 留位费截止时间
    "payDepositTime": ""             // 缴费时间
  }
  ```
- **可跳过**：否（需要选择是否接收录取）

### 9. 签证申请提交 (VISA_PREPARATION)
**步骤说明**：缴纳留位费后，进入签证申请环节
- **业务内容**：
  - 需填写 ID995A、同意书等表格
  - 收集存款证明等材料
  - 签证材料准备和整理
- **数据库字段说明**：
  - `step_visa_preparation_remark` - 备注说明
  - `step_visa_preparation_select_time` - 选择时间
  - `step_visa_preparation_data` - 业务数据(JSON)
- **JSON字段配置** (`step_visa_preparation_data`)：
  ```json
  {
    "visaRequired": false,           // 是否需要签证申请
    "formsFilled": "",               // 表格填写状态（ID995A、同意书等）
    "materialsList": [],            // 材料文件列表
    "visaFeePaid": false,          // 是否签证行政费
    "visaFeeAmount": 0            //签证行政费金额，选择是需要填写

  }
  ```
- **可跳过**：是（如不需要签证申请）

## 第四阶段：最终确认阶段

### 10. 递条件录取材料交 (CONDITIONAL_MATERIALS)
**步骤说明**：获有条件录取的学生，需递交满足条件的学术材料等
- **业务内容**：
  - 获有条件录取的学生，需递交满足条件的学术材料等
  - 以换取正式录取
  - 材料审核和确认
- **数据库字段说明**：
  - `step_conditional_materials_remark` - 备注说明
  - `step_conditional_materials_select_time` - 选择时间
  - `step_conditional_materials_data` - 业务数据(JSON)
- **JSON字段配置** (`step_conditional_materials_data`)：
  ```json
  {
    "materialChecklist": "",               // 条件材料清单
    "submissionTime": "",                  // 递交时间
  }
  ```
- **可跳过**：是（如获得的是正式录取而非条件录取）

### 11. 签证审批&正式录取 (VISA_FORMAL_OFFER)
**步骤说明**：签证审批通过且所有条件满足后，获得正式录取
- **业务内容**：
  - 签证审批通过且所有条件满足后
  - 将获得正式录取或注册通知
  - 最终录取确认
- **数据库字段说明**：
  - `step_visa_formal_offer_remark` - 备注说明
  - `step_visa_formal_offer_select_time` - 选择时间
  - `step_visa_formal_offer_data` - 业务数据(JSON)
- **JSON字段配置** (`step_visa_formal_offer_data`)：
  ```json
  {
    "isReceiveFormalOffer": false,   // 是否收到正式录取
    "receiveFormalOfferTime": "",    // 正式录取时间
    "formalOfferDetails": ""         // 正式录取详情
  }
  ```
- **可跳过**：否（关键步骤）

### 12. 住宿申请&注册 (ACCOMMODATION_REGISTRATION)
**步骤说明**：住宿申请和学校注册
- **业务内容**：
  - 部分学校涉及住宿申请
  - 注册为所有学生的必办流程
  - 最终入学确认
- **数据库字段说明**：
  - `step_accommodation_registration_remark` - 备注说明
  - `step_accommodation_registration_select_time` - 选择时间
  - `step_accommodation_registration_data` - 业务数据(JSON)
- **JSON字段配置** (`step_accommodation_registration_data`)：
  ```json
  {
    "accommodationApplied": false,   // 住宿申请状态
    "accommodationTime": "",         // 住宿申请时间
    "registrationCompleted": false,  // 注册状态
    "registrationTime": "",          // 注册时间
    "enrollmentConfirmed": null      // 最终入学确认（true/false/null）
  }
  ```
- **可跳过**：否（关键步骤，注册为必办流程）

---

## 技术实现要点

### JSON字段映射总览

| 步骤代码 | 数据库字段名 | 主要JSON字段 |
|---------|-------------|-------------|
| WRITING_PROCESS | step_writing_process_data | needsConsultation, writingTeacher, consultationTime |
| WRITING_FINALIZATION | step_writing_finalization_data | versionNumber, customerConfirmed, confirmTime |
| APPLICATION_FILLING | step_application_filling_data | applicationNumber, applicationEmail, fillingProgress |
| APPLICATION_SUBMISSION | step_application_submission_data | customerConfirmed, submissionTime, trackingNumber |
| INTERVIEW_INVITATION | step_interview_invitation_data | interviewInvited, interviewTime, interviewMethod |
| INTERVIEW_TRAINING | step_interview_training_data | trainingArranged, trainingTime, trainingTeacher |
| ATTEND_INTERVIEW | step_attend_interview_data | interviewAttended, interviewResult, interviewLink |
| ADMISSION_NOTICE | step_admission_notice_data | admissionReceived, admissionType, admissionConditions |
| ACCEPT_OFFER_DEPOSIT | step_accept_offer_deposit_data | offerAccepted, depositAmount, isPayDeposit |
| VISA_PREPARATION | step_visa_preparation_data | visaRequired, formsFilled, materialsCollected |
| SCHOOL_VISA_UPLOAD | step_school_visa_upload_data | systemUploadRequired, visaAdminFee, uploadProgress |
| CONDITIONAL_MATERIALS | step_conditional_materials_data | conditionalMaterialsRequired, submissionStatus |
| VISA_FORMAL_OFFER | step_visa_formal_offer_data | visaApprovalStatus, isReceiveFormalOffer |
| ACCOMMODATION_REGISTRATION | step_accommodation_registration_data | accommodationApplied, registrationCompleted, enrollmentConfirmed |

### 步骤状态管理
- **PENDING**：待处理
- **IN_PROGRESS**：进行中
- **COMPLETED**：已完成
- **SKIPPED**：已跳过
- **CANCELLED**：已取消

### 关键步骤控制
以下步骤为关键步骤，不可跳过：
- 文书定稿
- 学校申请填写
- 申请递交确认
- 录取通知
- 签证审批&正式录取
- 住宿申请&注册

### 业务字段映射
| 业务概念 | 数据库字段 | 说明 |
|---------|-----------|------|
| 录取通知 | is_receive_admission | 是否收到录取通知 |
| 录取类型 | admission_type | CONDITIONAL/UNCONDITIONAL |
| 留位费 | deposit_amount, is_pay_deposit | 留位费金额和缴费状态 |
| 签证行政费 | visa_admin_fee, is_pay_visa_fee | 签证行政费和缴费状态 |
| 正式录取 | is_receive_formal_offer | 是否收到正式录取 |
| 住宿申请 | accommodation_application | 是否申请住宿 |
| 最终注册 | is_confirm_enrollment | 是否确认就读 |