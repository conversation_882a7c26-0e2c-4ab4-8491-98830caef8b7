<template>
  <div class="writing-finalization-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>文书定稿详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>定稿文件列表：</label>
            <div v-if="stepData.finalFileList && stepData.finalFileList.length > 0">
              <el-tag v-for="(file, index) in stepData.finalFileList" :key="index" class="file-tag">
                {{ file }}
              </el-tag>
            </div>
            <span v-else>暂无文件</span>
          </div>
          <div v-if="stepData.finalTime" class="info-item">
            <label>定稿完成时间：</label>
            <span>{{ stepData.finalTime }}</span>
          </div>
          <div v-if="stepData.remark" class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="定稿文件列表" prop="finalFileList">
          <el-select
            v-model="stepData.finalFileList"
            multiple
            filterable
            allow-create
            placeholder="请输入或选择定稿文件名称"
            style="width: 100%"
          >
            <el-option
              v-for="item in commonFileNames"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
          <div class="form-tip">可以手动输入文件名称，或从常用选项中选择</div>
        </el-form-item>

        <el-form-item label="定稿完成时间" prop="finalTime">
          <el-date-picker
            v-model="stepData.finalTime"
            type="datetime"
            placeholder="请选择定稿完成时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="其他备注信息"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 常用文件名称选项
const commonFileNames = [
  '个人陈述(Personal Statement)',
  '推荐信(Reference Letter)',
  '简历(CV/Resume)',
  '动机信(Motivation Letter)',
  '研究计划(Research Proposal)',
  '作品集(Portfolio)',
  '学术论文(Academic Essay)',
  '补充材料(Supplementary Materials)'
]

// 步骤数据 - 按照新的JSON字段配置
const stepData = reactive({
  finalFileList: [],               // 定稿文件列表(数组结构)
  finalTime: '',                   // 定稿完成时间
  remark: ''                       // 备注说明
})

// 验证规则
const rules = {
  finalFileList: [
    { required: true, type: 'array', min: 1, message: '请至少添加一个定稿文件', trigger: 'change' }
  ],
  finalTime: [
    { required: true, message: '请选择定稿完成时间', trigger: 'change' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    finalFileList: [],
    finalTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    finalFileList: [],
    finalTime: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.writing-finalization-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }

          .file-tag {
            margin: 2px 4px 2px 0;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .form-tip {
      color: #909399;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style> 