<template>
  <div class="writing-finalization-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>文书定稿详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>定稿文件列表：</label>
            <span>{{ finalFileListDisplay || '暂无文件' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="定稿文件列表" prop="finalFileList">
          <el-input
            v-model="finalFileListStr"
            type="textarea"
            :rows="3"
            placeholder="请输入定稿文件列表，用逗号分隔，如：PS.pdf, CV.pdf, RL1.pdf"
          />
          <div class="form-tip">
            <el-text type="info" size="small">
              文书内容确认和版本管理，多个文件请用逗号分隔
            </el-text>
          </div>
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="文书内容确认和版本管理相关说明"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  finalFileList: [],
  remark: ''
})

// 定稿文件列表字符串
const finalFileListStr = computed({
  get: () => Array.isArray(stepData.finalFileList) ? stepData.finalFileList.join(', ') : stepData.finalFileList || '',
  set: (value) => {
    stepData.finalFileList = value ? value.split(',').map(item => item.trim()).filter(item => item) : []
  }
})

// 查看模式显示文件列表
const finalFileListDisplay = computed(() => {
  if (Array.isArray(stepData.finalFileList)) {
    return stepData.finalFileList.join(', ')
  }
  return stepData.finalFileList || ''
})

// 验证规则
const rules = {
  finalFileList: [
    { required: true, message: '请输入定稿文件列表', trigger: 'blur' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    finalFileList: [],
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    finalFileList: [],
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.writing-finalization-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .form-tip {
      margin-top: 8px;
      padding: 8px 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
    }
  }
}
</style> 