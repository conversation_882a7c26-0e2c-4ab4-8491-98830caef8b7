"use strict";const e=require("./vendor.js"),n=require("../api/request.js"),o=require("../config/environment.js");exports.request=function(e){return n.request({url:e.url,method:e.method||"GET",data:e.param,params:e.params,header:e.header,noToken:e.noToken,isLoading:e.isLoading,loadingTitle:e.loadingTitle})},exports.upload=function(t){const{url:r="/system/fileRecord/uploadByImg",name:i="file",filePath:a="",formData:s={},fileType:d="image",header:u=n.getAuthHeader(),isLoading:l=!0,loadingTitle:c="上传中...",success:f=null,fail:m=null,complete:p=null}=t;if(l&&e.index.showLoading(c),!a){l&&e.index.hideLoading();const n=new Error("未提供文件路径");return"function"==typeof m&&m(n),"function"==typeof p&&p(),Promise.reject(n)}return e.index.uploadFile({url:o.environment.baseUrl+r,filePath:a,name:i,formData:s,header:u,fileType:d,success:o=>{let t;l&&e.index.hideLoading();try{t=JSON.parse(o.data)}catch(r){e.index.__f__("error","at common/request.js:76","解析上传返回数据失败:",r),t=o.data}try{t=n.handleResponseDecrypt(t)}catch(i){return e.index.__f__("error","at common/request.js:84","上传响应解密失败:",i),void("function"==typeof m&&m({message:i.message||"数据解密失败",code:"DECRYPT_ERROR"}))}200===o.statusCode?"function"==typeof f&&f(t):"function"==typeof m&&m(t)},fail:n=>{e.index.__f__("error","at common/request.js:104","上传请求失败:",n),l&&e.index.hideLoading(),"function"==typeof m&&m(n)},complete:()=>{"function"==typeof p&&p()}})};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/request.js.map
