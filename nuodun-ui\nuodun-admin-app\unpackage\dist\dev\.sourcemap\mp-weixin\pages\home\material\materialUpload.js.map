{"version": 3, "file": "materialUpload.js", "sources": ["pages/home/<USER>/materialUpload.vue", "../../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNcaG9tZVxtYXRlcmlhbFxtYXRlcmlhbFVwbG9hZC52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <custom-nav title=\"材料上传\" :showLeft=\"true\"></custom-nav>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-wrapper\">\n      <uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\n    </view>\n\n    <!-- 页面内容 -->\n    <view v-else class=\"material-upload\">\n      <!-- 学生在读类型选择 -->\n      <view v-if=\"showStudentStatusSelect\" class=\"select-section\">\n        <view class=\"section-title\">请选择学生在读类型</view>\n        <text class=\"select-tip\">请先选择该学生的在读类型，以便为您展示对应的材料配置：</text>\n        <view class=\"picker-wrapper\">\n          <uni-data-picker placeholder=\"请选择学生在读类型\" popup-title=\"请选择学生在读类型\" :localdata=\"studentStatusOptions\"\n            v-model=\"formData.studentStatus\"></uni-data-picker>\n        </view>\n        <view class=\"select-actions\">\n          <button class=\"action-btn confirm-btn\" @tap=\"handleStudentStatusConfirm\">\n            确认\n          </button>\n        </view>\n      </view>\n\n      <!-- 材料列表为空 -->\n      <view v-else-if=\"!materialOptList.length\" class=\"empty-section\">\n        <view class=\"empty-icon\">📋</view>\n        <text class=\"empty-text\">暂无可上传的材料配置</text>\n      </view>\n\n      <!-- 材料列表 -->\n      <view v-else class=\"materials-container\">\n        <view v-for=\"item in materialOptList\" :key=\"item.id\" class=\"material-item\"\n          :class=\"{ 'required': item.isRequired === 'Y' }\">\n\n          <!-- 材料头部信息 -->\n          <view class=\"material-header\">\n            <view class=\"header-left\">\n              <text class=\"material-title\">{{ item.materialType }}</text>\n              <view class=\"material-tags\">\n                <text class=\"type-tag\" :class=\"item.dataType === '1' ? 'file-tag' : 'text-tag'\">\n                  {{ item.dataType === '1' ? '文件' : '文本' }}\n                </text>\n                <text v-if=\"item.isRequired === 'Y'\" class=\"required-tag\">必填</text>\n              </view>\n            </view>\n          </view>\n\n          <text class=\"material-desc\">{{ item.description }}</text>\n\n          <!-- 材料内容 -->\n          <view class=\"material-content\">\n            <!-- 文件类型 -->\n            <view v-if=\"item.dataType === '1'\" class=\"file-container\">\n              <!-- 已上传文件列表 - 只有当有有效文件时才显示 -->\n              <view v-if=\"getValidFileList(item.fileList).length > 0\" class=\"file-list\">\n                <DocumentPreview v-for=\"(file, fileIndex) in getValidFileList(item.fileList)\" :key=\"fileIndex\"\n                  :file-path=\"(fileUrl + file.filePath) || file.fileFullPath || ''\"\n                  :file-name=\"file.sourceFileName || file.fileName || file.name || '未知文件'\"\n                  :allow-view=\"!!((fileUrl + file.filePath) || file.fileFullPath)\"\n                  :allow-download=\"!!((fileUrl + file.filePath) || file.fileFullPath)\" :allow-delete=\"true\"\n                  @delete=\"handleFileDelete(item.id, fileIndex, $event)\" class=\"material-file-item\" />\n              </view>\n\n              <!-- 上传按钮 -->\n              <view class=\"upload-btn\" @tap=\"chooseFiles(item.id)\">\n                <text class=\"upload-icon\">+</text>\n                <view class=\"upload-content\">\n                  <text class=\"upload-text\">{{\n                    item.fileList && item.fileList.length > 0 ? '继续添加' : '上传文件'\n                  }}\n                  </text>\n                  <text class=\"upload-tip\">\n                    <!-- #ifdef MP-WEIXIN -->\n                    支持文档、图片格式（可选择相册或聊天记录）\n                    <!-- #endif -->\n                    <!-- #ifndef MP-WEIXIN -->\n                    {{ isWechatBrowser ? '支持文档、图片格式（微信浏览器）' : '支持文档、图片格式' }}\n                    <!-- #endif -->\n                  </text>\n                  <!-- 调试信息 -->\n                  <text v-if=\"isWechatBrowser\" class=\"debug-info\">\n                    微信浏览器环境 - 可能无法正确上传\n                  </text>\n                </view>\n              </view>\n            </view>\n\n            <!-- 文本类型 -->\n            <view v-else-if=\"item.dataType === '2'\" class=\"text-container\">\n              <view class=\"text-header\">\n                <text class=\"text-title\">账号信息</text>\n                <text class=\"add-btn\" @tap=\"addTextRow(item.id)\">\n                  添加\n                </text>\n              </view>\n\n              <view class=\"text-rows\">\n                <view v-for=\"(row, index) in item.textDataList\" :key=\"index\" class=\"text-row\">\n                  <input v-model=\"row.key\" @input=\"handleTextDataChange(item.id)\" placeholder=\"例：账号\"\n                    class=\"text-input key-input\" />\n                  <input v-model=\"row.value\" @input=\"handleTextDataChange(item.id)\" placeholder=\"例：12345678\"\n                    class=\"text-input value-input\" />\n                  <text class=\"delete-row-btn\" @tap=\"removeTextRow(item.id, index)\"\n                    :class=\"{ disabled: item.textDataList.length <= 1 }\">\n                    删除\n                  </text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 底部空间，防止内容被固定按钮遮挡 -->\n      <view class=\"bottom-spacing\"></view>\n\n      <!-- 操作按钮 -->\n      <view v-if=\"materialOptList.length > 0\" class=\"action-section\">\n        <button class=\"action-btn save-btn\" @tap=\"handleSave\" :disabled=\"saveLoading\">\n          <text v-if=\"saveLoading\">保存中...</text>\n          <text v-else>保存材料</text>\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, getCurrentInstance } from 'vue'\nimport config from '@/config/environment'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { getMaterialByTypeAndStatus } from '@/api/home/<USER>'\nimport { getMaterialByStId, saveOrUpdateStMaterial } from '@/api/home/<USER>'\nimport { selectStMaterialStAndType, uploadMaterialList } from '@/api/home/<USER>'\nimport { chooseAndUploadFile, getFileExtension, validateMaterialFileType } from '@/common/fileUtils.js'\nimport { createModal } from '@/common/modal'\nimport { validateRequiredMaterials, buildSubmitData } from './materialValidation.js'\nimport CustomNav from '@/components/custom-nav/custom-nav.vue'\nimport DocumentPreview from '@/components/document-preview/index.vue'\n\nconst fileUrl = config.fileUrl || '';\n// 获取 toast 实例\nconst { proxy } = getCurrentInstance()\nconst toast = proxy.toast\n// 字典数据\nconst { t_st_study_status } = proxy.useDict('t_st_study_status')\n\n// 响应式数据\nconst loading = ref(false)\nconst saveLoading = ref(false)\n\n// 加载中文本\nconst loadingText = {\n  contentdown: '正在加载...',\n  contentrefresh: '加载中...',\n  contentnomore: '没有更多数据了'\n}\n\n// 学生信息\nconst stInfo = ref({\n  stId: null,\n  stDecaType: '',\n  studentStatus: '',\n  materialPlanId: null\n})\n\n// 学生在读类型选择相关\nconst showStudentStatusSelect = ref(false)\n\n// 表单数据\nconst formData = ref({\n  studentStatus: ''\n})\n\n// 学生在读类型选项数据\nconst studentStatusOptions = ref([])\n// 材料配置列表\nconst materialOptList = ref([])\n// 材料文件变更记录\nconst materialChanges = ref({})\n\n\n// 检测是否在微信浏览器中\nconst isWechatBrowser = computed(() => {\n  // #ifdef H5\n  if (typeof navigator !== 'undefined') {\n    return /micromessenger/i.test(navigator.userAgent)\n  }\n  // #endif\n  return false\n})\n\n\n// 获取有效的文件列表\nconst getValidFileList = (fileList) => {\n  if (!fileList || !Array.isArray(fileList)) return []\n  const validFiles = fileList.filter(file => file && (file.sourceFileName || file.fileName || file.name))\n  return validFiles\n}\n\n// 显示文件来源选择器（微信小程序专用）\nconst showFileSourceSelector = () => {\n  return new Promise((resolve) => {\n    uni.showActionSheet({\n      itemList: ['从手机相册选择（仅图片）', '从微信聊天记录选择（所有文件）'],\n      success: (res) => {\n        if (res.tapIndex === 0) {\n          resolve('album') // 从相册选择\n        } else if (res.tapIndex === 1) {\n          resolve('chat') // 从聊天记录选择\n        }\n      },\n      fail: () => {\n        resolve(null) // 用户取消\n      }\n    })\n  })\n}\n\n// 处理文件删除\nconst handleFileDelete = (materialOptId, fileIndex, event) => {\n  // 由于我们使用了过滤后的文件列表，需要找到原始文件列表中的真实索引\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n  if (!materialOpt || !materialOpt.fileList) return\n\n  const validFiles = getValidFileList(materialOpt.fileList)\n  const fileToDelete = validFiles[fileIndex]\n\n  // 在原始文件列表中找到这个文件的真实索引\n  const realIndex = materialOpt.fileList.findIndex(file =>\n    file === fileToDelete ||\n    (file.sourceFileName === fileToDelete.sourceFileName && file.fileFullPath === fileToDelete.fileFullPath) ||\n    (file.fileName === fileToDelete.fileName && file.fileUrl === fileToDelete.fileUrl) ||\n    (file.sourceFileName === event.fileName && file.fileFullPath === event.filePath)\n  )\n\n  if (realIndex !== -1) {\n    removeFile(materialOptId, realIndex)\n  } else {\n    console.warn('未找到要删除的文件')\n  }\n}\n\n// 处理学生在读类型确认\nconst handleStudentStatusConfirm = async () => {\n  if (!formData.value.studentStatus) {\n    toast.show('请选择学生在读类型')\n    return\n  }\n\n  try {\n    uni.showLoading('提交中...')\n\n    // 保存学生资料信息\n    const stMaterialData = {\n      stId: stInfo.value.stId,\n      stDecaType: stInfo.value.stDecaType,\n      studentStatus: formData.value.studentStatus,\n      stFileCreStatus: '1' // 初始状态待上传\n    }\n\n    await saveOrUpdateStMaterial(stMaterialData)\n\n    // 更新学生信息\n    stInfo.value.studentStatus = formData.value.studentStatus\n\n    // 隐藏选择界面，加载材料配置\n    showStudentStatusSelect.value = false\n    await loadData()\n\n    uni.hideLoading()\n    toast.show('学生在读类型设置成功')\n  } catch (error) {\n    console.error('保存学生在读类型失败:', error)\n    uni.hideLoading()\n  }\n}\n\n// 添加文本行\nconst addTextRow = (materialOptId) => {\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n  if (!materialOpt) return\n\n  if (!materialOpt.textDataList) {\n    materialOpt.textDataList = []\n  }\n\n  materialOpt.textDataList.push({ key: '', value: '' })\n  handleTextDataChange(materialOptId)\n}\n\n// 删除文本行\nconst removeTextRow = (materialOptId, index) => {\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n  if (!materialOpt || !materialOpt.textDataList) return\n\n  // 至少保留一行\n  if (materialOpt.textDataList.length <= 1) return\n\n  materialOpt.textDataList.splice(index, 1)\n  handleTextDataChange(materialOptId)\n}\n\n// 文本数据变更处理\nconst handleTextDataChange = (materialOptId) => {\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n  if (!materialOpt) return\n\n  // 更新变更记录\n  if (!materialChanges.value[materialOptId]) {\n    materialChanges.value[materialOptId] = {\n      id: materialOpt.planFileId || null,\n      materialOptId: materialOptId,\n      materialType: materialOpt.materialType,\n      description: materialOpt.description,\n      planType: materialOpt.dataType,\n      fileList: [],\n      textDataList: [{ key: '', value: '' }],\n      isModified: false\n    }\n  }\n\n  materialChanges.value[materialOptId].textDataList = [...(materialOpt.textDataList || [])]\n  materialChanges.value[materialOptId].isModified = true\n}\n\n// 选择文件\nconst chooseFiles = async (materialOptId) => {\n  try {\n    // 在微信浏览器中显示特殊提示和调试信息\n    if (isWechatBrowser.value) {\n    } else {\n    }\n\n    // #ifdef MP-WEIXIN\n    // 微信小程序环境，先让用户选择文件来源\n    const sourceType = await showFileSourceSelector()\n    if (!sourceType) {\n      return // 用户取消选择\n    }\n    // #endif\n\n    const result = await chooseAndUploadFile({\n      count: 10, // 最多选择10个文件\n      maxSize: 10, // 10MB限制\n      allowedTypes: ['image', 'document'], // 支持图片、文档、视频和其他文件类型\n      acceptTypes: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,.csv,image/*', // H5环境下精确指定文件类型\n      // #ifdef MP-WEIXIN\n      fileSourceType: sourceType, // 传递文件来源类型\n      // #endif\n      uploadData: {\n        fileBizType: 'material_upload',\n        materialOptId: materialOptId\n      },\n      onProgress: (progress) => {\n      },\n      onSuccess: (uploadResults) => {\n      }\n    })\n\n    if (result.uploaded && result.uploadResults) {\n      // 上传成功，更新文件列表\n      const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n      if (materialOpt) {\n        if (!materialOpt.fileList) {\n          materialOpt.fileList = []\n        }\n\n        // 验证并添加新上传的文件到列表\n        const validFiles = []\n        const invalidFiles = []\n\n        result.uploadResults.forEach(file => {\n          // 使用原始文件名进行验证\n          const sourceFileName = file.sourceFileName || file.name\n          const filePath = file.filePath\n\n          // 使用增强的文件类型验证，支持从 filePath 获取扩展名\n          if (validateMaterialFileType(sourceFileName, filePath)) {\n            // 保持原始数据结构，不修改任何字段名\n            validFiles.push({\n              ...file, // 保持所有原始字段\n              // 确保有必要的字段用于显示\n              displayName: sourceFileName || file.fileName || file.name || '未知文件'\n            })\n          } else {\n            invalidFiles.push(sourceFileName || filePath || '未知文件')\n          }\n        })\n\n        if (validFiles.length > 0) {\n          materialOpt.fileList.push(...validFiles)\n          // 更新变更记录\n          updateMaterialChange(materialOptId, 'fileList', materialOpt.fileList)\n        }\n\n        // 显示结果提示\n        if (invalidFiles.length > 0) {\n          toast.show(`${invalidFiles.length}个文件格式不支持`)\n        } else {\n          toast.show('文件上传成功')\n        }\n      }\n    }\n  } catch (error) {\n    console.error('文件选择或上传失败:', error)\n  }\n}\n\n// 删除文件\nconst removeFile = (materialOptId, fileIndex) => {\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n  if (!materialOpt || !materialOpt.fileList) return\n\n  materialOpt.fileList.splice(fileIndex, 1)\n  updateMaterialChange(materialOptId, 'fileList', materialOpt.fileList)\n\n  toast.show('文件已删除')\n}\n\n// 更新材料变更记录\nconst updateMaterialChange = (materialOptId, type, data) => {\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\n  if (!materialOpt) return\n\n  if (!materialChanges.value[materialOptId]) {\n    materialChanges.value[materialOptId] = {\n      id: materialOpt.planFileId || null,\n      materialOptId: materialOptId,\n      materialType: materialOpt.materialType,\n      description: materialOpt.description,\n      planType: materialOpt.dataType,\n      fileList: [],\n      textDataList: [{ key: '', value: '' }],\n      isModified: false\n    }\n  }\n\n  if (type === 'fileList') {\n    materialChanges.value[materialOptId].fileList = [...(data || [])]\n  } else if (type === 'textDataList') {\n    materialChanges.value[materialOptId].textDataList = [...(data || [])]\n  }\n\n  materialChanges.value[materialOptId].isModified = true\n}\n\n// 验证必填材料（使用工具类）\nconst validateMaterials = () => {\n  return validateRequiredMaterials(materialOptList.value, materialChanges.value, toast)\n}\n\n// 提交材料数据\nconst submitAgreement = async () => {\n  try {\n    uni.showLoading('保存中...')\n    saveLoading.value = true\n\n    // 构建提交数据 - 使用工具类\n    const submitData = buildSubmitData(materialChanges.value)\n\n    if (submitData.length === 0) {\n      toast.show('没有检测到文件变更')\n      return\n    }\n\n    // 构建请求参数\n    const param = {\n      stId: stInfo.value.stId,\n      stDecaType: stInfo.value.stDecaType,\n      materialPlanId: stInfo.value.materialPlanId,\n      fileOptType: '1',\n      materialPlanFileList: submitData\n    }\n\n    // 调用批量提交接口\n    await uploadMaterialList(param)\n\n    toast.show('保存成功')\n\n    // 延迟返回上一页\n    setTimeout(() => {\n      uni.navigateBack()\n    }, 500)\n\n  } catch (error) {\n    console.error('保存失败:', error)\n  } finally {\n    uni.hideLoading();\n    saveLoading.value = false\n  }\n}\n\n// 保存材料\nconst handleSave = async () => {\n  // 验证必填材料\n  if (!validateMaterials()) {\n    return\n  }\n\n  // 显示确认对话框\n  try {\n    const result = await createModal({\n      title: '确认提交',\n      content: '确定要提交该协议吗？提交后将不可修改',\n      confirmText: '确定',\n      cancelText: '取消'\n    })\n\n    if (result.confirm) {\n      await submitAgreement()\n    }\n  } catch (error) {\n    console.error('确认对话框错误:', error)\n    // 如果对话框出错，直接提交\n    await submitAgreement()\n  }\n}\n\n// 加载材料配置和已上传文件\nconst loadData = async () => {\n  if (!stInfo.value.stId) return\n\n  loading.value = true\n  try {\n    // 根据学生类型和在读状态查询材料配置\n    const [optRes, fileRes] = await Promise.all([\n      getMaterialByTypeAndStatus({\n        stDecaType: stInfo.value.stDecaType,\n        studentStatus: stInfo.value.studentStatus\n      }),\n      selectStMaterialStAndType({\n        stId: stInfo.value.stId,\n        fileOptType: '1',\n        materialPlanId: stInfo.value.materialPlanId\n      })\n    ])\n\n    // 获取材料配置列表\n    const materialOpts = optRes.data || []\n\n    // 获取已上传文件列表\n    const uploadedFiles = fileRes.data || []\n\n    // 创建已上传文件的映射\n    const uploadedFileMap = {}\n    uploadedFiles.forEach(file => {\n      if (!uploadedFileMap[file.materialOptId]) {\n        uploadedFileMap[file.materialOptId] = []\n      }\n\n      // 解析 dataJson 字段\n      let fileList = []\n      if (file.dataJson) {\n        try {\n          if (typeof file.dataJson === 'string') {\n            fileList = JSON.parse(file.dataJson)\n          } else {\n            fileList = file.dataJson\n          }\n        } catch (e) {\n          console.warn('解析文件JSON失败:', e)\n          fileList = []\n        }\n      }\n\n      // 将文件信息添加到映射中\n      if (Array.isArray(fileList) && fileList.length > 0) {\n        uploadedFileMap[file.materialOptId].push({\n          planFileId: file.id,\n          fileList: fileList\n        })\n      }\n    })\n\n    // 合并配置数据和已上传文件数据\n    materialOptList.value = materialOpts.map(opt => {\n      const uploadedData = uploadedFileMap[opt.id]\n      let fileList = []\n      let textDataList = [{ key: '', value: '' }]\n      let planFileId = null\n      let dataType = opt.dataType || '1'\n\n      if (uploadedData && uploadedData.length > 0) {\n        const firstData = uploadedData[0]\n        planFileId = firstData.planFileId\n\n        const dbRecord = uploadedFiles.find(f => f.id === firstData.planFileId)\n        if (dbRecord) {\n          if (dataType === '2') {\n            // 文本类型，解析文本数据\n            try {\n              if (dbRecord.dataJson) {\n                const parsedData = typeof dbRecord.dataJson === 'string'\n                  ? JSON.parse(dbRecord.dataJson)\n                  : dbRecord.dataJson\n\n                if (Array.isArray(parsedData)) {\n                  textDataList = parsedData.length > 0 ? parsedData : [{ key: '', value: '' }]\n                } else if (parsedData && typeof parsedData === 'object') {\n                  textDataList = [parsedData]\n                } else {\n                  textDataList = [{ key: '', value: '' }]\n                }\n              }\n            } catch (e) {\n              console.warn('解析文本数据失败:', e)\n              textDataList = [{ key: '', value: '' }]\n            }\n          } else {\n            // 文件类型，合并所有已上传的文件\n            uploadedData.forEach(data => {\n              fileList = fileList.concat(data.fileList || [])\n            })\n          }\n        }\n      }\n\n      return {\n        ...opt,\n        fileList: fileList,\n        textDataList: textDataList,\n        dataType: dataType,\n        planFileId: planFileId\n      }\n    })\n\n    // 初始化变更记录\n    materialOptList.value.forEach(item => {\n      materialChanges.value[item.id] = {\n        id: item.planFileId || null,\n        materialOptId: item.id,\n        materialType: item.materialType,\n        description: item.description,\n        planType: item.dataType || '1',\n        fileList: item.fileList || [],\n        textDataList: item.textDataList || [{ key: '', value: '' }],\n        isModified: false\n      }\n    })\n  } catch (error) {\n    console.error('加载数据失败:', error)\n    toast.show('加载数据失败')\n  } finally {\n    loading.value = false\n  }\n}\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack()\n}\n\n// 初始化学生在读类型选项数据\nconst initStudentStatusOptions = () => {\n  // 将字典数据转换为uni-data-picker需要的格式\n  studentStatusOptions.value = t_st_study_status.value.map(item => ({\n    text: item.label,\n    value: item.value\n  }))\n}\n\n// 页面初始化\nconst initPage = async (options = {}) => {\n  // 从页面参数获取学生信息\n  stInfo.value = {\n    stId: parseInt(options.stId) || null,\n  }\n\n  // 初始化学生在读类型选项数据\n  initStudentStatusOptions()\n\n  // 检查学生在读类型\n  if (!stInfo.value.studentStatus) {\n    // 先查询学生资料信息\n    try {\n      const res = await getMaterialByStId(stInfo.value.stId)\n      if (res.data && res.data.studentStatus) {\n        stInfo.value.studentStatus = res.data.studentStatus\n        stInfo.value.stDecaType = res.data.stDecaType\n        // 有在读类型，直接加载数据\n        loadData()\n      } else {\n        // 没有在读类型，显示选择界面\n        showStudentStatusSelect.value = true\n      }\n    } catch (error) {\n      console.error('获取学生资料信息失败:', error)\n      // 如果查询失败，也显示选择界面\n      showStudentStatusSelect.value = true\n    }\n  } else {\n    // 已有在读类型，直接加载数据\n    loadData()\n  }\n}\n\n// 使用 onLoad 获取页面参数\nonLoad((options) => {\n  initPage(options)\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 0;\n}\n\n.loading-wrapper {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300rpx;\n}\n\n.material-upload {\n  padding: 30rpx;\n}\n\n// 选择学生在读类型区域\n.select-section {\n  background-color: #fff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  border-radius: 8rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.select-tip {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 30rpx;\n  display: block;\n}\n\n// uni-data-picker 样式\n.picker-wrapper {\n  margin-bottom: 30rpx;\n}\n\n:deep(.uni-data-tree) {\n  padding: 10rpx 0;\n}\n\n:deep(.uni-data-pickerview__item) {\n  line-height: 80rpx;\n  padding: 0 20rpx;\n}\n\n:deep(.uni-data-picker__input-text) {\n  font-size: 28rpx;\n}\n\n:deep(.input-value) {\n  padding: 24rpx 32rpx;\n  background-color: #f8f9fa;\n  border-radius: 8rpx;\n  border: 2rpx solid #e0e0e0;\n  transition: border-color 0.3s ease;\n  min-height: 60rpx;\n\n  &:active {\n    border-color: var(--nuodun-primary-color);\n  }\n}\n\n.select-actions {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-around;\n  padding: 0 20rpx;\n}\n\n.action-btn {\n  width: 45%;\n  height: 88rpx;\n  line-height: 88rpx;\n  font-size: 28rpx;\n  border-radius: 44rpx;\n  margin: 0 10rpx;\n  border: none;\n  text-align: center;\n}\n\n.confirm-btn {\n  background: linear-gradient(135deg, var(--nuodun-primary-color, #1890ff) 0%, var(--nuodun-primary-color-light, #4dabff) 100%);\n  color: #fff;\n  box-shadow: 0 4rpx 12rpx rgba(var(--nuodun-primary-color-rgb, 24, 144, 255), 0.3);\n}\n\n.reject-btn {\n  background-color: #fff;\n  color: #ff3b30;\n  border: 1px solid #ff3b30;\n}\n\n// 空状态\n.empty-section {\n  background-color: #fff;\n  padding: 60rpx 30rpx;\n  margin-bottom: 20rpx;\n  border-radius: 8rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.empty-icon {\n  font-size: 100rpx;\n  margin-bottom: 20rpx;\n  opacity: 0.5;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n// 材料容器 - 整体卡片\n.materials-container {\n  background-color: #fff;\n  border-radius: 8rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 20rpx;\n  overflow: hidden;\n}\n\n// 材料项\n.material-item {\n  padding: 24rpx 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  position: relative;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &.required {\n    border-left: 4rpx solid #ff4757;\n    padding-left: 26rpx;\n  }\n}\n\n.material-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12rpx;\n}\n\n.header-left {\n  flex: 1;\n}\n\n.material-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.material-tags {\n  display: flex;\n  gap: 8rpx;\n  flex-wrap: wrap;\n}\n\n.type-tag,\n.required-tag {\n  padding: 4rpx 8rpx;\n  border-radius: 4rpx;\n  font-size: 18rpx;\n  font-weight: 500;\n}\n\n.file-tag {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n\n.text-tag {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n\n.required-tag {\n  background-color: #ffebee;\n  color: #d32f2f;\n}\n\n.material-desc {\n  font-size: 24rpx;\n  color: #666;\n  line-height: 1.4;\n  margin-bottom: 16rpx;\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.material-content {\n  margin-top: 16rpx;\n}\n\n// 文件容器\n.file-container {\n  width: 100%;\n}\n\n// 文件列表\n.file-list {\n  margin-bottom: 12rpx;\n}\n\n// 材料文件项样式\n.material-file-item {\n  margin-bottom: 12rpx;\n\n  :deep(.file-card) {\n    margin-bottom: 0;\n    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);\n    border: 1rpx solid #f0f0f0;\n    background-color: #fff;\n  }\n\n  :deep(.file-card-btn) {\n    font-size: 24rpx;\n    padding: 6rpx 16rpx;\n  }\n\n  :deep(.file-card-icon) {\n    font-size: 32rpx;\n  }\n\n  :deep(.file-card-name) {\n    font-size: 26rpx;\n  }\n}\n\n// 上传按钮\n.upload-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 16rpx 20rpx;\n  border: 1rpx dashed #ddd;\n  border-radius: 6rpx;\n  background-color: #fafafa;\n  transition: all 0.3s ease;\n\n  &:active {\n    border-color: var(--nuodun-primary-color);\n    background-color: rgba(24, 144, 255, 0.02);\n  }\n}\n\n.upload-icon {\n  font-size: 20rpx;\n  color: var(--nuodun-primary-color);\n  margin-right: 12rpx;\n  font-weight: bold;\n}\n\n.upload-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.upload-text {\n  font-size: 24rpx;\n  color: var(--nuodun-primary-color);\n  margin-bottom: 4rpx;\n}\n\n.upload-tip {\n  font-size: 20rpx;\n  color: #999;\n  line-height: 1.2;\n}\n\n.debug-info {\n  font-size: 18rpx;\n  color: #ff6b35;\n  line-height: 1.2;\n  margin-top: 4rpx;\n  text-align: center;\n}\n\n// 文本容器\n.text-container {\n  width: 100%;\n}\n\n.text-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.text-title {\n  font-size: 24rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.add-btn {\n  color: var(--nuodun-primary-color);\n  font-size: 22rpx;\n  padding: 8rpx 12rpx;\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &:active {\n    color: var(--nuodun-primary-dark);\n    transform: scale(0.95);\n  }\n}\n\n.text-rows {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.text-row {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.text-input {\n  flex: 1;\n  height: 60rpx;\n  padding: 0 16rpx;\n  background-color: #f8f9fa;\n  border: 1rpx solid #e0e0e0;\n  border-radius: 6rpx;\n  font-size: 24rpx;\n  color: #333;\n  transition: all 0.3s ease;\n\n  &.key-input {\n    flex: 1;\n  }\n\n  &.value-input {\n    flex: 2;\n  }\n\n  &:focus {\n    border-color: var(--nuodun-primary-color);\n    background-color: #fff;\n  }\n\n  &::placeholder {\n    color: #999;\n  }\n}\n\n.delete-row-btn {\n  color: #ff4757;\n  font-size: 22rpx;\n  padding: 8rpx 12rpx;\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &.disabled {\n    color: #ccc;\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:not(.disabled):active {\n    color: #ff3742;\n    transform: scale(0.95);\n  }\n}\n\n// 底部操作按钮 - 与agreementSign.vue保持一致\n.action-section {\n  background-color: #fff;\n  padding: 30rpx;\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.action-btn {\n  width: 100%;\n  height: 90rpx;\n  line-height: 90rpx;\n  border-radius: 45rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  border: none;\n  text-align: center;\n  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.save-btn {\n  background: linear-gradient(135deg, var(--nuodun-primary-color) 0%, var(--nuodun-primary-color-light, #4dabff) 100%);\n  color: #fff !important;\n\n  &[disabled] {\n    opacity: 0.6;\n    background: #ccc;\n    color: #999 !important;\n    box-shadow: none;\n  }\n\n  &:not([disabled]):active {\n    transform: scale(0.98);\n  }\n}\n\n.bottom-spacing {\n  height: 130rpx;\n}\n\n// 响应式设计和动画效果\n@media (max-width: 750rpx) {\n  .page-content {\n    padding: 20rpx;\n  }\n\n  .material-item {\n    padding: 24rpx;\n  }\n\n  .material-title {\n    font-size: 28rpx;\n  }\n\n  .text-row {\n    flex-direction: column;\n    gap: 12rpx;\n  }\n\n  .text-input {\n    width: 100%;\n\n    &.key-input,\n    &.value-input {\n      flex: none;\n    }\n  }\n\n  .delete-row-btn {\n    align-self: flex-end;\n    width: 50rpx;\n    height: 50rpx;\n    font-size: 28rpx;\n  }\n\n  .bottom-actions {\n    padding: 15rpx 20rpx;\n  }\n\n  .save-btn {\n    height: 76rpx;\n    font-size: 28rpx;\n  }\n}\n\n// 动画效果\n.material-item {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx);\n  }\n\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// 暗色模式支持\n@media (prefers-color-scheme: dark) {\n  :root {\n    --nuodun-bg-color: #1c1c1e;\n    --nuodun-card-bg: #2c2c2e;\n    --nuodun-text-primary: #ffffff;\n    --nuodun-text-secondary: #aeaeb2;\n    --nuodun-text-placeholder: #8e8e93;\n    --nuodun-border-color: #38383a;\n    --nuodun-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\n  }\n}\n</style>", "import MiniProgramPage from 'D:/work/gitData/nuodun-boot/nuodun-ui/nuodun-admin-app/pages/home/<USER>/materialUpload.vue'\nwx.createPage(MiniProgramPage)"], "names": ["CustomNav", "DocumentPreview", "fileUrl", "config", "proxy", "getCurrentInstance", "toast", "t_st_study_status", "useDict", "loading", "ref", "saveLoading", "loadingText", "contentdown", "contentrefresh", "contentnomore", "stInfo", "stId", "stDecaType", "studentStatus", "materialPlanId", "showStudentStatusSelect", "formData", "studentStatusOptions", "materialOptList", "materialChanges", "isWechatBrowser", "computed", "getValidFileList", "fileList", "Array", "isArray", "filter", "file", "sourceFileName", "fileName", "name", "handleStudentStatusConfirm", "async", "value", "common_vendor", "index", "showLoading", "stMaterialData", "stFileCreStatus", "saveOrUpdateStMaterial", "loadData", "uni", "hideLoading", "show", "error", "__f__", "handleTextDataChange", "materialOptId", "materialOpt", "find", "item", "id", "planFileId", "materialType", "description", "planType", "dataType", "textDataList", "key", "isModified", "chooseFiles", "sourceType", "Promise", "resolve", "showActionSheet", "itemList", "success", "res", "tapIndex", "fail", "result", "chooseAndUploadFile", "count", "maxSize", "allowedTypes", "acceptTypes", "fileSourceType", "uploadData", "fileBizType", "onProgress", "progress", "onSuccess", "uploadResults", "uploaded", "validFiles", "invalidFiles", "for<PERSON>ach", "filePath", "validateMaterialFileType", "push", "displayName", "length", "updateMaterialChange", "removeFile", "fileIndex", "splice", "type", "data", "submitAgreement", "submitData", "buildSubmitData", "param", "fileOptType", "materialPlanFileList", "uploadMaterialList", "setTimeout", "navigateBack", "handleSave", "validateRequiredMaterials", "createModal", "title", "content", "confirmText", "cancelText", "confirm", "optRes", "fileRes", "all", "getMaterialByTypeAndStatus", "selectStMaterialStAndType", "materialOpts", "uploadedFiles", "uploadedFileMap", "dataJson", "JSON", "parse", "e", "map", "opt", "uploadedData", "firstData", "db<PERSON><PERSON><PERSON>", "f", "parsedData", "concat", "initPage", "options", "parseInt", "text", "label", "getMaterialByStId", "onLoad", "event", "fileToDelete", "realIndex", "findIndex", "fileFullPath", "wx", "createPage", "MiniProgramPage"], "mappings": "ooBA6IA,MAAMA,EAAY,IAAW,+CACvBC,EAAkB,IAAW,oFAE7B,MAAAC,EAAUC,EAAAA,YAAOD,SAAW,IAE5BE,MAAEA,GAAUC,uBACZC,EAAQF,EAAME,OAEdC,kBAAEA,GAAsBH,EAAMI,QAAQ,qBAGtCC,EAAUC,EAAGA,KAAC,GACdC,EAAcD,EAAGA,KAAC,GAGlBE,EAAc,CAClBC,YAAa,UACbC,eAAgB,SAChBC,cAAe,WAIXC,EAASN,EAAAA,IAAI,CACjBO,KAAM,KACNC,WAAY,GACZC,cAAe,GACfC,eAAgB,OAIZC,EAA0BX,EAAGA,KAAC,GAG9BY,EAAWZ,EAAAA,IAAI,CACnBS,cAAe,KAIXI,EAAuBb,EAAGA,IAAC,IAE3Bc,EAAkBd,EAAGA,IAAC,IAEtBe,EAAkBf,EAAGA,IAAC,IAItBgB,EAAkBC,EAAQA,UAAC,KAMxB,IAKHC,EAAoBC,IACxB,IAAKA,IAAaC,MAAMC,QAAQF,GAAW,MAAO,GAE3C,OADYA,EAASG,QAAeC,GAAAA,IAASA,EAAKC,gBAAkBD,EAAKE,UAAYF,EAAKG,SAgD7FC,EAA6BC,UAC7B,GAAChB,EAASiB,MAAMpB,cAKhB,IACCqB,EAAAC,MAACC,YAAY,UAGhB,MAAMC,EAAiB,CACrB1B,KAAMD,EAAOuB,MAAMtB,KACnBC,WAAYF,EAAOuB,MAAMrB,WACzBC,cAAeG,EAASiB,MAAMpB,cAC9ByB,gBAAiB,WAGbC,EAAAA,uBAAuBF,GAGtB3B,EAAAuB,MAAMpB,cAAgBG,EAASiB,MAAMpB,cAG5CE,EAAwBkB,OAAQ,QAC1BO,IAENC,EAAAA,MAAIC,cACJ1C,EAAM2C,KAAK,aACZ,OAAQC,GACPH,EAAAA,MAAcI,MAAA,QAAA,gDAAA,cAAeD,GAC7BH,EAAAA,MAAIC,aACL,MA7BC1C,EAAM2C,KAAK,cA0DTG,EAAwBC,IAC5B,MAAMC,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,IAGA7B,EAAgBc,MAAMc,KACT5B,EAAAc,MAAMc,GAAiB,CACrCI,GAAIH,EAAYI,YAAc,KAC9BL,gBACAM,aAAcL,EAAYK,aAC1BC,YAAaN,EAAYM,YACzBC,SAAUP,EAAYQ,SACtBjC,SAAU,GACVkC,aAAc,CAAC,CAAEC,IAAK,GAAIzB,MAAO,KACjC0B,YAAY,IAIAxC,EAAAc,MAAMc,GAAeU,aAAe,IAAKT,EAAYS,cAAgB,IACrEtC,EAAAc,MAAMc,GAAeY,YAAa,IAI9CC,EAAc5B,MAAOe,IACrB,IAEE3B,EAAgBa,MAMd,MAAA4B,QAtID,IAAIC,SAASC,IAClBtB,EAAAA,MAAIuB,gBAAgB,CAClBC,SAAU,CAAC,eAAgB,mBAC3BC,QAAUC,IACa,IAAjBA,EAAIC,SACNL,EAAQ,SACkB,IAAjBI,EAAIC,UACbL,EAAQ,SAGZM,KAAM,KACJN,EAAQ,YA4HZ,IAAKF,EACH,OAII,MAAAS,QAAeC,sBAAoB,CACvCC,MAAO,GACPC,QAAS,GACTC,aAAc,CAAC,QAAS,YACxBC,YAAa,6TAEbC,eAAgBf,EAEhBgB,WAAY,CACVC,YAAa,kBACb/B,iBAEFgC,WAAaC,MAEbC,UAAYC,QAIV,GAAAZ,EAAOa,UAAYb,EAAOY,cAAe,CAE3C,MAAMlC,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IACnE,GAAIC,EAAa,CACVA,EAAYzB,WACfyB,EAAYzB,SAAW,IAIzB,MAAM6D,EAAa,GACbC,EAAe,GAEdf,EAAAY,cAAcI,SAAgB3D,IAE7B,MAAAC,EAAiBD,EAAKC,gBAAkBD,EAAKG,KAC7CyD,EAAW5D,EAAK4D,SAGlBC,EAAwBA,yBAAC5D,EAAgB2D,GAE3CH,EAAWK,KAAK,IACX9D,EAEH+D,YAAa9D,GAAkBD,EAAKE,UAAYF,EAAKG,MAAQ,SAGlDuD,EAAAI,KAAK7D,GAAkB2D,GAAY,WAIhDH,EAAWO,OAAS,IACV3C,EAAAzB,SAASkE,QAAQL,GAERQ,EAAA7C,EAAe,WAAYC,EAAYzB,WAI1D8D,EAAaM,OAAS,EACxB3F,EAAM2C,KAAK,GAAG0C,EAAaM,kBAE3B3F,EAAM2C,KAAK,SAEd,CACF,CACF,OAAQC,GACPH,EAAAA,MAAAI,MAAA,QAAA,gDAAc,aAAcD,EAC7B,GAIGiD,EAAa,CAAC9C,EAAe+C,KACjC,MAAM9C,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,GAAgBA,EAAYzB,WAErByB,EAAAzB,SAASwE,OAAOD,EAAW,GAClBF,EAAA7C,EAAe,WAAYC,EAAYzB,UAE5DvB,EAAM2C,KAAK,WAIPiD,EAAuB,CAAC7C,EAAeiD,EAAMC,KACjD,MAAMjD,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,IAEA7B,EAAgBc,MAAMc,KACT5B,EAAAc,MAAMc,GAAiB,CACrCI,GAAIH,EAAYI,YAAc,KAC9BL,gBACAM,aAAcL,EAAYK,aAC1BC,YAAaN,EAAYM,YACzBC,SAAUP,EAAYQ,SACtBjC,SAAU,GACVkC,aAAc,CAAC,CAAEC,IAAK,GAAIzB,MAAO,KACjC0B,YAAY,IAIH,aAATqC,EACc7E,EAAAc,MAAMc,GAAexB,SAAW,IAAK0E,GAAQ,IAC3C,iBAATD,IACO7E,EAAAc,MAAMc,GAAeU,aAAe,IAAKwC,GAAQ,KAGnD9E,EAAAc,MAAMc,GAAeY,YAAa,IAS9CuC,EAAkBlE,UAClB,IACCE,EAAAC,MAACC,YAAY,UAChB/B,EAAY4B,OAAQ,EAGpB,MAAMkE,EAAaC,EAAAA,gBAAgBjF,EAAgBc,OAE/C,GAAsB,IAAtBkE,EAAWR,OAEb,YADA3F,EAAM2C,KAAK,aAKb,MAAM0D,EAAQ,CACZ1F,KAAMD,EAAOuB,MAAMtB,KACnBC,WAAYF,EAAOuB,MAAMrB,WACzBE,eAAgBJ,EAAOuB,MAAMnB,eAC7BwF,YAAa,IACbC,qBAAsBJ,SAIlBK,EAAAA,mBAAmBH,GAEzBrG,EAAM2C,KAAK,QAGX8D,YAAW,KACThE,EAAAA,MAAIiE,iBACH,IAEJ,OAAQ9D,GACPH,EAAAA,oEAAc,QAASG,EAC3B,CAAY,QACRH,EAAGN,MAACO,cACJrC,EAAY4B,OAAQ,CACrB,GAIG0E,EAAa3E,UAEb,GA/CG4E,EAAAA,0BAA0B1F,EAAgBe,MAAOd,EAAgBc,MAAOjC,GAoD3E,WACmB6G,cAAY,CAC/BC,MAAO,OACPC,QAAS,qBACTC,YAAa,KACbC,WAAY,QAGHC,eACHhB,GAET,OAAQtD,GACPH,EAAAA,MAAAI,MAAA,QAAA,gDAAc,WAAYD,SAEpBsD,GACP,GAIG1D,EAAWR,UACX,GAACtB,EAAOuB,MAAMtB,KAAd,CAEJR,EAAQ8B,OAAQ,EACZ,IAEF,MAAOkF,EAAQC,SAAiBtD,QAAQuD,IAAI,CAC1CC,6BAA2B,CACzB1G,WAAYF,EAAOuB,MAAMrB,WACzBC,cAAeH,EAAOuB,MAAMpB,gBAE9B0G,4BAA0B,CACxB5G,KAAMD,EAAOuB,MAAMtB,KACnB2F,YAAa,IACbxF,eAAgBJ,EAAOuB,MAAMnB,mBAK3B0G,EAAeL,EAAOlB,MAAQ,GAG9BwB,EAAgBL,EAAQnB,MAAQ,GAGhCyB,EAAkB,CAAE,EAC1BD,EAAcnC,SAAgB3D,IACvB+F,EAAgB/F,EAAKoB,iBACR2E,EAAA/F,EAAKoB,eAAiB,IAIxC,IAAIxB,EAAW,GACf,GAAII,EAAKgG,SACH,IAEWpG,EADgB,iBAAlBI,EAAKgG,SACHC,KAAKC,MAAMlG,EAAKgG,UAEhBhG,EAAKgG,QAEnB,OAAQG,GACPrF,EAAAA,MAAaI,MAAA,OAAA,gDAAA,cAAeiF,GAC5BvG,EAAW,EACZ,CAICC,MAAMC,QAAQF,IAAaA,EAASoE,OAAS,GAC/B+B,EAAA/F,EAAKoB,eAAe0C,KAAK,CACvCrC,WAAYzB,EAAKwB,GACjB5B,gBAMUL,EAAAe,MAAQuF,EAAaO,KAAWC,IACxC,MAAAC,EAAeP,EAAgBM,EAAI7E,IACzC,IAAI5B,EAAW,GACXkC,EAAe,CAAC,CAAEC,IAAK,GAAIzB,MAAO,KAClCmB,EAAa,KACbI,EAAWwE,EAAIxE,UAAY,IAE3B,GAAAyE,GAAgBA,EAAatC,OAAS,EAAG,CACrC,MAAAuC,EAAYD,EAAa,GAC/B7E,EAAa8E,EAAU9E,WAEvB,MAAM+E,EAAWV,EAAcxE,SAAUmF,EAAEjF,KAAO+E,EAAU9E,aAC5D,GAAI+E,EACF,GAAiB,MAAb3E,EAEE,IACF,GAAI2E,EAASR,SAAU,CACf,MAAAU,EAA0C,iBAAtBF,EAASR,SAC/BC,KAAKC,MAAMM,EAASR,UACpBQ,EAASR,SAGIlE,EADbjC,MAAMC,QAAQ4G,GACDA,EAAW1C,OAAS,EAAI0C,EAAa,CAAC,CAAE3E,IAAK,GAAIzB,MAAO,KAC9DoG,GAAoC,iBAAfA,EACf,CAACA,GAED,CAAC,CAAE3E,IAAK,GAAIzB,MAAO,IAErC,CACF,OAAQ6F,GACPrF,EAAAA,MAAAI,MAAA,OAAA,gDAAa,YAAaiF,GAC1BrE,EAAe,CAAC,CAAEC,IAAK,GAAIzB,MAAO,IACnC,MAGDgG,EAAa3C,SAAgBW,IAC3B1E,EAAWA,EAAS+G,OAAOrC,EAAK1E,UAAY,MAInD,CAEM,MAAA,IACFyG,EACHzG,WACAkC,eACAD,WACAJ,iBAKYlC,EAAAe,MAAMqD,SAAgBpC,IACpB/B,EAAAc,MAAMiB,EAAKC,IAAM,CAC/BA,GAAID,EAAKE,YAAc,KACvBL,cAAeG,EAAKC,GACpBE,aAAcH,EAAKG,aACnBC,YAAaJ,EAAKI,YAClBC,SAAUL,EAAKM,UAAY,IAC3BjC,SAAU2B,EAAK3B,UAAY,GAC3BkC,aAAcP,EAAKO,cAAgB,CAAC,CAAEC,IAAK,GAAIzB,MAAO,KACtD0B,YAAY,KAGjB,OAAQf,GACPH,EAAAA,MAAAI,MAAA,QAAA,gDAAc,UAAWD,GACzB5C,EAAM2C,KAAK,SACf,CAAY,QACRxC,EAAQ8B,OAAQ,CACjB,CA5HuB,GA8IpBsG,EAAWvG,MAAOwG,EAAU,MAU5B,GARJ9H,EAAOuB,MAAQ,CACbtB,KAAM8H,SAASD,EAAQ7H,OAAS,MAVlCM,EAAqBgB,MAAQhC,EAAkBgC,MAAM8F,KAAa7E,IAAA,CAChEwF,KAAMxF,EAAKyF,MACX1G,MAAOiB,EAAKjB,UAeTvB,EAAOuB,MAAMpB,cAoBN2B,SAlBN,IACF,MAAM2B,QAAYyE,EAAAA,kBAAkBlI,EAAOuB,MAAMtB,MAC7CwD,EAAI8B,MAAQ9B,EAAI8B,KAAKpF,eAChBH,EAAAuB,MAAMpB,cAAgBsD,EAAI8B,KAAKpF,cAC/BH,EAAAuB,MAAMrB,WAAauD,EAAI8B,KAAKrF,WAEzB4B,KAGVzB,EAAwBkB,OAAQ,CAEnC,OAAQW,GACPH,EAAAA,MAAcI,MAAA,QAAA,gDAAA,cAAeD,GAE7B7B,EAAwBkB,OAAQ,CACjC,UAQCC,EAAA2G,QAAEL,IACND,EAASC,4oBA/dc,EAACzF,EAAe+C,EAAWgD,KAElD,MAAM9F,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC/D,IAACC,IAAgBA,EAAYzB,SAAU,OAErC,MACAwH,EADazH,EAAiB0B,EAAYzB,UAChBuE,GAG1BkD,EAAYhG,EAAYzB,SAAS0H,WAAUtH,GAC/CA,IAASoH,GACRpH,EAAKC,iBAAmBmH,EAAanH,gBAAkBD,EAAKuH,eAAiBH,EAAaG,cAC1FvH,EAAKE,WAAakH,EAAalH,UAAYF,EAAK/B,UAAYmJ,EAAanJ,SACzE+B,EAAKC,iBAAmBkH,EAAMjH,UAAYF,EAAKuH,eAAiBJ,EAAMvD,YAGnD,IAAlByD,EACFnD,EAAW9C,EAAeiG,GAE1BvG,EAAAA,MAAAI,MAAA,OAAA,gDAAa,+bAwCE,CAACE,IAClB,MAAMC,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,IAEAA,EAAYS,eACfT,EAAYS,aAAe,IAG7BT,EAAYS,aAAagC,KAAK,CAAE/B,IAAK,GAAIzB,MAAO,KAChDa,EAAqBC,sLAID,EAACA,EAAeZ,KACpC,MAAMa,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,GAAgBA,EAAYS,eAG7BT,EAAYS,aAAakC,QAAU,IAE3B3C,EAAAS,aAAasC,OAAO5D,EAAO,GACvCW,EAAqBC,qTC/SvBoG,GAAGC,WAAWC"}