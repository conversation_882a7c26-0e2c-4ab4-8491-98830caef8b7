<template>
  <div class="unified-step-form">
    <!-- 步骤特定组件 -->
    <div class="step-specific-form">
      <!-- 文书环节 -->
      <WritingProcessStep 
        v-if="stepCode === 'WRITING_PROCESS'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 文书定稿 -->
      <WritingFinalizationStep 
        v-else-if="stepCode === 'WRITING_FINALIZATION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 学校申请填写 -->
      <ApplicationFillingStep 
        v-else-if="stepCode === 'APPLICATION_FILLING'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 面试邀请通知 -->
      <InterviewInvitationStep 
        v-else-if="stepCode === 'INTERVIEW_INVITATION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 面试培训安排 -->
      <InterviewTrainingStep 
        v-else-if="stepCode === 'INTERVIEW_TRAINING'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 参加面试 -->
      <AttendInterviewStep 
        v-else-if="stepCode === 'ATTEND_INTERVIEW'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 录取通知 -->
      <AdmissionNoticeStep 
        v-else-if="stepCode === 'ADMISSION_NOTICE'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 接受录取&留位费 -->
      <AcceptOfferDepositStep 
        v-else-if="stepCode === 'ACCEPT_OFFER_DEPOSIT'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 签证申请提交 -->
      <VisaPreparationStep 
        v-else-if="stepCode === 'VISA_PREPARATION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 条件录取材料递交 -->
      <ConditionalMaterialsStep 
        v-else-if="stepCode === 'CONDITIONAL_MATERIALS'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 签证审批&正式录取 -->
      <VisaFormalOfferStep 
        v-else-if="stepCode === 'VISA_FORMAL_OFFER'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 住宿申请&注册 -->
      <AccommodationRegistrationStep 
        v-else-if="stepCode === 'ACCOMMODATION_REGISTRATION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 默认提示 -->
      <div v-else class="no-form-tip">
        <el-alert
          title="暂未配置该步骤的专用表单"
          type="info"
          :description="`步骤代码: ${stepCode}`"
          show-icon
          :closable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onBeforeUnmount, nextTick, computed } from 'vue'
import WritingProcessStep from './stepForms/WritingProcessStep.vue'
import WritingFinalizationStep from './stepForms/WritingFinalizationStep.vue'
import ApplicationFillingStep from './stepForms/ApplicationFillingStep.vue'
import InterviewInvitationStep from './stepForms/InterviewInvitationStep.vue'
import InterviewTrainingStep from './stepForms/InterviewTrainingStep.vue'
import AttendInterviewStep from './stepForms/AttendInterviewStep.vue'
import AdmissionNoticeStep from './stepForms/AdmissionNoticeStep.vue'
import AcceptOfferDepositStep from './stepForms/AcceptOfferDepositStep.vue'
import VisaPreparationStep from './stepForms/VisaPreparationStep.vue'
import ConditionalMaterialsStep from './stepForms/ConditionalMaterialsStep.vue'
import VisaFormalOfferStep from './stepForms/VisaFormalOfferStep.vue'
import AccommodationRegistrationStep from './stepForms/AccommodationRegistrationStep.vue'

const props = defineProps({
  progressData: {
    type: Object,
    required: true
  },
  stepName: {
    type: String,
    default: '当前步骤'
  },
  stepCode: {
    type: String,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'dataChange'])

// 步骤特定数据
const stepSpecificData = ref({})

// 处理步骤特定数据变化
const handleStepDataChange = (data) => {
  stepSpecificData.value = data
  emit('dataChange', data)
}

// 获取表单数据
const getFormData = () => {
  return {
    stepSpecificData: stepSpecificData.value,
    operationData: JSON.stringify(stepSpecificData.value)
  }
}

// 验证表单
const validateForm = async () => {
  // 由各个子组件内部处理验证
  return true
}

// 重置表单
const resetForm = () => {
  stepSpecificData.value = {}
}

// 初始化数据
const initData = () => {
  if (props.progressData) {
    const fieldMap = getStepDataFieldMap()
    if (fieldMap.data) {
      try {
        const data = JSON.parse(props.progressData[fieldMap.data] || '{}')
        stepSpecificData.value = data
      } catch (error) {
        stepSpecificData.value = {}
      }
    }
  }
}

// 获取步骤数据字段映射
const getStepDataFieldMap = () => {
  const maps = {
    'WRITING_PROCESS': { data: 'stepWritingProcessData' },
    'WRITING_FINALIZATION': { data: 'stepWritingFinalizationData' },
    'APPLICATION_FILLING': { data: 'stepApplicationFillingData' },
    'INTERVIEW_INVITATION': { data: 'stepInterviewInvitationData' },
    'INTERVIEW_TRAINING': { data: 'stepInterviewTrainingData' },
    'ATTEND_INTERVIEW': { data: 'stepAttendInterviewData' },
    'ADMISSION_NOTICE': { data: 'stepAdmissionNoticeData' },
    'ACCEPT_OFFER_DEPOSIT': { data: 'stepAcceptOfferDepositData' },
    'VISA_PREPARATION': { data: 'stepVisaPreparationData' },
    'CONDITIONAL_MATERIALS': { data: 'stepConditionalMaterialsData' },
    'VISA_FORMAL_OFFER': { data: 'stepVisaFormalOfferData' },
    'ACCOMMODATION_REGISTRATION': { data: 'stepAccommodationRegistrationData' }
  }
  return maps[props.stepCode] || { data: '' }
}

// 监听步骤代码变化
watch(() => props.stepCode, () => {
  initData()
}, { immediate: true })

// 组件销毁前的清理
onBeforeUnmount(() => {
  // 清理逻辑
})

// 暴露方法
defineExpose({
  getFormData,
  resetForm,
  validateForm
})
</script>

<style scoped lang="scss">
.unified-step-form {
  .step-specific-form {
    margin-bottom: 20px;
  }

  .no-form-tip {
    padding: 20px;
    text-align: center;
  }
}
</style> 