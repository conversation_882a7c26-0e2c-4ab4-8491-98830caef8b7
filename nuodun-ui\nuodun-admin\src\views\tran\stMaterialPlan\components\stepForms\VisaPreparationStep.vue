<template>
  <div class="visa-preparation-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>签证申请提交详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否需要签证申请：</label>
            <span>{{ stepData.visaRequired ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.visaRequired" class="info-item">
            <label>表格填写状态：</label>
            <span>{{ stepData.formsFilled || '暂未填写' }}</span>
          </div>
          <div v-if="stepData.visaRequired" class="info-item">
            <label>材料文件列表：</label>
            <span>{{ Array.isArray(stepData.materialsList) ? stepData.materialsList.join(', ') : (stepData.materialsList || '暂无材料') }}</span>
          </div>
          <div v-if="stepData.visaRequired" class="info-item">
            <label>是否缴纳签证行政费：</label>
            <span>{{ stepData.visaFeePaid ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.visaRequired && stepData.visaFeePaid" class="info-item">
            <label>签证行政费金额：</label>
            <span>{{ stepData.visaFeeAmount || '暂未填写' }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否需要签证申请" prop="visaRequired">
          <el-radio-group v-model="stepData.visaRequired">
            <el-radio :label="true">需要申请</el-radio>
            <el-radio :label="false">不需要申请</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="stepData.visaRequired">
          <el-form-item label="表格填写状态" prop="formsFilled">
            <el-input
              v-model="stepData.formsFilled"
              placeholder="填写状态（ID995A、同意书等）"
            />
          </el-form-item>

          <el-form-item label="材料文件列表" prop="materialsList">
            <el-input
              v-model="materialsListStr"
              type="textarea"
              :rows="2"
              placeholder="请输入材料文件列表，用逗号分隔"
            />
          </el-form-item>

          <el-form-item label="是否缴纳签证行政费" prop="visaFeePaid">
            <el-radio-group v-model="stepData.visaFeePaid">
              <el-radio :label="true">已缴费</el-radio>
              <el-radio :label="false">未缴费</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="stepData.visaFeePaid">
            <el-form-item label="签证行政费金额" prop="visaFeeAmount">
              <el-input-number
                v-model="stepData.visaFeeAmount"
                :min="0"
                :precision="2"
                placeholder="请输入金额"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="缴纳留位费后，进入签证申请环节，需填写ID995A、同意书等表格，收集存款证明等材料"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  visaRequired: false,
  formsFilled: '',
  materialsList: [],
  visaFeePaid: false,
  visaFeeAmount: 0,
  remark: ''
})

// 材料列表字符串
const materialsListStr = computed({
  get: () => Array.isArray(stepData.materialsList) ? stepData.materialsList.join(', ') : stepData.materialsList || '',
  set: (value) => {
    stepData.materialsList = value ? value.split(',').map(item => item.trim()).filter(item => item) : []
  }
})

// 验证规则
const rules = {
  visaRequired: [
    { required: true, message: '请选择是否需要签证申请', trigger: 'change' }
  ],
  visaFeePaid: [
    { required: true, message: '请选择是否缴纳签证行政费', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.visaRequired && value === undefined) {
        callback(new Error('请选择是否缴纳签证行政费'))
      } else {
        callback()
      }
    }}
  ],
  visaFeeAmount: [
    { required: true, message: '请输入签证行政费金额', trigger: 'blur', validator: (rule, value, callback) => {
      if (stepData.visaRequired && stepData.visaFeePaid && (!value || value <= 0)) {
        callback(new Error('请输入有效的签证行政费金额'))
      } else {
        callback()
      }
    }}
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    visaRequired: false,
    formsFilled: '',
    materialsList: [],
    visaFeePaid: false,
    visaFeeAmount: 0,
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    visaRequired: false,
    formsFilled: '',
    materialsList: [],
    visaFeePaid: false,
    visaFeeAmount: 0,
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.visa-preparation-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 