"use strict";const e=require("./vendor.js"),o=require("./toast.js"),n=require("./request.js");function s(n){return new Promise(((s,i)=>{let t=n.sourceType;"album"===n.fileSourceType?t=["album"]:"chat"===n.fileSourceType&&(t=["album","camera"]),e.index.chooseImage({count:n.count,sizeType:["compressed"],sourceType:t,success:e=>{const t=e.tempFilePaths.map(((o,n)=>{const s=e.tempFiles[n];return{name:s.name||`image_${Date.now()}_${n}.jpg`,size:s.size||0,type:s.type||"image/jpeg",path:o}})).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==t.length?s(t):i(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:607","选择图片失败:",o),i(new Error("选择图片失败"))}})}))}function i(n){return new Promise(((s,i)=>{e.index.chooseMessageFile({count:n.count,type:"all",success:e=>{const t=e.tempFiles.map((e=>({name:e.name,size:e.size,type:e.type||"",path:e.path}))).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==t.length?s(t):i(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:646","选择文件失败:",o),i(new Error("选择文件失败"))}})}))}function t(n){return new Promise(((s,i)=>{"function"==typeof e.index.chooseFile?e.index.chooseFile({count:n.count,type:"file",success:e=>{const t=e.tempFiles.map((e=>({name:e.name,size:e.size,type:e.type||"",path:e.path}))).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==t.length?s(t):i(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:687","chooseFile失败，回退到chooseImage:",o),l(n,s,i)}}):l(n,s,i)}))}function l(n,s,i){e.index.chooseImage({count:n.count,sizeType:["compressed","original"],sourceType:["album"],success:e=>{const t=e.tempFilePaths.map(((o,n)=>{const s=e.tempFiles[n];return{name:s.name||`image_${Date.now()}_${n}.jpg`,size:s.size||0,type:s.type||"image/jpeg",path:o}})).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==t.length?s(t):i(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:739","选择图片失败:",o),i(new Error("选择文件失败"))}})}exports.chooseAndUploadFile=function(o={}){const l={count:1,maxSize:10,allowedTypes:["image","file"],acceptTypes:null,sourceType:["album","camera"],fileSourceType:"chat",uploadData:{fileBizType:"common_file_upload"},uploadUrl:"/system/fileRecord/uploadByFile",onChoose:null,onProgress:null,onSuccess:null,onFail:null,onComplete:null,autoUpload:!0,showLoading:!0,...o};return new Promise(((o,r)=>{let a=null;if(l.allowedTypes.includes("image"),l.allowedTypes.some((e=>"image"!==e)),a="album"===l.fileSourceType?s:"files"===l.fileSourceType?t:i,!a){const e=new Error("当前平台不支持文件选择");return l.onFail&&l.onFail(e),l.onComplete&&l.onComplete(!1),void r(e)}a(l).then((s=>{if(l.onChoose){if(!1===l.onChoose(s))return void o({files:s,uploaded:!1})}l.autoUpload?function(o,s){return new Promise(((i,t)=>{if(!o||0===o.length)return void t(new Error("没有文件需要上传"));const l=o.map(((o,i)=>function(o,s,i){return new Promise(((t,l)=>{s.showLoading&&e.index.showLoading({title:`上传中... (${i+1})`});const r={url:s.uploadUrl,filePath:o.path,name:"file",formData:s.uploadData,success:n=>{if(s.showLoading&&e.index.hideLoading(),200===n.code&&n.data){const e={...o,...n.data,uploaded:!0,uploadTime:(new Date).getTime()};s.onProgress&&s.onProgress({file:e,index:i,progress:100,status:"success"}),t(e)}else{const e=new Error(n.msg||"上传失败");e.file=o,e.index=i,s.onProgress&&s.onProgress({file:o,index:i,progress:0,status:"error",error:e}),l(e)}},fail:n=>{s.showLoading&&e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:834","上传文件失败:",n);const t=new Error("上传失败");t.file=o,t.index=i,t.originalError=n,s.onProgress&&s.onProgress({file:o,index:i,progress:0,status:"error",error:t}),l(t)}},a=n.upload(r);a&&a.onProgressUpdate&&s.onProgress&&a.onProgressUpdate((e=>{s.onProgress({file:o,index:i,progress:e.progress,status:"uploading"})}))}))}(o,s,i)));Promise.all(l).then((e=>{s.onSuccess&&s.onSuccess(e),s.onComplete&&s.onComplete(!0),i(e)})).catch((e=>{s.onFail&&s.onFail(e),s.onComplete&&s.onComplete(!1),t(e)}))}))}(s,l).then((e=>{o({files:s,uploadResults:e,uploaded:!0})})).catch((e=>{l.onFail&&l.onFail(e),l.onComplete&&l.onComplete(!1),r(e)})):o({files:s,uploaded:!1})})).catch((e=>{l.onFail&&l.onFail(e),l.onComplete&&l.onComplete(!1),r(e)}))}))},exports.downloadAndOpenFile=function(n,s={}){const i={onSuccess:null,onFail:null,onComplete:null,loadingText:"文件下载中...",successText:"文件已保存",failText:"文件下载失败",autoOpen:!0,fileName:"",...s};return new Promise(((s,t)=>{if(!n){const e=new Error("没有可下载的文件");return i.onFail&&i.onFail(e),i.onComplete&&i.onComplete(!1),void t(e)}e.index.showLoading(i.loadingText),e.index.downloadFile({url:n,success:n=>{200===n.statusCode?e.index.saveFile({tempFilePath:n.tempFilePath,success:n=>{e.index.hideLoading(),i.successText&&o.toast.show(i.successText),i.autoOpen?e.index.openDocument({filePath:n.savedFilePath,success:()=>{i.onSuccess&&i.onSuccess(n),i.onComplete&&i.onComplete(!0),s(n)},fail:e=>{o.toast.show("无法打开此类型文件"),i.onSuccess&&i.onSuccess(n),i.onComplete&&i.onComplete(!0),s(n)}}):(i.onSuccess&&i.onSuccess(n),i.onComplete&&i.onComplete(!0),s(n))},fail:n=>{e.index.hideLoading(),o.toast.show("文件保存失败"),i.onFail&&i.onFail(n),i.onComplete&&i.onComplete(!1),t(n)}}):(e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:1040","文件下载失败",n),o.toast.show(i.failText),i.onFail&&i.onFail(n),i.onComplete&&i.onComplete(!1),t(new Error(i.failText)))},fail:n=>{e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:1051","文件下载失败",n),o.toast.show(i.failText),i.onFail&&i.onFail(n),i.onComplete&&i.onComplete(!1),t(n)}})}))},exports.getFileType=function(e){if(!e)return"file";const o=e.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp"].includes(o)?"image":["mp4","avi","mov","wmv","flv","3gp"].includes(o)?"video":["pdf","doc","docx","xls","xlsx","ppt","pptx","txt"].includes(o)?"document":"file"},exports.getFileTypeIcon=function(e){if(!e)return"📄";const o=e.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp"].includes(o)?"🖼️":"pdf"===o?"📕":["doc","docx"].includes(o)?"📘":["xls","xlsx"].includes(o)?"📗":["ppt","pptx"].includes(o)?"📙":"📄"},exports.validateMaterialFileType=function(e,o=null){const n=function(e,o=null){if(e&&e.includes("."))return e.toLowerCase().split(".").pop();if(o&&o.includes("."))return o.toLowerCase().split(".").pop();return null}(e,o);return!!n&&["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx","xls","xlsx","ppt","pptx"].includes(n)};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/fileUtils.js.map
