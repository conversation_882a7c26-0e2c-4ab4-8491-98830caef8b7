<template>
  <div class="attend-interview-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>参加面试详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否参加面试：</label>
            <span>{{ stepData.interviewAttended ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.interviewAttended && stepData.interviewLink" class="info-item">
            <label>面试链接：</label>
            <span>{{ stepData.interviewLink }}</span>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否参加面试" prop="interviewAttended">
          <el-radio-group v-model="stepData.interviewAttended">
            <el-radio :label="true">已参加</el-radio>
            <el-radio :label="false">未参加</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="stepData.interviewAttended">
          <el-form-item label="面试链接" prop="interviewLink">
            <el-input
              v-model="stepData.interviewLink"
              placeholder="请输入面试链接（如线上面试）"
            />
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="学生参加学校面试，面试成功则收到录取，失败则终止该学校申请"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  interviewAttended: false,
  interviewLink: '',
  remark: ''
})

// 验证规则
const rules = {
  interviewAttended: [
    { required: true, message: '请选择是否参加面试', trigger: 'change' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    interviewAttended: false,
    interviewLink: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    interviewAttended: false,
    interviewLink: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.attend-interview-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 