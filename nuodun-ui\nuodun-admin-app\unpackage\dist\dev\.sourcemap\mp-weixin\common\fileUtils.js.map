{"version": 3, "file": "fileUtils.js", "sources": ["common/fileUtils.js"], "sourcesContent": ["import {toast} from \"@/common/toast.js\"\nimport {upload} from \"@/common/request\"\n\n/**\n * 通用文件选择和上传工具类\n * 支持H5、APP、小程序多端\n */\n\n/**\n * 通用文件选择和上传方法\n * 支持H5、APP、小程序多端\n * @param {Object} options 配置选项\n * @param {Number} options.count 选择文件数量，默认1\n * @param {Number} options.maxSize 文件大小限制(MB)，默认10MB\n * @param {Array} options.allowedTypes 允许的文件类型，如['image', 'video', 'file', 'document']，默认['image', 'file']\n * @param {String} options.acceptTypes H5环境下自定义accept属性，如'image/*,.pdf,.doc,.docx'\n * @param {String} options.sourceType 选择来源，如['album', 'camera']，默认['album', 'camera']\n * @param {String} options.fileSourceType 微信小程序文件来源类型，'album'从相册选择，'chat'从聊天记录选择\n * @param {Object} options.uploadData 上传时的额外数据，默认{ fileBizType: 'common_file_upload' }\n * @param {String} options.uploadUrl 上传接口地址，默认'/system/fileRecord/uploadByFile'\n * @param {Function} options.onChoose 选择文件后的回调，可用于预处理\n * @param {Function} options.onProgress 上传进度回调\n * @param {Function} options.onSuccess 上传成功回调\n * @param {Function} options.onFail 上传失败回调\n * @param {Function} options.onComplete 上传完成回调（无论成功失败）\n * @param {Boolean} options.autoUpload 是否选择后自动上传，默认true\n * @param {Boolean} options.showLoading 是否显示加载提示，默认true\n * @returns {Promise} 返回Promise，resolve时包含选择的文件信息和上传结果\n */\nexport function chooseAndUploadFile(options = {}) {\n    // 默认配置\n    const defaultOptions = {\n        count: 1,\n        maxSize: 10, // MB\n        allowedTypes: ['image', 'file'],\n        acceptTypes: null, // H5环境下自定义accept属性\n        sourceType: ['album', 'camera'],\n        fileSourceType: 'chat', // 微信小程序默认从聊天记录选择\n        uploadData: {fileBizType: 'common_file_upload'},\n        uploadUrl: '/system/fileRecord/uploadByFile',\n        onChoose: null,\n        onProgress: null,\n        onSuccess: null,\n        onFail: null,\n        onComplete: null,\n        autoUpload: true,\n        showLoading: true\n    };\n\n    // 合并配置\n    const opts = {...defaultOptions, ...options};\n\n    return new Promise((resolve, reject) => {\n        // 根据平台选择不同的文件选择方式\n        let chooseMethod = null;\n\n        // 判断是否包含图片类型\n        const hasImage = opts.allowedTypes.includes('image');\n        // 判断是否包含其他文件类型\n        const hasOtherFiles = opts.allowedTypes.some(type => type !== 'image');\n\n        // #ifdef H5\n        // H5环境需要检测是否在微信浏览器中\n        const isWechatBrowser = /micromessenger/i.test(navigator.userAgent);\n        if (isWechatBrowser) {\n            // 微信浏览器环境，使用特殊处理\n            chooseMethod = chooseFileWechat;\n        } else {\n            // 普通H5环境使用input file\n            chooseMethod = chooseFileH5;\n        }\n        // #endif\n\n        // #ifdef APP-PLUS\n        // APP环境根据文件类型选择方法\n        if (hasImage && !hasOtherFiles) {\n            // 只选择图片\n            chooseMethod = chooseImageApp;\n        } else if (hasOtherFiles) {\n            // 选择文件（包括图片）\n            chooseMethod = chooseFileApp;\n        } else {\n            chooseMethod = chooseImageApp;\n        }\n        // #endif\n\n        // #ifdef MP\n        // 小程序环境\n        if (opts.fileSourceType === 'album') {\n            // 从相册选择只能选择图片（微信小程序限制）\n            chooseMethod = chooseImageMp;\n        } else if (opts.fileSourceType === 'files') {\n            // 从手机文件选择（使用文档选择器）\n            chooseMethod = chooseDocumentMp;\n        } else {\n            // 从聊天记录选择文件（默认行为，支持所有文件类型）\n            chooseMethod = chooseFileMp;\n        }\n        // #endif\n\n        if (!chooseMethod) {\n            const error = new Error('当前平台不支持文件选择');\n            if (opts.onFail) opts.onFail(error);\n            if (opts.onComplete) opts.onComplete(false);\n            reject(error);\n            return;\n        }\n\n        // 执行文件选择\n        chooseMethod(opts)\n            .then(files => {\n                // 文件选择成功，执行选择回调\n                if (opts.onChoose) {\n                    const result = opts.onChoose(files);\n                    if (result === false) {\n                        // 如果回调返回false，取消后续操作\n                        resolve({files, uploaded: false});\n                        return;\n                    }\n                }\n\n                // 如果不自动上传，直接返回文件信息\n                if (!opts.autoUpload) {\n                    resolve({files, uploaded: false});\n                    return;\n                }\n\n                // 自动上传文件\n                uploadFiles(files, opts)\n                    .then(uploadResults => {\n                        resolve({files, uploadResults, uploaded: true});\n                    })\n                    .catch(uploadError => {\n                        if (opts.onFail) opts.onFail(uploadError);\n                        if (opts.onComplete) opts.onComplete(false);\n                        reject(uploadError);\n                    });\n            })\n            .catch(chooseError => {\n                if (opts.onFail) opts.onFail(chooseError);\n                if (opts.onComplete) opts.onComplete(false);\n                reject(chooseError);\n            });\n    });\n}\n\n/**\n * 格式化文件大小\n * @param {Number} size 文件大小（字节）\n * @returns {String} 格式化后的文件大小\n */\nexport function formatFileSize(size) {\n    if (!size || size === 0) return '0 B';\n\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let index = 0;\n    let fileSize = size;\n\n    while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024;\n        index++;\n    }\n\n    return `${fileSize.toFixed(index === 0 ? 0 : 1)} ${units[index]}`;\n}\n\n/**\n * 验证文件类型\n * @param {String} fileName 文件名\n * @param {Array} allowedTypes 允许的文件类型\n * @returns {Boolean} 是否允许\n */\nexport function validateFileType(fileName, allowedTypes = []) {\n    if (!fileName || allowedTypes.length === 0) return true;\n\n    const ext = fileName.toLowerCase().split('.').pop();\n    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', '3gp'];\n    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];\n\n    for (const type of allowedTypes) {\n        if (type === 'image' && imageTypes.includes(ext)) return true;\n        if (type === 'video' && videoTypes.includes(ext)) return true;\n        if (type === 'document' && documentTypes.includes(ext)) return true;\n        if (type === 'file') return true; // 允许所有文件类型\n    }\n\n    return false;\n}\n\n/**\n * 验证文件类型\n * @param {String} fileName 文件名\n * @param {Array} allowedTypes 允许的文件类型\n * @returns {Boolean} 是否允许\n */\nexport function getFileType(fileName) {\n    if (!fileName) return 'file';\n\n    const ext = fileName.toLowerCase().split('.').pop();\n    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', '3gp'];\n    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];\n\n    if (imageTypes.includes(ext)) return 'image';\n    if (videoTypes.includes(ext)) return 'video';\n    if (documentTypes.includes(ext)) return 'document';\n\n    return 'file';\n}\n\n/**\n * 从文件名或路径中提取文件扩展名\n * @param {String} fileName 文件名\n * @param {String} filePath 文件路径（可选）\n * @returns {String|null} 文件扩展名，如果无法获取则返回null\n */\nexport function getFileExtension(fileName, filePath = null) {\n    // 优先从 fileName 获取扩展名\n    if (fileName && fileName.includes('.')) {\n        const ext = fileName.toLowerCase().split('.').pop()\n        return ext\n    }\n\n    // 如果 fileName 没有扩展名，从 filePath 获取\n    if (filePath && filePath.includes('.')) {\n        const ext = filePath.toLowerCase().split('.').pop()\n        return ext\n    }\n\n    return null\n}\n\n/**\n * 验证材料文件类型\n * @param {String} fileName 文件名\n * @param {String} filePath 文件路径（可选）\n * @returns {Boolean} 是否为允许的文件类型\n */\nexport function validateMaterialFileType(fileName, filePath = null) {\n    const ext = getFileExtension(fileName, filePath)\n    if (!ext) return false\n\n    const allowedExtensions = [\n        // 图片格式\n        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',\n        // 文档格式\n        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',\n    ]\n\n    return allowedExtensions.includes(ext)\n}\n\n/**\n * 获取文件类型图标\n * @param {String} fileName 文件名\n * @returns {String} 文件类型图标\n */\nexport function getFileTypeIcon(fileName) {\n    if (!fileName) return '📄';\n\n    const ext = fileName.toLowerCase().split('.').pop();\n\n    // 图片类型\n    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {\n        return '🖼️';\n    }\n\n    // PDF\n    if (ext === 'pdf') {\n        return '📕';\n    }\n\n    // Word文档\n    if (['doc', 'docx'].includes(ext)) {\n        return '📘';\n    }\n\n    // Excel表格\n    if (['xls', 'xlsx'].includes(ext)) {\n        return '📗';\n    }\n\n    // PowerPoint\n    if (['ppt', 'pptx'].includes(ext)) {\n        return '📙';\n    }\n\n    // 其他文件\n    return '📄';\n}\n\n// 微信浏览器环境文件选择\nfunction chooseFileWechat(opts) {\n    return new Promise((resolve, reject) => {\n\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.multiple = opts.count > 1;\n\n        // 微信浏览器对accept属性支持有限，需要简化\n        if (opts.acceptTypes) {\n            input.accept = opts.acceptTypes;\n        } else {\n            // 微信浏览器中，简化accept属性\n            const acceptTypes = [];\n            if (opts.allowedTypes.includes('image')) {\n                acceptTypes.push('image/*');\n            }\n            if (opts.allowedTypes.includes('document') || opts.allowedTypes.includes('file')) {\n                // 微信浏览器中，使用通用的文件类型\n                acceptTypes.push('*/*');\n            }\n            input.accept = acceptTypes.join(',');\n        }\n\n        // 添加样式确保在微信中可见\n        input.style.position = 'absolute';\n        input.style.left = '-9999px';\n        input.style.opacity = '0';\n        input.style.zIndex = '9999';\n        document.body.appendChild(input);\n\n\n        input.onchange = (e) => {\n            const files = Array.from(e.target.files);\n\n            // 验证文件\n            const validFiles = [];\n            for (const file of files) {\n\n                if (file.size > opts.maxSize * 1024 * 1024) {\n                    uni.showToast({\n                        title: `文件 ${file.name} 超过 ${opts.maxSize}MB 限制`,\n                        icon: 'none'\n                    });\n                    continue;\n                }\n\n                // 在微信浏览器中验证文件类型\n                if (!validateWechatFileType(file.name, opts.allowedTypes)) {\n                    uni.showToast({\n                        title: `文件 ${file.name} 格式不支持`,\n                        icon: 'none'\n                    });\n                    continue;\n                }\n\n                validFiles.push({\n                    name: file.name,\n                    size: file.size,\n                    type: file.type,\n                    // #ifdef H5\n                    path: URL.createObjectURL(file),\n                    // #endif\n                    // #ifndef H5\n                    path: file.path || '',\n                    // #endif\n                    file: file\n                });\n            }\n\n            // 清理DOM\n            try {\n                document.body.removeChild(input);\n            } catch (err) {\n                console.warn('清理input元素失败:', err);\n            }\n\n            if (validFiles.length === 0) {\n                reject(new Error('没有有效的文件'));\n                return;\n            }\n\n            resolve(validFiles);\n        };\n\n        // 添加错误处理\n        input.onerror = (err) => {\n            console.error('文件选择错误:', err);\n            try {\n                document.body.removeChild(input);\n            } catch (e) {\n                console.warn('清理input元素失败:', e);\n            }\n            reject(new Error('文件选择失败'));\n        };\n\n        // 触发文件选择\n        setTimeout(() => {\n            try {\n                input.click();\n            } catch (err) {\n                console.error('触发文件选择失败:', err);\n                reject(new Error('无法打开文件选择器'));\n            }\n        }, 100);\n    });\n}\n\n// 验证微信浏览器中的文件类型\nfunction validateWechatFileType(fileName, allowedTypes) {\n    if (!fileName || allowedTypes.length === 0) return true;\n\n    const ext = fileName.toLowerCase().split('.').pop();\n    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx'];\n\n    for (const type of allowedTypes) {\n        if (type === 'image' && imageTypes.includes(ext)) return true;\n        if ((type === 'document' || type === 'file') && documentTypes.includes(ext)) return true;\n    }\n\n    return false;\n}\n\n// H5环境文件选择\nfunction chooseFileH5(opts) {\n    return new Promise((resolve, reject) => {\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.multiple = opts.count > 1;\n\n        // 设置接受的文件类型\n        if (opts.acceptTypes) {\n            // 如果指定了自定义accept类型，直接使用\n            input.accept = opts.acceptTypes;\n        } else {\n            // 否则根据allowedTypes生成accept属性\n            const acceptTypes = [];\n            if (opts.allowedTypes.includes('image')) {\n                acceptTypes.push('image/*');\n            }\n            if (opts.allowedTypes.includes('video')) {\n                acceptTypes.push('video/*');\n            }\n            if (opts.allowedTypes.includes('document')) {\n                // 支持常见的办公文档格式\n                acceptTypes.push('.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx');\n            }\n            if (opts.allowedTypes.includes('file')) {\n                acceptTypes.push('.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar');\n            }\n            input.accept = acceptTypes.join(',');\n        }\n\n        input.onchange = (e) => {\n            const files = Array.from(e.target.files);\n\n            // 验证文件\n            const validFiles = [];\n            for (const file of files) {\n                if (file.size > opts.maxSize * 1024 * 1024) {\n                    toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                    continue;\n                }\n                validFiles.push({\n                    name: file.name,\n                    size: file.size,\n                    type: file.type,\n                    // #ifdef H5\n                    path: URL.createObjectURL(file),\n                    // #endif\n                    // #ifndef H5\n                    path: file.path || '',\n                    // #endif\n                    file: file // H5环境保存原始文件对象\n                });\n            }\n\n            if (validFiles.length === 0) {\n                reject(new Error('没有有效的文件'));\n                return;\n            }\n\n            resolve(validFiles);\n        };\n\n        input.click();\n    });\n}\n\n// APP环境图片选择\nfunction chooseImageApp(opts) {\n    return new Promise((resolve, reject) => {\n        uni.chooseImage({\n            count: opts.count,\n            sizeType: ['compressed'],\n            sourceType: opts.sourceType,\n            success: (res) => {\n                const files = res.tempFilePaths.map((path, index) => {\n                    const file = res.tempFiles[index];\n                    return {\n                        name: file.name || `image_${Date.now()}_${index}.jpg`,\n                        size: file.size || 0,\n                        type: file.type || 'image/jpeg',\n                        path: path\n                    };\n                });\n\n                // 验证文件大小\n                const validFiles = files.filter(file => {\n                    if (file.size > opts.maxSize * 1024 * 1024) {\n                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                        return false;\n                    }\n                    return true;\n                });\n\n                if (validFiles.length === 0) {\n                    reject(new Error('没有有效的文件'));\n                    return;\n                }\n\n                resolve(validFiles);\n            },\n            fail: (err) => {\n                console.error('选择图片失败:', err);\n                reject(new Error('选择图片失败'));\n            }\n        });\n    });\n}\n\n// APP环境文件选择\nfunction chooseFileApp(opts) {\n    return new Promise((resolve, reject) => {\n        // APP环境使用 chooseMessageFile 选择文件\n        uni.chooseMessageFile({\n            count: opts.count,\n            type: 'all',\n            success: (res) => {\n                const files = res.tempFiles.map(file => ({\n                    name: file.name,\n                    size: file.size,\n                    type: file.type || '',\n                    path: file.path\n                }));\n\n                // 验证文件大小\n                const validFiles = files.filter(file => {\n                    if (file.size > opts.maxSize * 1024 * 1024) {\n                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                        return false;\n                    }\n                    return true;\n                });\n\n                if (validFiles.length === 0) {\n                    reject(new Error('没有有效的文件'));\n                    return;\n                }\n\n                resolve(validFiles);\n            },\n            fail: (err) => {\n                console.error('选择文件失败:', err);\n                reject(new Error('选择文件失败'));\n            }\n        });\n    });\n}\n\n// 小程序环境图片选择\nfunction chooseImageMp(opts) {\n    return new Promise((resolve, reject) => {\n        // 根据文件来源类型决定sourceType\n        let sourceType = opts.sourceType;\n        if (opts.fileSourceType === 'album') {\n            sourceType = ['album']; // 只从相册选择\n        } else if (opts.fileSourceType === 'chat') {\n            sourceType = ['album', 'camera']; // 默认行为\n        }\n\n        uni.chooseImage({\n            count: opts.count,\n            sizeType: ['compressed'],\n            sourceType: sourceType,\n            success: (res) => {\n                const files = res.tempFilePaths.map((path, index) => {\n                    const file = res.tempFiles[index];\n                    return {\n                        name: file.name || `image_${Date.now()}_${index}.jpg`,\n                        size: file.size || 0,\n                        type: file.type || 'image/jpeg',\n                        path: path\n                    };\n                });\n\n                // 验证文件大小\n                const validFiles = files.filter(file => {\n                    if (file.size > opts.maxSize * 1024 * 1024) {\n                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                        return false;\n                    }\n                    return true;\n                });\n\n                if (validFiles.length === 0) {\n                    reject(new Error('没有有效的文件'));\n                    return;\n                }\n\n                resolve(validFiles);\n            },\n            fail: (err) => {\n                console.error('选择图片失败:', err);\n                reject(new Error('选择图片失败'));\n            }\n        });\n    });\n}\n\n// 小程序环境文件选择（从聊天记录）\nfunction chooseFileMp(opts) {\n    return new Promise((resolve, reject) => {\n        // 小程序环境使用 chooseMessageFile\n        uni.chooseMessageFile({\n            count: opts.count,\n            type: 'all',\n            success: (res) => {\n                const files = res.tempFiles.map(file => ({\n                    name: file.name,\n                    size: file.size,\n                    type: file.type || '',\n                    path: file.path\n                }));\n\n                // 验证文件大小\n                const validFiles = files.filter(file => {\n                    if (file.size > opts.maxSize * 1024 * 1024) {\n                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                        return false;\n                    }\n                    return true;\n                });\n\n                if (validFiles.length === 0) {\n                    reject(new Error('没有有效的文件'));\n                    return;\n                }\n\n                resolve(validFiles);\n            },\n            fail: (err) => {\n                console.error('选择文件失败:', err);\n                reject(new Error('选择文件失败'));\n            }\n        });\n    });\n}\n\n// 小程序环境文档选择（从手机文件系统）\nfunction chooseDocumentMp(opts) {\n    return new Promise((resolve, reject) => {\n        // #ifdef MP-WEIXIN\n        // 微信小程序尝试使用 uni.chooseFile（如果支持）\n        if (typeof uni.chooseFile === 'function') {\n            uni.chooseFile({\n                count: opts.count,\n                type: 'file',\n                success: (res) => {\n                    const files = res.tempFiles.map(file => ({\n                        name: file.name,\n                        size: file.size,\n                        type: file.type || '',\n                        path: file.path\n                    }));\n\n                    // 验证文件大小\n                    const validFiles = files.filter(file => {\n                        if (file.size > opts.maxSize * 1024 * 1024) {\n                            toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                            return false;\n                        }\n                        return true;\n                    });\n\n                    if (validFiles.length === 0) {\n                        reject(new Error('没有有效的文件'));\n                        return;\n                    }\n\n                    resolve(validFiles);\n                },\n                fail: (err) => {\n                    console.error('chooseFile失败，回退到chooseImage:', err);\n                    // 回退到选择图片\n                    fallbackToChooseImage(opts, resolve, reject);\n                }\n            });\n        } else {\n            // 如果不支持 chooseFile，直接使用图片选择\n            fallbackToChooseImage(opts, resolve, reject);\n        }\n        // #endif\n\n        // #ifndef MP-WEIXIN\n        // 其他小程序平台回退到图片选择\n        fallbackToChooseImage(opts, resolve, reject);\n        // #endif\n    });\n}\n\n// 回退到图片选择的辅助函数\nfunction fallbackToChooseImage(opts, resolve, reject) {\n    uni.chooseImage({\n        count: opts.count,\n        sizeType: ['compressed', 'original'],\n        sourceType: ['album'],\n        success: (res) => {\n            const files = res.tempFilePaths.map((path, index) => {\n                const file = res.tempFiles[index];\n                return {\n                    name: file.name || `image_${Date.now()}_${index}.jpg`,\n                    size: file.size || 0,\n                    type: file.type || 'image/jpeg',\n                    path: path\n                };\n            });\n\n            // 验证文件大小\n            const validFiles = files.filter(file => {\n                if (file.size > opts.maxSize * 1024 * 1024) {\n                    toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);\n                    return false;\n                }\n                return true;\n            });\n\n            if (validFiles.length === 0) {\n                reject(new Error('没有有效的文件'));\n                return;\n            }\n\n            resolve(validFiles);\n        },\n        fail: (err) => {\n            console.error('选择图片失败:', err);\n            reject(new Error('选择文件失败'));\n        }\n    });\n}\n\n// 上传文件\nfunction uploadFiles(files, opts) {\n    return new Promise((resolve, reject) => {\n        if (!files || files.length === 0) {\n            reject(new Error('没有文件需要上传'));\n            return;\n        }\n\n        const uploadPromises = files.map((file, index) => {\n            return uploadSingleFile(file, opts, index);\n        });\n\n        Promise.all(uploadPromises)\n            .then(results => {\n                if (opts.onSuccess) opts.onSuccess(results);\n                if (opts.onComplete) opts.onComplete(true);\n                resolve(results);\n            })\n            .catch(error => {\n                if (opts.onFail) opts.onFail(error);\n                if (opts.onComplete) opts.onComplete(false);\n                reject(error);\n            });\n    });\n}\n\n// 上传单个文件\nfunction uploadSingleFile(file, opts, index) {\n    return new Promise((resolve, reject) => {\n        if (opts.showLoading) {\n            uni.showLoading({\n                title: `上传中... (${index + 1})`\n            });\n        }\n\n        // 准备上传参数\n        const uploadOptions = {\n            url: opts.uploadUrl,\n            filePath: file.path,\n            name: 'file',\n            formData: opts.uploadData,\n            success: (res) => {\n                if (opts.showLoading) {\n                    uni.hideLoading();\n                }\n\n                if (res.code === 200 && res.data) {\n                    // 上传成功\n                    const result = {\n                        ...file,\n                        ...res.data,\n                        uploaded: true,\n                        uploadTime: new Date().getTime()\n                    };\n\n                    if (opts.onProgress) {\n                        opts.onProgress({\n                            file: result,\n                            index,\n                            progress: 100,\n                            status: 'success'\n                        });\n                    }\n\n                    resolve(result);\n                } else {\n                    // 上传失败\n                    const error = new Error(res.msg || '上传失败');\n                    error.file = file;\n                    error.index = index;\n\n                    if (opts.onProgress) {\n                        opts.onProgress({\n                            file,\n                            index,\n                            progress: 0,\n                            status: 'error',\n                            error\n                        });\n                    }\n\n                    reject(error);\n                }\n            },\n            fail: (err) => {\n                if (opts.showLoading) {\n                    uni.hideLoading();\n                }\n\n                console.error('上传文件失败:', err);\n                const error = new Error('上传失败');\n                error.file = file;\n                error.index = index;\n                error.originalError = err;\n\n                if (opts.onProgress) {\n                    opts.onProgress({\n                        file,\n                        index,\n                        progress: 0,\n                        status: 'error',\n                        error\n                    });\n                }\n\n                reject(error);\n            }\n        };\n\n        // 执行上传\n        const uploadTask = upload(uploadOptions);\n\n        // 监听上传进度\n        if (uploadTask && uploadTask.onProgressUpdate && opts.onProgress) {\n            uploadTask.onProgressUpdate((progressRes) => {\n                opts.onProgress({\n                    file,\n                    index,\n                    progress: progressRes.progress,\n                    status: 'uploading'\n                });\n            });\n        }\n    });\n}\n\n\n/**\n * 下载并打开文件\n * @param {String} fileUrl 文件的完整URL\n * @param {Object} options 配置选项\n * @param {Function} options.onSuccess 下载成功回调\n * @param {Function} options.onFail 下载失败回调\n * @param {Function} options.onComplete 完成回调（无论成功失败）\n * @param {String} options.loadingText 加载提示文字\n * @param {String} options.successText 成功提示文字\n * @param {String} options.failText 失败提示文字\n * @param {Boolean} options.autoOpen 是否自动打开文件\n * @param {String} options.fileName 文件名称，H5环境下使用\n * @returns {Promise<Object>} 下载结果\n */\nexport function downloadAndOpenFile(fileUrl, options = {}) {\n    // 默认选项\n    const defaultOptions = {\n        onSuccess: null,\n        onFail: null,\n        onComplete: null,\n        loadingText: '文件下载中...',\n        successText: '文件已保存',\n        failText: '文件下载失败',\n        autoOpen: true,\n        fileName: ''\n    };\n\n    // 合并选项\n    const opts = { ...defaultOptions, ...options };\n\n    return new Promise((resolve, reject) => {\n        // 验证URL\n        if (!fileUrl) {\n            const error = new Error('没有可下载的文件');\n            if (opts.onFail) opts.onFail(error);\n            if (opts.onComplete) opts.onComplete(false);\n            reject(error);\n            return;\n        }\n\n        // 判断当前环境\n        // #ifdef H5\n        // H5环境下检测是否在微信浏览器中\n        const isWechatBrowser = /micromessenger/i.test(navigator.userAgent);\n\n        if (isWechatBrowser) {\n            // 微信浏览器环境，直接弹出浏览器下载选项\n            try {\n\n                // 直接使用window.open触发下载，让浏览器处理\n                window.open(fileUrl, '_blank');\n\n                // 微信浏览器中不显示成功提示，让用户看到浏览器的下载选项\n                if (opts.onSuccess) opts.onSuccess({});\n                if (opts.onComplete) opts.onComplete(true);\n                resolve({});\n            } catch (err) {\n                console.error('微信浏览器下载文件失败', err);\n                toast.show(opts.failText);\n\n                if (opts.onFail) opts.onFail(err);\n                if (opts.onComplete) opts.onComplete(false);\n                reject(err);\n            }\n        } else {\n            // 普通H5环境使用浏览器原生下载功能\n            try {\n                uni.showLoading(opts.loadingText);\n\n                // 从URL中提取文件名\n                let fileName = opts.fileName || '';\n                if (!fileName) {\n                    const urlParts = fileUrl.split('/');\n                    fileName = urlParts[urlParts.length - 1];\n                    // 处理URL编码\n                    fileName = decodeURIComponent(fileName);\n                }\n\n                // 创建一个隐藏的a标签来下载文件\n                const a = document.createElement('a');\n                a.style.display = 'none';\n                a.href = fileUrl;\n                a.download = fileName;\n                a.target = '_blank';\n\n                // 添加到DOM并触发点击\n                document.body.appendChild(a);\n                a.click();\n\n                // 清理DOM\n                setTimeout(() => {\n                    document.body.removeChild(a);\n                    uni.hideLoading();\n\n                    if (opts.successText) {\n                        toast.show(opts.successText);\n                    }\n\n                    if (opts.onSuccess) opts.onSuccess({});\n                    if (opts.onComplete) opts.onComplete(true);\n                    resolve({});\n                }, 100);\n            } catch (err) {\n                uni.hideLoading();\n                console.error('H5环境下载文件失败', err);\n                toast.show(opts.failText);\n\n                if (opts.onFail) opts.onFail(err);\n                if (opts.onComplete) opts.onComplete(false);\n                reject(err);\n            }\n        }\n        // #endif\n\n        // #ifndef H5\n        // 非H5环境使用uni API\n        uni.showLoading(opts.loadingText);\n\n        // 下载文件\n        uni.downloadFile({\n            url: fileUrl,\n            success: (res) => {\n                if (res.statusCode === 200) {\n                    // 保存文件\n                    uni.saveFile({\n                        tempFilePath: res.tempFilePath,\n                        success: (saveRes) => {\n                            uni.hideLoading();\n\n                            // 显示成功提示\n                            if (opts.successText) {\n                                toast.show(opts.successText)\n                            }\n\n                            // 自动打开文件\n                            if (opts.autoOpen) {\n                                uni.openDocument({\n                                    filePath: saveRes.savedFilePath,\n                                    success: () => {\n                                        if (opts.onSuccess) opts.onSuccess(saveRes);\n                                        if (opts.onComplete) opts.onComplete(true);\n                                        resolve(saveRes);\n                                    },\n                                    fail: (openErr) => {\n                                        toast.show('无法打开此类型文件')\n                                        if (opts.onSuccess) opts.onSuccess(saveRes);\n                                        if (opts.onComplete) opts.onComplete(true);\n                                        resolve(saveRes); // 文件保存成功但打开失败，仍然视为成功\n                                    }\n                                });\n                            } else {\n                                if (opts.onSuccess) opts.onSuccess(saveRes);\n                                if (opts.onComplete) opts.onComplete(true);\n                                resolve(saveRes);\n                            }\n                        },\n                        fail: (saveErr) => {\n                            uni.hideLoading();\n\n                            toast.show('文件保存失败')\n\n                            if (opts.onFail) opts.onFail(saveErr);\n                            if (opts.onComplete) opts.onComplete(false);\n                            reject(saveErr);\n                        }\n                    });\n                } else {\n                    uni.hideLoading();\n                    console.error('文件下载失败', res);\n\n                    toast.show(opts.failText)\n\n                    if (opts.onFail) opts.onFail(res);\n                    if (opts.onComplete) opts.onComplete(false);\n                    reject(new Error(opts.failText));\n                }\n            },\n            fail: (err) => {\n                uni.hideLoading();\n                console.error('文件下载失败', err);\n\n                toast.show(opts.failText)\n\n                if (opts.onFail) opts.onFail(err);\n                if (opts.onComplete) opts.onComplete(false);\n                reject(err);\n            }\n        });\n        // #endif\n    });\n}\n"], "names": ["chooseImageMp", "opts", "Promise", "resolve", "reject", "sourceType", "fileSourceType", "uni", "chooseImage", "count", "sizeType", "success", "res", "validFiles", "tempFilePaths", "map", "path", "index", "file", "tempFiles", "name", "Date", "now", "size", "type", "filter", "maxSize", "toast", "show", "length", "Error", "fail", "err", "__f__", "chooseFileMp", "chooseMessageFile", "chooseDocumentMp", "chooseFile", "fallbackToChooseImage", "options", "allowedTypes", "acceptTypes", "uploadData", "fileBizType", "uploadUrl", "onChoose", "onProgress", "onSuccess", "onFail", "onComplete", "autoUpload", "showLoading", "<PERSON><PERSON><PERSON><PERSON>", "includes", "some", "error", "then", "files", "uploaded", "uploadPromises", "title", "uploadOptions", "url", "filePath", "formData", "hideLoading", "code", "data", "result", "uploadTime", "getTime", "progress", "status", "msg", "originalError", "uploadTask", "upload", "onProgressUpdate", "progressRes", "uploadSingleFile", "all", "results", "catch", "uploadFiles", "uploadResults", "uploadError", "chooseError", "fileUrl", "loadingText", "successText", "failText", "autoOpen", "fileName", "downloadFile", "statusCode", "saveFile", "tempFile<PERSON>ath", "saveRes", "openDocument", "savedFilePath", "openErr", "common_toast", "saveErr", "ext", "toLowerCase", "split", "pop", "getFileExtension"], "mappings": "8FAojBA,SAASA,EAAcC,GACnB,OAAO,IAAIC,SAAQ,CAACC,EAASC,KAEzB,IAAIC,EAAaJ,EAAKI,WACM,UAAxBJ,EAAKK,eACLD,EAAa,CAAC,SACiB,SAAxBJ,EAAKK,iBACCD,EAAA,CAAC,QAAS,WAG3BE,EAAAA,MAAIC,YAAY,CACZC,MAAOR,EAAKQ,MACZC,SAAU,CAAC,cACXL,aACAM,QAAUC,IACN,MAWMC,EAXQD,EAAIE,cAAcC,KAAI,CAACC,EAAMC,KACjC,MAAAC,EAAON,EAAIO,UAAUF,GACpB,MAAA,CACHG,KAAMF,EAAKE,MAAQ,SAASC,KAAKC,SAASL,QAC1CM,KAAML,EAAKK,MAAQ,EACnBC,KAAMN,EAAKM,MAAQ,aACnBR,WAKiBS,QAAeP,KAChCA,EAAKK,KAAsB,KAAftB,EAAKyB,QAAiB,QAClCC,EAAAA,MAAMC,KAAK,MAAMV,EAAKE,WAAWnB,EAAKyB,iBAC/B,KAKW,IAAtBb,EAAWgB,OAKf1B,EAAQU,GAJGT,EAAA,IAAI0B,MAAM,aAMzBC,KAAOC,IACHzB,EAAcU,MAAAgB,MAAA,QAAA,6BAAA,UAAWD,GAClB5B,EAAA,IAAI0B,MAAM,gBAIjC,CAGA,SAASI,EAAajC,GAClB,OAAO,IAAIC,SAAQ,CAACC,EAASC,KAEzBG,EAAAA,MAAI4B,kBAAkB,CAClB1B,MAAOR,EAAKQ,MACZe,KAAM,MACNb,QAAUC,IACN,MAQMC,EARQD,EAAIO,UAAUJ,KAAaG,IAAA,CACrCE,KAAMF,EAAKE,KACXG,KAAML,EAAKK,KACXC,KAAMN,EAAKM,MAAQ,GACnBR,KAAME,EAAKF,SAIUS,QAAeP,KAChCA,EAAKK,KAAsB,KAAftB,EAAKyB,QAAiB,QAClCC,EAAAA,MAAMC,KAAK,MAAMV,EAAKE,WAAWnB,EAAKyB,iBAC/B,KAKW,IAAtBb,EAAWgB,OAKf1B,EAAQU,GAJGT,EAAA,IAAI0B,MAAM,aAMzBC,KAAOC,IACHzB,EAAcU,MAAAgB,MAAA,QAAA,6BAAA,UAAWD,GAClB5B,EAAA,IAAI0B,MAAM,gBAIjC,CAGA,SAASM,EAAiBnC,GACtB,OAAO,IAAIC,SAAQ,CAACC,EAASC,KAGK,mBAAnBG,EAAAA,MAAI8B,WACX9B,EAAAA,MAAI8B,WAAW,CACX5B,MAAOR,EAAKQ,MACZe,KAAM,OACNb,QAAUC,IACN,MAQMC,EARQD,EAAIO,UAAUJ,KAAaG,IAAA,CACrCE,KAAMF,EAAKE,KACXG,KAAML,EAAKK,KACXC,KAAMN,EAAKM,MAAQ,GACnBR,KAAME,EAAKF,SAIUS,QAAeP,KAChCA,EAAKK,KAAsB,KAAftB,EAAKyB,QAAiB,QAClCC,EAAAA,MAAMC,KAAK,MAAMV,EAAKE,WAAWnB,EAAKyB,iBAC/B,KAKW,IAAtBb,EAAWgB,OAKf1B,EAAQU,GAJGT,EAAA,IAAI0B,MAAM,aAMzBC,KAAOC,IACHzB,EAAAU,MAAAgB,MAAA,QAAA,6BAAc,+BAAgCD,GAExBM,EAAArC,EAAME,EAASC,MAKvBkC,EAAArC,EAAME,EAASC,KASjD,CAGA,SAASkC,EAAsBrC,EAAME,EAASC,GAC1CG,EAAAA,MAAIC,YAAY,CACZC,MAAOR,EAAKQ,MACZC,SAAU,CAAC,aAAc,YACzBL,WAAY,CAAC,SACbM,QAAUC,IACN,MAWMC,EAXQD,EAAIE,cAAcC,KAAI,CAACC,EAAMC,KACjC,MAAAC,EAAON,EAAIO,UAAUF,GACpB,MAAA,CACHG,KAAMF,EAAKE,MAAQ,SAASC,KAAKC,SAASL,QAC1CM,KAAML,EAAKK,MAAQ,EACnBC,KAAMN,EAAKM,MAAQ,aACnBR,WAKiBS,QAAeP,KAChCA,EAAKK,KAAsB,KAAftB,EAAKyB,QAAiB,QAClCC,EAAAA,MAAMC,KAAK,MAAMV,EAAKE,WAAWnB,EAAKyB,iBAC/B,KAKW,IAAtBb,EAAWgB,OAKf1B,EAAQU,GAJGT,EAAA,IAAI0B,MAAM,aAMzBC,KAAOC,IACHzB,EAAcU,MAAAgB,MAAA,QAAA,6BAAA,UAAWD,GAClB5B,EAAA,IAAI0B,MAAM,aAG7B,6BAzsBO,SAA6BS,EAAU,IAE1C,MAmBMtC,EAAO,CAlBTQ,MAAO,EACPiB,QAAS,GACTc,aAAc,CAAC,QAAS,QACxBC,YAAa,KACbpC,WAAY,CAAC,QAAS,UACtBC,eAAgB,OAChBoC,WAAY,CAACC,YAAa,sBAC1BC,UAAW,kCACXC,SAAU,KACVC,WAAY,KACZC,UAAW,KACXC,OAAQ,KACRC,WAAY,KACZC,YAAY,EACZC,aAAa,KAImBZ,GAEpC,OAAO,IAAIrC,SAAQ,CAACC,EAASC,KAEzB,IAAIgD,EAAe,KA8CnB,GA3CsBnD,EAAAuC,aAAaa,SAAS,SAEtBpD,EAAKuC,aAAac,MAAa9B,GAAS,UAATA,IA+BlC4B,EAFS,UAAxBnD,EAAKK,eAEUN,EACgB,UAAxBC,EAAKK,eAEG8B,EAGAF,GAIdkB,EAAc,CACT,MAAAG,EAAQ,IAAIzB,MAAM,eAIxB,OAHI7B,EAAK+C,QAAQ/C,EAAK+C,OAAOO,GACzBtD,EAAKgD,YAAYhD,EAAKgD,YAAW,QACrC7C,EAAOmD,EAEV,CAGYH,EAAAnD,GACRuD,MAAcC,IAEX,GAAIxD,EAAK4C,SAAU,CAEf,IAAe,IADA5C,EAAK4C,SAASY,GAIzB,YADAtD,EAAQ,CAACsD,QAAOC,UAAU,GAGjC,CAGIzD,EAAKiD,WA+mB1B,SAAqBO,EAAOxD,GACxB,OAAO,IAAIC,SAAQ,CAACC,EAASC,KACzB,IAAKqD,GAA0B,IAAjBA,EAAM5B,OAEhB,YADOzB,EAAA,IAAI0B,MAAM,aAIrB,MAAM6B,EAAiBF,EAAM1C,KAAI,CAACG,EAAMD,IAmBhD,SAA0BC,EAAMjB,EAAMgB,GAClC,OAAO,IAAIf,SAAQ,CAACC,EAASC,KACrBH,EAAKkD,aACL5C,EAAAA,MAAI4C,YAAY,CACZS,MAAO,WAAW3C,EAAQ,OAKlC,MAAM4C,EAAgB,CAClBC,IAAK7D,EAAK2C,UACVmB,SAAU7C,EAAKF,KACfI,KAAM,OACN4C,SAAU/D,EAAKyC,WACf/B,QAAUC,IAKN,GAJIX,EAAKkD,aACL5C,EAAGU,MAACgD,cAGS,MAAbrD,EAAIsD,MAAgBtD,EAAIuD,KAAM,CAE9B,MAAMC,EAAS,IACRlD,KACAN,EAAIuD,KACPT,UAAU,EACVW,YAAY,IAAIhD,MAAOiD,WAGvBrE,EAAK6C,YACL7C,EAAK6C,WAAW,CACZ5B,KAAMkD,EACNnD,QACAsD,SAAU,IACVC,OAAQ,YAIhBrE,EAAQiE,EAC5B,KAAuB,CAEH,MAAMb,EAAQ,IAAIzB,MAAMlB,EAAI6D,KAAO,QACnClB,EAAMrC,KAAOA,EACbqC,EAAMtC,MAAQA,EAEVhB,EAAK6C,YACL7C,EAAK6C,WAAW,CACZ5B,OACAD,QACAsD,SAAU,EACVC,OAAQ,QACRjB,UAIRnD,EAAOmD,EACV,GAELxB,KAAOC,IACC/B,EAAKkD,aACL5C,EAAGU,MAACgD,cAGR1D,EAAcU,MAAAgB,MAAA,QAAA,6BAAA,UAAWD,GACnB,MAAAuB,EAAQ,IAAIzB,MAAM,QACxByB,EAAMrC,KAAOA,EACbqC,EAAMtC,MAAQA,EACdsC,EAAMmB,cAAgB1C,EAElB/B,EAAK6C,YACL7C,EAAK6C,WAAW,CACZ5B,OACAD,QACAsD,SAAU,EACVC,OAAQ,QACRjB,UAIRnD,EAAOmD,KAKToB,EAAaC,SAAOf,GAGtBc,GAAcA,EAAWE,kBAAoB5E,EAAK6C,YACvC6B,EAAAE,kBAAkBC,IACzB7E,EAAK6C,WAAW,CACZ5B,OACAD,QACAsD,SAAUO,EAAYP,SACtBC,OAAQ,mBAK5B,CAnHmBO,CAAiB7D,EAAMjB,EAAMgB,KAGxCf,QAAQ8E,IAAIrB,GACPH,MAAgByB,IACThF,EAAK8C,WAAW9C,EAAK8C,UAAUkC,GAC/BhF,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC9C,EAAQ8E,MAEXC,OAAe3B,IACRtD,EAAK+C,QAAQ/C,EAAK+C,OAAOO,GACzBtD,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC7C,EAAOmD,QAGvB,CAhoBgB4B,CAAY1B,EAAOxD,GACduD,MAAsB4B,IACnBjF,EAAQ,CAACsD,QAAO2B,gBAAe1B,UAAU,OAE5CwB,OAAqBG,IACdpF,EAAK+C,QAAQ/C,EAAK+C,OAAOqC,GACzBpF,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC7C,EAAOiF,MAZXlF,EAAQ,CAACsD,QAAOC,UAAU,OAejCwB,OAAqBI,IACdrF,EAAK+C,QAAQ/C,EAAK+C,OAAOsC,GACzBrF,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC7C,EAAOkF,QAGvB,8BAquBO,SAA6BC,EAAShD,EAAU,IAEnD,MAYMtC,EAAO,CAXT8C,UAAW,KACXC,OAAQ,KACRC,WAAY,KACZuC,YAAa,WACbC,YAAa,QACbC,SAAU,SACVC,UAAU,EACVC,SAAU,MAIuBrD,GAErC,OAAO,IAAIrC,SAAQ,CAACC,EAASC,KAEzB,IAAKmF,EAAS,CACJ,MAAAhC,EAAQ,IAAIzB,MAAM,YAIxB,OAHI7B,EAAK+C,QAAQ/C,EAAK+C,OAAOO,GACzBtD,EAAKgD,YAAYhD,EAAKgD,YAAW,QACrC7C,EAAOmD,EAEV,CA8EDhD,EAAAA,MAAI4C,YAAYlD,EAAKuF,aAGrBjF,EAAAA,MAAIsF,aAAa,CACb/B,IAAKyB,EACL5E,QAAUC,IACiB,MAAnBA,EAAIkF,WAEJvF,EAAAA,MAAIwF,SAAS,CACTC,aAAcpF,EAAIoF,aAClBrF,QAAUsF,IACN1F,EAAGU,MAACgD,cAGAhE,EAAKwF,qBACC7D,KAAK3B,EAAKwF,aAIhBxF,EAAK0F,SACLpF,EAAAA,MAAI2F,aAAa,CACbnC,SAAUkC,EAAQE,cAClBxF,QAAS,KACDV,EAAK8C,WAAW9C,EAAK8C,UAAUkD,GAC/BhG,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC9C,EAAQ8F,IAEZlE,KAAOqE,IACEC,EAAA1E,MAACC,KAAK,aACP3B,EAAK8C,WAAW9C,EAAK8C,UAAUkD,GAC/BhG,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC9C,EAAQ8F,OAIZhG,EAAK8C,WAAW9C,EAAK8C,UAAUkD,GAC/BhG,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC9C,EAAQ8F,KAGhBlE,KAAOuE,IACH/F,EAAGU,MAACgD,cAECoC,EAAA1E,MAACC,KAAK,UAEP3B,EAAK+C,QAAQ/C,EAAK+C,OAAOsD,GACzBrG,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC7C,EAAOkG,OAIf/F,EAAGU,MAACgD,cACJ1D,EAAcU,MAAAgB,MAAA,QAAA,8BAAA,SAAUrB,WAElBgB,KAAK3B,EAAKyF,UAEZzF,EAAK+C,QAAQ/C,EAAK+C,OAAOpC,GACzBX,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC7C,EAAO,IAAI0B,MAAM7B,EAAKyF,aAG9B3D,KAAOC,IACHzB,EAAGU,MAACgD,cACJ1D,EAAcU,MAAAgB,MAAA,QAAA,8BAAA,SAAUD,WAElBJ,KAAK3B,EAAKyF,UAEZzF,EAAK+C,QAAQ/C,EAAK+C,OAAOhB,GACzB/B,EAAKgD,YAAYhD,EAAKgD,YAAW,GACrC7C,EAAO4B,QAKvB,sBAj2BO,SAAqB4D,GACxB,IAAKA,EAAiB,MAAA,OAEtB,MAAMW,EAAMX,EAASY,cAAcC,MAAM,KAAKC,MAK1C,MAJe,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,QAIzCrD,SAASkD,GAAa,QAHlB,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAIxClD,SAASkD,GAAa,QAHf,CAAC,MAAO,MAAO,OAAQ,MAAO,OAAQ,MAAO,OAAQ,OAIzDlD,SAASkD,GAAa,WAEjC,MACX,0BAiDO,SAAyBX,GAC5B,IAAKA,EAAiB,MAAA,KAEtB,MAAMW,EAAMX,EAASY,cAAcC,MAAM,KAAKC,MAG1C,MAAA,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,QAAQrD,SAASkD,GAC/C,MAIC,QAARA,EACO,KAIP,CAAC,MAAO,QAAQlD,SAASkD,GAClB,KAIP,CAAC,MAAO,QAAQlD,SAASkD,GAClB,KAIP,CAAC,MAAO,QAAQlD,SAASkD,GAClB,KAIJ,IACX,mCAnDO,SAAkCX,EAAU7B,EAAW,MACpD,MAAAwC,EAvBH,SAA0BX,EAAU7B,EAAW,MAElD,GAAI6B,GAAYA,EAASvC,SAAS,KAEvB,OADKuC,EAASY,cAAcC,MAAM,KAAKC,MAKlD,GAAI3C,GAAYA,EAASV,SAAS,KAEvB,OADKU,EAASyC,cAAcC,MAAM,KAAKC,MAI3C,OAAA,IACX,CASgBC,CAAiBf,EAAU7B,GACvC,QAAKwC,GAEqB,CAEtB,MAAO,OAAQ,MAAO,MAAO,MAAO,OAEpC,MAAO,MAAO,OAAQ,MAAO,OAAQ,MAAO,QAGvBlD,SAASkD,EACtC"}